{"version": 3, "file": "tracking.service.js", "sourceRoot": "", "sources": ["../../../src/common/services/tracking.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,+CAA+C;AAC/C,uCAAwC;AACxC,kEAA0E;AAC1E,4EAG2C;AAC3C,8EAG4C;AA2BrC,IAAM,eAAe,uBAArB,MAAM,eAAe;IAG1B,YAC8B,aAA8C,EAE1E,iBAAsD,EAEtD,kBAAwD;QAJpB,kBAAa,GAAb,aAAa,CAAyB;QAElE,sBAAiB,GAAjB,iBAAiB,CAA6B;QAE9C,uBAAkB,GAAlB,kBAAkB,CAA8B;QAPzC,WAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;IAQxD,CAAC;IAKJ,KAAK,CAAC,aAAa,CAAC,gBAAkC;QACpD,IAAI,CAAC;YAEH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACrD,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;YACD,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtD,MAAM,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;YACrC,CAAC;YACD,IACE,gBAAgB,CAAC,SAAS;gBAC1B,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,gBAAgB,CAAC,SAAS,CAAC,EACnD,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAGD,MAAM,YAAY,GAAQ;gBACxB,MAAM,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC,MAAM,CAAC;gBACnD,OAAO,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC,OAAO,CAAC;gBACrD,cAAc,EAAE,gBAAgB,CAAC,cAAc;gBAC/C,YAAY,EAAE,IAAI,IAAI,EAAE;aACzB,CAAC;YAEF,IAAI,gBAAgB,CAAC,SAAS,EAAE,CAAC;gBAC/B,YAAY,CAAC,SAAS,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,gBAAgB,CAAC,SAAS,CAAC,CAAC;YAC1E,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,CAAC;YAEtD,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEtB,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,0BAA0B,gBAAgB,CAAC,MAAM,qBAAqB,gBAAgB,CAAC,OAAO,OAAO,gBAAgB,CAAC,cAAc,SAAS,CAC9I,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,6BAA6B,KAAK,CAAC,OAAO,EAAE,EAC5C,KAAK,CAAC,KAAK,CACZ,CAAC;QAEJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,uBAAgD;QAEhD,IAAI,CAAC;YAEH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC5D,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;YACD,IACE,uBAAuB,CAAC,SAAS;gBACjC,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,uBAAuB,CAAC,SAAS,CAAC,EAC1D,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAGD,MAAM,YAAY,GAAQ;gBACxB,MAAM,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,uBAAuB,CAAC,MAAM,CAAC;gBAC1D,YAAY,EAAE,kBAAkB;gBAChC,eAAe,EAAE;oBACf,OAAO,EAAE,uBAAuB,CAAC,OAAO;oBACxC,SAAS,EAAE,uBAAuB,CAAC,SAAS;iBAC7C;gBACD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,uBAAuB,CAAC,SAAS;aAC7C,CAAC;YAEF,IAAI,uBAAuB,CAAC,SAAS,EAAE,CAAC;gBACtC,YAAY,CAAC,SAAS,GAAG,IAAI,gBAAK,CAAC,QAAQ,CACzC,uBAAuB,CAAC,SAAS,CAClC,CAAC;YACJ,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAE1D,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEtB,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,kCAAkC,uBAAuB,CAAC,MAAM,oBAAoB,uBAAuB,CAAC,OAAO,EAAE,CACtH,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,qCAAqC,KAAK,CAAC,OAAO,EAAE,EACpD,KAAK,CAAC,KAAK,CACZ,CAAC;QAEJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,iBAAiB,CACrB,oBAA0C;QAE1C,IAAI,CAAC;YAEH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,oBAAoB,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzD,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,CAAC;YACpC,CAAC;YACD,IACE,oBAAoB,CAAC,SAAS;gBAC9B,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,oBAAoB,CAAC,SAAS,CAAC,EACvD,CAAC;gBACD,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAGD,MAAM,YAAY,GAAQ;gBACxB,MAAM,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,oBAAoB,CAAC,MAAM,CAAC;gBACvD,YAAY,EAAE,oBAAoB,CAAC,YAAY;gBAC/C,eAAe,EAAE,oBAAoB,CAAC,eAAe;gBACrD,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,SAAS,EAAE,oBAAoB,CAAC,SAAS;aAC1C,CAAC;YAEF,IAAI,oBAAoB,CAAC,SAAS,EAAE,CAAC;gBACnC,YAAY,CAAC,SAAS,GAAG,IAAI,gBAAK,CAAC,QAAQ,CACzC,oBAAoB,CAAC,SAAS,CAC/B,CAAC;YACJ,CAAC;YAED,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;YAE1D,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;YAEtB,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,+BAA+B,oBAAoB,CAAC,MAAM,cAAc,oBAAoB,CAAC,YAAY,EAAE,CAC5G,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,kCAAkC,KAAK,CAAC,OAAO,EAAE,EACjD,KAAK,CAAC,KAAK,CACZ,CAAC;QAEJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,uBAAuB,CAC3B,SAAiB,EACjB,UAII,EAAE;QAEN,IAAI,CAAC;YACH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAED,MAAM,UAAU,GAAQ;gBACtB,MAAM,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;aACtC,CAAC;YAGF,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzC,UAAU,CAAC,YAAY,GAAG,EAAE,CAAC;gBAC7B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;oBACtB,UAAU,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;gBACnD,CAAC;gBACD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;oBACpB,UAAU,CAAC,YAAY,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;gBACjD,CAAC;YACH,CAAC;YAED,MAAM,QAAQ,GAAU;gBACtB,EAAE,MAAM,EAAE,UAAU,EAAE;gBACtB;oBACE,OAAO,EAAE;wBACP,IAAI,EAAE,gBAAgB;wBACtB,UAAU,EAAE,SAAS;wBACrB,YAAY,EAAE,KAAK;wBACnB,EAAE,EAAE,eAAe;qBACpB;iBACF;gBACD,EAAE,OAAO,EAAE,gBAAgB,EAAE;aAC9B,CAAC;YAGF,IAAI,OAAO,CAAC,SAAS,IAAI,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACnE,QAAQ,CAAC,IAAI,CAAC;oBACZ,MAAM,EAAE;wBACN,yBAAyB,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;qBACjE;iBACF,CAAC,CAAC;YACL,CAAC;YAED,QAAQ,CAAC,IAAI,CACX;gBACE,OAAO,EAAE;oBACP,IAAI,EAAE,UAAU;oBAChB,UAAU,EAAE,yBAAyB;oBACrC,YAAY,EAAE,KAAK;oBACnB,EAAE,EAAE,SAAS;iBACd;aACF,EACD,EAAE,OAAO,EAAE,UAAU,EAAE,EACvB;gBACE,MAAM,EAAE;oBACN,GAAG,EAAE;wBACH,SAAS,EAAE,cAAc;wBACzB,WAAW,EAAE,eAAe;wBAC5B,MAAM,EAAE,iBAAiB;qBAC1B;oBACD,KAAK,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;oBAClB,YAAY,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE;iBACxC;aACF,EACD;gBACE,MAAM,EAAE;oBACN,GAAG,EAAE;wBACH,SAAS,EAAE,gBAAgB;wBAC3B,WAAW,EAAE,kBAAkB;qBAChC;oBACD,cAAc,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;oBAClC,eAAe,EAAE;wBACf,KAAK,EAAE;4BACL,MAAM,EAAE,aAAa;4BACrB,KAAK,EAAE,QAAQ;yBAChB;qBACF;oBACD,YAAY,EAAE,EAAE,IAAI,EAAE,eAAe,EAAE;iBACxC;aACF,EACD;gBACE,QAAQ,EAAE;oBACR,GAAG,EAAE,CAAC;oBACN,SAAS,EAAE,gBAAgB;oBAC3B,WAAW,EAAE,kBAAkB;oBAC/B,cAAc,EAAE,CAAC;oBACjB,eAAe,EAAE,CAAC;oBAClB,YAAY,EAAE,CAAC;iBAChB;aACF,EACD,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,CAC9B,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,yCAAyC,KAAK,CAAC,OAAO,EAAE,EACxD,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,8BAA8B,CAClC,SAAiB,EACjB,UAII,EAAE;QAEN,IAAI,CAAC;YACH,IAAI,CAAC,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACvC,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACvC,CAAC;YAED,MAAM,UAAU,GAAQ;gBACtB,WAAW,EAAE,IAAI,gBAAK,CAAC,QAAQ,CAAC,SAAS,CAAC;aAC3C,CAAC;YAGF,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;gBACzC,UAAU,CAAC,SAAS,GAAG,EAAE,CAAC;gBAC1B,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;oBACtB,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC;gBAChD,CAAC;gBACD,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC;oBACpB,UAAU,CAAC,SAAS,CAAC,IAAI,GAAG,OAAO,CAAC,OAAO,CAAC;gBAC9C,CAAC;YACH,CAAC;YAGD,IAAI,OAAO,CAAC,SAAS,IAAI,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC;gBACnE,UAAU,CAAC,SAAS,GAAG,IAAI,gBAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YAC/D,CAAC;YAED,MAAM,QAAQ,GAAU;gBACtB,EAAE,MAAM,EAAE,UAAU,EAAE;gBACtB;oBACE,OAAO,EAAE;wBACP,IAAI,EAAE,UAAU;wBAChB,UAAU,EAAE,WAAW;wBACvB,YAAY,EAAE,KAAK;wBACnB,EAAE,EAAE,SAAS;qBACd;iBACF;gBACD,EAAE,OAAO,EAAE,UAAU,EAAE;gBACvB;oBACE,MAAM,EAAE;wBACN,GAAG,EAAE;4BACH,SAAS,EAAE,cAAc;4BACzB,WAAW,EAAE,eAAe;yBAC7B;wBACD,WAAW,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;wBACxB,aAAa,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE;qBACtC;iBACF;gBACD;oBACE,QAAQ,EAAE;wBACR,GAAG,EAAE,CAAC;wBACN,SAAS,EAAE,gBAAgB;wBAC3B,WAAW,EAAE,kBAAkB;wBAC/B,WAAW,EAAE,CAAC;wBACd,aAAa,EAAE,CAAC;qBACjB;iBACF;gBACD,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE;aAC9B,CAAC;YAEF,OAAO,MAAM,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,IAAI,EAAE,CAAC;QAClE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,iDAAiD,KAAK,CAAC,OAAO,EAAE,EAChE,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AA9VY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAKR,WAAA,IAAA,sBAAW,EAAC,0BAAQ,CAAC,IAAI,CAAC,CAAA;IAC1B,WAAA,IAAA,sBAAW,EAAC,mCAAY,CAAC,IAAI,CAAC,CAAA;IAE9B,WAAA,IAAA,sBAAW,EAAC,qCAAa,CAAC,IAAI,CAAC,CAAA;qCAHmB,gBAAK;QAE7B,gBAAK;QAEJ,gBAAK;GARxB,eAAe,CA8V3B"}