"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicSchema = exports.Topic = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const subject_schema_1 = require("./subject.schema");
const swagger_1 = require("@nestjs/swagger");
let Topic = class Topic {
};
exports.Topic = Topic;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Topic name',
        example: 'Calculus',
        required: true,
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Topic.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Subject ID this topic belongs to',
        example: '60d21b4667d0d8992e610c85',
        type: String,
        required: true,
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'Subject', required: true }),
    __metadata("design:type", subject_schema_1.Subject)
], Topic.prototype, "subjectId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Topic description',
        example: 'The branch of mathematics that deals with limits and the differentiation and integration of functions',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Topic.prototype, "description", void 0);
exports.Topic = Topic = __decorate([
    (0, mongoose_1.Schema)()
], Topic);
exports.TopicSchema = mongoose_1.SchemaFactory.createForClass(Topic);
//# sourceMappingURL=topic.schema.js.map