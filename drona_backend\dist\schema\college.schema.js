"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollegeSchema = exports.College = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const swagger_1 = require("@nestjs/swagger");
let College = class College {
};
exports.College = College;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'College name',
        example: 'Harvard University',
        required: true,
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], College.prototype, "name", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'College address',
        example: '123 Main St, Cambridge, MA 02138',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "address", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'City', example: 'Cambridge' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'State/Province',
        example: 'Massachusetts',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "state", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Country', example: 'USA' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Postal/ZIP code', example: '02138' }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "postalCode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Contact email address',
        example: '<EMAIL>',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "contactEmail", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Contact phone number',
        example: '+****************',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "contactPhone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'College website URL',
        example: 'https://www.harvard.edu',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "website", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'College logo URL',
        example: 'https://example.com/logo.png',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], College.prototype, "logoUrl", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'College status',
        example: 'active',
        enum: ['active', 'inactive'],
        default: 'active',
    }),
    (0, mongoose_1.Prop)({ default: 'active', enum: ['active', 'inactive'] }),
    __metadata("design:type", String)
], College.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of teacher email addresses authorized for this college',
        example: ['<EMAIL>', '<EMAIL>'],
        type: [String],
    }),
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], College.prototype, "teachers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of college admin email addresses authorized for this college',
        example: ['<EMAIL>', '<EMAIL>'],
        type: [String],
    }),
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], College.prototype, "admins", void 0);
exports.College = College = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], College);
exports.CollegeSchema = mongoose_1.SchemaFactory.createForClass(College);
//# sourceMappingURL=college.schema.js.map