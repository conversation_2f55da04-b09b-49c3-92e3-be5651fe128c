import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { LoginDto } from './dto/login.dto';
import { RegisterDto } from './dto/register.dto';
import { UserDocument } from '../schema/user.schema';
import { FirebaseAuthService } from './firebase-auth.service';
import { AuthResponseDto } from './dto/auth-response.dto';
import { College } from '../schema/college.schema';
export declare class AuthService {
    private userModel;
    private collegeModel;
    private jwtService;
    private firebaseAuthService;
    constructor(userModel: Model<UserDocument>, collegeModel: Model<College>, jwtService: JwtService, firebaseAuthService: FirebaseAuthService);
    validateUser(email: string, password: string): Promise<UserDocument>;
    login(loginDto: LoginDto): Promise<AuthResponseDto>;
    register(registerDto: RegisterDto): Promise<AuthResponseDto>;
}
