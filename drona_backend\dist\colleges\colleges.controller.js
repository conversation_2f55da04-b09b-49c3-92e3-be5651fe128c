"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollegesController = void 0;
const common_1 = require("@nestjs/common");
const colleges_service_1 = require("./colleges.service");
const create_college_dto_1 = require("./dto/create-college.dto");
const update_college_dto_1 = require("./dto/update-college.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const swagger_1 = require("@nestjs/swagger");
let CollegesController = class CollegesController {
    constructor(collegesService) {
        this.collegesService = collegesService;
    }
    create(createCollegeDto) {
        return this.collegesService.create(createCollegeDto);
    }
    findAll() {
        return this.collegesService.findAll();
    }
    findOne(id) {
        return this.collegesService.findOne(id);
    }
    update(id, updateCollegeDto) {
        return this.collegesService.update(id, updateCollegeDto);
    }
    remove(id) {
        return this.collegesService.remove(id);
    }
    findAllPublic() {
        return this.collegesService.findAllPublic();
    }
};
exports.CollegesController = CollegesController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new college',
        description: 'Creates a new college (super admin only)',
    }),
    (0, swagger_1.ApiBody)({ type: create_college_dto_1.CreateCollegeDto }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'College created successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                name: { type: 'string', example: 'Harvard University' },
                address: {
                    type: 'string',
                    example: '123 Main St, Cambridge, MA 02138',
                },
                city: { type: 'string', example: 'Cambridge' },
                state: { type: 'string', example: 'Massachusetts' },
                country: { type: 'string', example: 'USA' },
                postalCode: { type: 'string', example: '02138' },
                contactPhone: { type: 'string', example: '+****************' },
                contactEmail: { type: 'string', example: '<EMAIL>' },
                website: { type: 'string', example: 'https://www.harvard.edu' },
                logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
                status: { type: 'string', example: 'active' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_college_dto_1.CreateCollegeDto]),
    __metadata("design:returntype", void 0)
], CollegesController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all colleges',
        description: 'Returns all colleges (super admin only)',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns all colleges',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    name: { type: 'string', example: 'Harvard University' },
                    address: {
                        type: 'string',
                        example: '123 Main St, Cambridge, MA 02138',
                    },
                    city: { type: 'string', example: 'Cambridge' },
                    state: { type: 'string', example: 'Massachusetts' },
                    country: { type: 'string', example: 'USA' },
                    postalCode: { type: 'string', example: '02138' },
                    contactPhone: { type: 'string', example: '+****************' },
                    contactEmail: { type: 'string', example: '<EMAIL>' },
                    website: { type: 'string', example: 'https://www.harvard.edu' },
                    logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
                    status: { type: 'string', example: 'active' },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CollegesController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get a college by ID',
        description: 'Returns a college by ID (super admin only)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the college',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                name: { type: 'string', example: 'Harvard University' },
                address: {
                    type: 'string',
                    example: '123 Main St, Cambridge, MA 02138',
                },
                city: { type: 'string', example: 'Cambridge' },
                state: { type: 'string', example: 'Massachusetts' },
                country: { type: 'string', example: 'USA' },
                postalCode: { type: 'string', example: '02138' },
                contactPhone: { type: 'string', example: '+****************' },
                contactEmail: { type: 'string', example: '<EMAIL>' },
                website: { type: 'string', example: 'https://www.harvard.edu' },
                logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
                status: { type: 'string', example: 'active' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CollegesController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update a college',
        description: 'Updates a college by ID (super admin only)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiBody)({ type: update_college_dto_1.UpdateCollegeDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'College updated successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                name: { type: 'string', example: 'Harvard University' },
                address: {
                    type: 'string',
                    example: '123 Main St, Cambridge, MA 02138',
                },
                city: { type: 'string', example: 'Cambridge' },
                state: { type: 'string', example: 'Massachusetts' },
                country: { type: 'string', example: 'USA' },
                postalCode: { type: 'string', example: '02138' },
                contactPhone: { type: 'string', example: '+****************' },
                contactEmail: { type: 'string', example: '<EMAIL>' },
                website: { type: 'string', example: 'https://www.harvard.edu' },
                logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
                status: { type: 'string', example: 'active' },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_college_dto_1.UpdateCollegeDto]),
    __metadata("design:returntype", void 0)
], CollegesController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete a college',
        description: 'Deletes a college by ID (super admin only)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'College deleted successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'College deleted successfully' },
                id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CollegesController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('public/list'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all registered colleges (public)',
        description: 'Returns a list of all registered colleges with limited information (ID, name, logoUrl). This endpoint is public.',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns a list of colleges with limited information',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    name: { type: 'string', example: 'Harvard University' },
                    logoUrl: { type: 'string', example: 'https://example.com/logo.png' },
                },
            },
        },
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], CollegesController.prototype, "findAllPublic", null);
exports.CollegesController = CollegesController = __decorate([
    (0, swagger_1.ApiTags)('Colleges'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('colleges'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [colleges_service_1.CollegesService])
], CollegesController);
//# sourceMappingURL=colleges.controller.js.map