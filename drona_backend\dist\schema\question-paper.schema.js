"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionPaperSchema = exports.QuestionPaper = exports.Section = exports.SectionQuestion = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
class SectionQuestion {
}
exports.SectionQuestion = SectionQuestion;
__decorate([
    (0, mongoose_1.Prop)({
        type: mongoose_2.Schema.Types.ObjectId,
        ref: 'Question',
        required: true,
    }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], SectionQuestion.prototype, "questionId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], SectionQuestion.prototype, "order", void 0);
class Section {
}
exports.Section = Section;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Section.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Section.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], Section.prototype, "order", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], Section.prototype, "sectionMarks", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [Object], required: true }),
    __metadata("design:type", Array)
], Section.prototype, "questions", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'Subject' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Section.prototype, "subjectId", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Section.prototype, "subjectName", void 0);
let QuestionPaper = class QuestionPaper {
};
exports.QuestionPaper = QuestionPaper;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], QuestionPaper.prototype, "title", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, required: false, ref: 'Subject' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionPaper.prototype, "subjectId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'Topic' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionPaper.prototype, "topicId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], QuestionPaper.prototype, "totalMarks", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], QuestionPaper.prototype, "duration", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], QuestionPaper.prototype, "instructions", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        type: [{ type: mongoose_2.Schema.Types.ObjectId, ref: 'Question' }],
        required: true,
    }),
    __metadata("design:type", Array)
], QuestionPaper.prototype, "questions", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, required: true, ref: 'User' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionPaper.prototype, "generatedBy", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, required: true, ref: 'College' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionPaper.prototype, "collegeId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 'active' }),
    __metadata("design:type", String)
], QuestionPaper.prototype, "status", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], QuestionPaper.prototype, "maxQuestions", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], QuestionPaper.prototype, "maxGeneration", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], QuestionPaper.prototype, "maxDownloads", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], QuestionPaper.prototype, "type", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], QuestionPaper.prototype, "withAnswers", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [Object], required: true }),
    __metadata("design:type", Array)
], QuestionPaper.prototype, "sections", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: ['NEET', 'CET', 'JEE', 'AIIMS', 'JIPMER', 'CUSTOM'] }),
    __metadata("design:type", String)
], QuestionPaper.prototype, "examType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ enum: ['auto', 'custom'], default: 'custom' }),
    __metadata("design:type", String)
], QuestionPaper.prototype, "difficultyMode", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], QuestionPaper.prototype, "numberOfQuestions", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], QuestionPaper.prototype, "isMultiSubject", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], QuestionPaper.prototype, "subjectCount", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [Object] }),
    __metadata("design:type", Array)
], QuestionPaper.prototype, "subjectBreakdown", void 0);
exports.QuestionPaper = QuestionPaper = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], QuestionPaper);
exports.QuestionPaperSchema = mongoose_1.SchemaFactory.createForClass(QuestionPaper);
//# sourceMappingURL=question-paper.schema.js.map