if(typeof cptable === 'undefined') cptable = {};
cptable[57005] = (function(){ var d = [], e = {}, D = [], j;
D[0] = "\u0000\u0001\u0002\u0003\u0004\u0005\u0006\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u001a\u001b\u001c\u001d\u001e\u001f !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~�ఁంఃఅఆఇఈఉఊఋఎఏఐఐఒఓఔఔకఖగఘఙచఛజఝఞటఠడఢణతథదధననపఫబభమయయరఱలళళవశషసహ�ాిీుూృెేైైొోౌౌ్�.������౦౧౨౩౪౫౬౭౮౯�����".split("");
for(j = 0; j != D[0].length; ++j) if(D[0][j].charCodeAt(0) !== 0xFFFD) { e[D[0][j]] = 0 + j; d[0 + j] = D[0][j];}
D[166] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ఌ����������������������".split("");
for(j = 0; j != D[166].length; ++j) if(D[166][j].charCodeAt(0) !== 0xFFFD) { e[D[166][j]] = 42496 + j; d[42496 + j] = D[166][j];}
D[167] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ౡ����������������������".split("");
for(j = 0; j != D[167].length; ++j) if(D[167][j].charCodeAt(0) !== 0xFFFD) { e[D[167][j]] = 42752 + j; d[42752 + j] = D[167][j];}
D[170] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ౠ����������������������".split("");
for(j = 0; j != D[170].length; ++j) if(D[170][j].charCodeAt(0) !== 0xFFFD) { e[D[170][j]] = 43520 + j; d[43520 + j] = D[170][j];}
D[223] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ౄ����������������������".split("");
for(j = 0; j != D[223].length; ++j) if(D[223][j].charCodeAt(0) !== 0xFFFD) { e[D[223][j]] = 57088 + j; d[57088 + j] = D[223][j];}
D[239] = "����������������������������������������������������������������౯౯౯౯౯౯౯౯౯౯౯౯������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[239].length; ++j) if(D[239][j].charCodeAt(0) !== 0xFFFD) { e[D[239][j]] = 61184 + j; d[61184 + j] = D[239][j];}
return {"enc": e, "dec": d }; })();
