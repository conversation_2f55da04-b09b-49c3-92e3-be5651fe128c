import { ConfigService } from '@nestjs/config';
import * as sharp from 'sharp';
export interface ImageCompressionOptions {
    maxSizeBytes?: number;
    quality?: number;
    maxWidth?: number;
    maxHeight?: number;
    format?: 'jpeg' | 'png' | 'webp';
    preserveAspectRatio?: boolean;
}
export interface CompressedImageResult {
    filename: string;
    path: string;
    url: string;
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    width: number;
    height: number;
    format: string;
}
export declare class ImageCompressionService {
    private readonly configService;
    private readonly logger;
    private readonly uploadsDir;
    private readonly baseUrl;
    constructor(configService: ConfigService);
    compressAndSaveImage(file: Express.Multer.File, options?: ImageCompressionOptions): Promise<CompressedImageResult>;
    compressBase64Image(base64Data: string, options?: ImageCompressionOptions): Promise<CompressedImageResult>;
    deleteImage(filename: string): Promise<boolean>;
    getImageInfo(file: Express.Multer.File): Promise<sharp.Metadata>;
    private isValidImageType;
    private getFileExtension;
    private formatBytes;
    private fileExists;
    private ensureUploadsDirectory;
}
