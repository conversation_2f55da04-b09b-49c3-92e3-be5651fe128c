"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/downloaded-papers/page",{

/***/ "(app-pages-browser)/./src/app/teacher/downloaded-papers/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/teacher/downloaded-papers/page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @tanstack/react-query */ \"(app-pages-browser)/./node_modules/@tanstack/react-query/build/modern/useQuery.js\");\n/* harmony import */ var _components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Breadcrumb */ \"(app-pages-browser)/./src/components/Breadcrumb.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock,Download,FileText,Hash,Table!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock,Download,FileText,Hash,Table!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hash.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock,Download,FileText,Hash,Table!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock,Download,FileText,Hash,Table!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock,Download,FileText,Hash,Table!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock,Download,FileText,Hash,Table!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/table.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Calendar,Clock,Download,FileText,Hash,Table!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/api/questionPapers */ \"(app-pages-browser)/./src/lib/api/questionPapers.ts\");\n/* harmony import */ var _components_ui_pagination__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/pagination */ \"(app-pages-browser)/./src/components/ui/pagination.tsx\");\n/* harmony import */ var xlsx__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! xlsx */ \"(app-pages-browser)/./node_modules/xlsx/xlsx.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst DownloadedPapersPage = ()=>{\n    _s();\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [pageSize, setPageSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(10);\n    const [downloadingIds, setDownloadingIds] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    // Fetch question papers\n    const { data: questionPapers = [], isLoading, error, refetch } = (0,_tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery)({\n        queryKey: [\n            'question-papers'\n        ],\n        queryFn: _lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_5__.getQuestionPapers,\n        staleTime: 5 * 60 * 1000\n    });\n    // Calculate pagination\n    const totalPages = Math.ceil(questionPapers.length / pageSize);\n    const startIndex = (currentPage - 1) * pageSize;\n    const paginatedPapers = questionPapers.slice(startIndex, startIndex + pageSize);\n    // Generate Excel file with answers (simplified format)\n    const generateAnswersExcel = (questionPaper, filename)=>{\n        const worksheetData = [];\n        // Add title row\n        worksheetData.push([\n            questionPaper.title || 'Question Paper'\n        ]);\n        worksheetData.push([]); // Empty row for spacing\n        // Add header row\n        worksheetData.push([\n            'Question No.',\n            'Correct Answer'\n        ]);\n        // Process questions\n        if (questionPaper.isMultiSubject && questionPaper.sections) {\n            let questionNumber = 1;\n            questionPaper.sections.forEach((section)=>{\n                if (section.questions && section.questions.length > 0) {\n                    section.questions.forEach((questionItem)=>{\n                        const question = questionItem.question;\n                        worksheetData.push([\n                            questionNumber,\n                            question.answer || 'N/A'\n                        ]);\n                        questionNumber++;\n                    });\n                }\n            });\n        } else {\n            questionPaper.questions.forEach((question, index)=>{\n                worksheetData.push([\n                    index + 1,\n                    question.answer || 'N/A'\n                ]);\n            });\n        }\n        // Create workbook and worksheet\n        const workbook = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_new();\n        const worksheet = xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.aoa_to_sheet(worksheetData);\n        // Set column widths\n        worksheet['!cols'] = [\n            {\n                width: 15\n            },\n            {\n                width: 30\n            } // Answer\n        ];\n        // Style the title row\n        if (worksheet['A1']) {\n            worksheet['A1'].s = {\n                font: {\n                    bold: true,\n                    sz: 14\n                },\n                alignment: {\n                    horizontal: 'center'\n                }\n            };\n        }\n        // Merge title cell across columns\n        worksheet['!merges'] = [\n            {\n                s: {\n                    r: 0,\n                    c: 0\n                },\n                e: {\n                    r: 0,\n                    c: 1\n                }\n            }\n        ];\n        xlsx__WEBPACK_IMPORTED_MODULE_8__.utils.book_append_sheet(workbook, worksheet, 'Answers');\n        // Generate and download Excel file\n        xlsx__WEBPACK_IMPORTED_MODULE_8__.writeFile(workbook, filename);\n    };\n    // Generate PDF with solutions and hints\n    const generateSolutionsPDF = async function(paperId) {\n        let includeHints = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n        // Import PDF generator dynamically to avoid SSR issues\n        const { default: PDFGenerator } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_pdfGenerator_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\"));\n        // Fetch question paper data\n        const data = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_5__.getQuestionPaper)(paperId);\n        if (!data) {\n            throw new Error('Question paper data not found');\n        }\n        // Create enhanced question paper object for solutions\n        const questionPaper = {\n            ...data,\n            withAnswers: true,\n            withSolutions: true,\n            withHints: includeHints\n        };\n        // Get college info from the API response\n        const collegeData = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_5__.getQuestionPaperForPDF)(paperId);\n        // Generate PDF using frontend generator with solutions focus\n        const pdfGenerator = new PDFGenerator();\n        return await pdfGenerator.generatePDF(questionPaper, collegeData.college);\n    };\n    // Handle download with different options\n    const handleDownload = async (paper, downloadType)=>{\n        try {\n            setDownloadingIds((prev)=>new Set(prev).add(paper._id));\n            // First, fetch the complete question paper details to ensure we have all data\n            console.log('Fetching complete question paper details for:', paper._id);\n            const fullQuestionPaper = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_5__.getQuestionPaper)(paper._id);\n            console.log('Full question paper data:', fullQuestionPaper);\n            // Check if the question paper has questions\n            if (!fullQuestionPaper.questions || fullQuestionPaper.questions.length === 0) {\n                throw new Error('This question paper does not contain any questions. It may have been created incorrectly.');\n            }\n            console.log(\"Question paper has \".concat(fullQuestionPaper.questions.length, \" questions. Proceeding with download...\"));\n            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');\n            const baseTitle = paper.title.replace(/[^a-zA-Z0-9\\s]/g, '').replace(/\\s+/g, '_');\n            let filename;\n            switch(downloadType){\n                case 'questions':\n                    // Generate PDF with questions only (no answers)\n                    const questionsPdfBlob = await (0,_lib_api_questionPapers__WEBPACK_IMPORTED_MODULE_5__.generateQuestionPaperPDF)(paper._id, false);\n                    filename = \"\".concat(baseTitle, \"_Questions_\").concat(timestamp, \".pdf\");\n                    // Create download link\n                    const questionsUrl = window.URL.createObjectURL(questionsPdfBlob);\n                    const questionsLink = document.createElement('a');\n                    questionsLink.href = questionsUrl;\n                    questionsLink.download = filename;\n                    document.body.appendChild(questionsLink);\n                    questionsLink.click();\n                    document.body.removeChild(questionsLink);\n                    window.URL.revokeObjectURL(questionsUrl);\n                    break;\n                case 'answers':\n                    // Generate Excel file with question numbers and answers\n                    filename = \"\".concat(baseTitle, \"_Answers_\").concat(timestamp, \".xlsx\");\n                    generateAnswersExcel(fullQuestionPaper, filename);\n                    break;\n                case 'solutions':\n                    // Generate PDF with solutions and hints\n                    const solutionsPdfBlob = await generateSolutionsPDF(paper._id, true);\n                    filename = \"\".concat(baseTitle, \"_Solutions_\").concat(timestamp, \".pdf\");\n                    // Create download link\n                    const solutionsUrl = window.URL.createObjectURL(solutionsPdfBlob);\n                    const solutionsLink = document.createElement('a');\n                    solutionsLink.href = solutionsUrl;\n                    solutionsLink.download = filename;\n                    document.body.appendChild(solutionsLink);\n                    solutionsLink.click();\n                    document.body.removeChild(solutionsLink);\n                    window.URL.revokeObjectURL(solutionsUrl);\n                    break;\n                default:\n                    throw new Error('Invalid download type');\n            }\n            console.log('Download completed successfully');\n        } catch (error) {\n            console.error('Download failed:', error);\n            const errorMessage = error instanceof Error ? error.message : 'Failed to download the question paper. Please try again.';\n            alert(errorMessage);\n        } finally{\n            setDownloadingIds((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(paper._id);\n                return newSet;\n            });\n        }\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString('en-US', {\n            year: 'numeric',\n            month: 'short',\n            day: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-gray-50 py-8\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-xl font-semibold text-black\",\n                            children: \"Downloaded Papers\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Breadcrumb__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            items: [\n                                {\n                                    label: 'Home',\n                                    href: '/teacher'\n                                },\n                                {\n                                    label: '...',\n                                    href: '#'\n                                },\n                                {\n                                    label: 'Downloaded Papers'\n                                }\n                            ],\n                            className: \"text-sm mt-1\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                    lineNumber: 224,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-lg font-semibold text-gray-900\",\n                                                children: \"Question Papers\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                lineNumber: 241,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 mt-1\",\n                                                children: [\n                                                    questionPapers.length,\n                                                    \" total papers available for download\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>refetch(),\n                                            disabled: isLoading,\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                isLoading ? 'Refreshing...' : 'Refresh'\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                            lineNumber: 238,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500\",\n                                            children: \"Loading question papers...\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 266,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 15\n                            }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-red-500 mb-4\",\n                                            children: \"Failed to load question papers\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>refetch(),\n                                            variant: \"outline\",\n                                            children: \"Try Again\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, undefined) : questionPapers.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center py-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 281,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 mb-2\",\n                                            children: \"No question papers found\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-400\",\n                                            children: \"Generate your first question paper to see it here\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                lineNumber: 279,\n                                columnNumber: 15\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"overflow-x-auto\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                            className: \"min-w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"border-b border-gray-200 bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Paper Title\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Subject\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 293,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Details\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Created\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 295,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-left font-medium text-gray-700\",\n                                                                children: \"Status\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                className: \"py-3 px-6 text-center font-medium text-gray-700\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                    children: paginatedPapers.map((paper)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                            className: \"border-b border-gray-100 hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start gap-3\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center flex-shrink-0\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                    className: \"w-5 h-5 text-blue-600\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                    lineNumber: 307,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 306,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                        className: \"font-medium text-gray-900 line-clamp-2\",\n                                                                                        children: paper.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 310,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                        className: \"text-sm text-gray-500 mt-1\",\n                                                                                        children: [\n                                                                                            \"ID: \",\n                                                                                            paper._id.slice(-8)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 313,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 309,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 304,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: paper.subjectId ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                        children: paper.subjectId.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 31\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800\",\n                                                                        children: [\n                                                                            \"Multi-Subject\",\n                                                                            paper.subjectCount ? \" (\".concat(paper.subjectCount, \")\") : ''\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 327,\n                                                                        columnNumber: 31\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 321,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1 text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 337,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            paper.totalMarks,\n                                                                                            \" marks\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 338,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 336,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-1 text-sm text-gray-600\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                        className: \"w-3 h-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 341,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: [\n                                                                                            paper.duration,\n                                                                                            \" min\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 342,\n                                                                                        columnNumber: 33\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 340,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 335,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 334,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-1 text-sm text-gray-600\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"w-3 h-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 350,\n                                                                                columnNumber: 31\n                                                                            }, undefined),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: formatDate(paper.createdAt)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                lineNumber: 351,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 349,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 348,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(paper.status.toLowerCase() === 'active' ? 'bg-emerald-100 text-emerald-800' : 'bg-gray-100 text-gray-800'),\n                                                                        children: paper.status.charAt(0).toUpperCase() + paper.status.slice(1)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 357,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 356,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"py-4 px-6\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                                                                                    asChild: true,\n                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                                        variant: \"outline\",\n                                                                                        size: \"sm\",\n                                                                                        disabled: downloadingIds.has(paper._id),\n                                                                                        className: \"flex items-center gap-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                                className: \"w-4 h-4\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                                lineNumber: 377,\n                                                                                                columnNumber: 37\n                                                                                            }, undefined),\n                                                                                            downloadingIds.has(paper._id) ? 'Downloading...' : 'Download'\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                        lineNumber: 371,\n                                                                                        columnNumber: 35\n                                                                                    }, undefined)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                    lineNumber: 370,\n                                                                                    columnNumber: 33\n                                                                                }, undefined),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                                                                                    align: \"end\",\n                                                                                    className: \"w-48\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                                                                            onClick: ()=>handleDownload(paper, 'questions'),\n                                                                                            disabled: downloadingIds.has(paper._id),\n                                                                                            className: \"flex items-center gap-2 cursor-pointer\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                                    lineNumber: 387,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                \"Download Questions\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                            lineNumber: 382,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                                                                            onClick: ()=>handleDownload(paper, 'answers'),\n                                                                                            disabled: downloadingIds.has(paper._id),\n                                                                                            className: \"flex items-center gap-2 cursor-pointer\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                                    lineNumber: 395,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                \"Download Answers\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                            lineNumber: 390,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined),\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                                                                            onClick: ()=>handleDownload(paper, 'solutions'),\n                                                                                            disabled: downloadingIds.has(paper._id),\n                                                                                            className: \"flex items-center gap-2 cursor-pointer\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Calendar_Clock_Download_FileText_Hash_Table_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                    className: \"w-4 h-4\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                                    lineNumber: 403,\n                                                                                                    columnNumber: 37\n                                                                                                }, undefined),\n                                                                                                \"Download Solutions\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                            lineNumber: 398,\n                                                                                            columnNumber: 35\n                                                                                        }, undefined)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                                    lineNumber: 381,\n                                                                                    columnNumber: 33\n                                                                                }, undefined)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                            lineNumber: 369,\n                                                                            columnNumber: 31\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                        lineNumber: 368,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                                    lineNumber: 367,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, paper._id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                            lineNumber: 302,\n                                                            columnNumber: 25\n                                                        }, undefined))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 289,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-6 py-4 border-t border-gray-200\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_pagination__WEBPACK_IMPORTED_MODULE_6__.Pagination, {\n                                            currentPage: currentPage,\n                                            totalPages: totalPages,\n                                            onPageChange: setCurrentPage,\n                                            pageSize: pageSize,\n                                            totalItems: questionPapers.length,\n                                            onPageSizeChange: setPageSize\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                                        lineNumber: 418,\n                                        columnNumber: 19\n                                    }, undefined)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                            lineNumber: 261,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n                    lineNumber: 236,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n            lineNumber: 223,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\FL\\\\medicos\\\\medicos-frontend\\\\src\\\\app\\\\teacher\\\\downloaded-papers\\\\page.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DownloadedPapersPage, \"rtLVZSNtDe4q9Z0F1wq6kyiIxTg=\", false, function() {\n    return [\n        _tanstack_react_query__WEBPACK_IMPORTED_MODULE_7__.useQuery\n    ];\n});\n_c = DownloadedPapersPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DownloadedPapersPage);\nvar _c;\n$RefreshReg$(_c, \"DownloadedPapersPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/teacher/downloaded-papers/page.tsx\n"));

/***/ })

});