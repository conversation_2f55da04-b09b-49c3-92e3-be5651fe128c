import { Model } from 'mongoose';
import { DownloadDocument } from '../../schema/download.schema';
import { UserActivityDocument } from '../../schema/user-activity.schema';
import { QuestionPaperDocument } from '../../schema/question-paper.schema';
export interface TrackDownloadDto {
    userId: string;
    paperId: string;
    collegeId?: string;
    downloadFormat: 'pdf' | 'docx';
    ipAddress?: string;
}
export interface TrackPaperGenerationDto {
    userId: string;
    paperId: string;
    collegeId?: string;
    subjectId: string;
    ipAddress?: string;
}
export interface TrackUserActivityDto {
    userId: string;
    collegeId?: string;
    activityType: 'login' | 'question_creation' | 'paper_generation';
    activityDetails?: any;
    ipAddress?: string;
}
export declare class TrackingService {
    private downloadModel;
    private userActivityModel;
    private questionPaperModel;
    private readonly logger;
    constructor(downloadModel: Model<DownloadDocument>, userActivityModel: Model<UserActivityDocument>, questionPaperModel: Model<QuestionPaperDocument>);
    trackDownload(trackDownloadDto: TrackDownloadDto): Promise<void>;
    trackPaperGeneration(trackPaperGenerationDto: TrackPaperGenerationDto): Promise<void>;
    trackUserActivity(trackUserActivityDto: TrackUserActivityDto): Promise<void>;
    getTeacherDownloadStats(teacherId: string, filters?: {
        startDate?: Date;
        endDate?: Date;
        subjectId?: string;
    }): Promise<any[]>;
    getTeacherPaperGenerationStats(teacherId: string, filters?: {
        startDate?: Date;
        endDate?: Date;
        subjectId?: string;
    }): Promise<any[]>;
}
