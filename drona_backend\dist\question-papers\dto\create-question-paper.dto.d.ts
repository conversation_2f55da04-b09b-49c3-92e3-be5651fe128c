import { Types } from 'mongoose';
export declare enum ExamType {
    NEET = "NEET",
    CET = "CET",
    JEE = "JEE",
    AIIMS = "AIIMS",
    JIPMER = "JIPMER",
    CUSTOM = "CUSTOM"
}
export declare enum SubjectShortCode {
    PHYSICS = "physics",
    CHEMISTRY = "chemistry",
    BIOLOGY = "biology",
    MATHEMATICS = "mathematics",
    MATH = "math",
    PHY = "phy",
    CHEM = "chem",
    BIO = "bio"
}
export declare class CustomDifficultyConfig {
    easyPercentage: number;
    mediumPercentage: number;
    hardPercentage: number;
}
export declare class CustomiseConfig {
    customDifficulty: CustomDifficultyConfig;
    numberOfQuestions: number;
    totalMarks: number;
    duration: number;
    includeAnswers: boolean;
}
export declare class SubjectConfiguration {
    subject: SubjectShortCode;
    numberOfQuestions: number;
    totalMarks: number;
    customDifficulty: CustomDifficultyConfig;
    topicId?: string;
}
export declare class CreateQuestionPaperDto {
    title: string;
    description?: string;
    subject?: string;
    topicId?: Types.ObjectId;
    totalMarks?: number;
    duration?: number;
    instructions?: string;
    examType?: ExamType;
    customise?: CustomiseConfig;
    subjects?: SubjectConfiguration[];
    includeAnswers?: boolean;
    bypassStatusChecks?: boolean;
}
