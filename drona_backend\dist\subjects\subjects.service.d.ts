import { Model } from 'mongoose';
import { Subject, SubjectDocument } from '../schema/subject.schema';
import { TopicDocument } from '../schema/topic.schema';
import { CreateSubjectDto } from './dto/create-subject.dto';
import { UpdateSubjectDto } from './dto/update-subject.dto';
export declare class SubjectsService {
    private subjectModel;
    private topicModel;
    private readonly logger;
    constructor(subjectModel: Model<SubjectDocument>, topicModel: Model<TopicDocument>);
    create(createSubjectDto: CreateSubjectDto): Promise<Subject>;
    findAll(): Promise<Subject[]>;
    findAllWithTopics(): Promise<any[]>;
    findOne(id: string): Promise<Subject>;
    update(id: string, updateSubjectDto: UpdateSubjectDto): Promise<Subject>;
    remove(id: string): Promise<void>;
}
