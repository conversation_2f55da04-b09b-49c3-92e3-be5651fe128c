"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TopicsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const topics_service_1 = require("./topics.service");
const create_topic_dto_1 = require("./dto/create-topic.dto");
const update_topic_dto_1 = require("./dto/update-topic.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
let TopicsController = TopicsController_1 = class TopicsController {
    constructor(topicsService) {
        this.topicsService = topicsService;
        this.logger = new common_1.Logger(TopicsController_1.name);
    }
    create(createTopicDto) {
        return this.topicsService.create(createTopicDto);
    }
    findAll(subjectId) {
        if (subjectId) {
            return this.topicsService.findBySubject(subjectId);
        }
        return this.topicsService.findAll();
    }
    findOne(id) {
        return this.topicsService.findOne(id);
    }
    update(id, updateTopicDto) {
        return this.topicsService.update(id, updateTopicDto);
    }
    remove(id) {
        return this.topicsService.remove(id);
    }
};
exports.TopicsController = TopicsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new topic',
        description: 'Creates a new topic under a subject (super admin only)',
    }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Topic created successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                name: { type: 'string', example: 'Calculus' },
                subjectId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                description: {
                    type: 'string',
                    example: 'Branch of mathematics dealing with limits',
                },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiConflictResponse)({
        description: 'Conflict - Topic with this name already exists in the subject',
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Subject not found' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_topic_dto_1.CreateTopicDto]),
    __metadata("design:returntype", void 0)
], TopicsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all topics',
        description: 'Returns all topics, optionally filtered by subject',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'subjectId',
        description: 'Subject ID to filter topics',
        required: false,
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns list of topics',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                    name: { type: 'string', example: 'Calculus' },
                    subjectId: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                            name: { type: 'string', example: 'Mathematics' },
                            description: { type: 'string', example: 'Study of numbers' },
                        },
                    },
                    description: {
                        type: 'string',
                        example: 'Branch of mathematics dealing with limits',
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    __param(0, (0, common_1.Query)('subjectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TopicsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get topic by ID',
        description: 'Returns a specific topic by its ID',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Topic ID',
        example: '60d21b4667d0d8992e610c86',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the topic',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                name: { type: 'string', example: 'Calculus' },
                subjectId: {
                    type: 'object',
                    properties: {
                        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                        name: { type: 'string', example: 'Mathematics' },
                        description: { type: 'string', example: 'Study of numbers' },
                    },
                },
                description: {
                    type: 'string',
                    example: 'Branch of mathematics dealing with limits',
                },
            },
        },
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Topic not found' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TopicsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update topic',
        description: 'Updates a topic (super admin only)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Topic ID',
        example: '60d21b4667d0d8992e610c86',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Topic updated successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                name: { type: 'string', example: 'Calculus' },
                subjectId: {
                    type: 'object',
                    properties: {
                        _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                        name: { type: 'string', example: 'Mathematics' },
                        description: { type: 'string', example: 'Study of numbers' },
                    },
                },
                description: {
                    type: 'string',
                    example: 'Branch of mathematics dealing with limits',
                },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiConflictResponse)({
        description: 'Conflict - Topic with this name already exists in the subject',
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Topic or Subject not found' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_topic_dto_1.UpdateTopicDto]),
    __metadata("design:returntype", void 0)
], TopicsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete topic',
        description: 'Deletes a topic (super admin only)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Topic ID',
        example: '60d21b4667d0d8992e610c86',
    }),
    (0, swagger_1.ApiOkResponse)({ description: 'Topic deleted successfully' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Topic not found' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TopicsController.prototype, "remove", null);
exports.TopicsController = TopicsController = TopicsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Topics'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('topics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [topics_service_1.TopicsService])
], TopicsController);
//# sourceMappingURL=topics.controller.js.map