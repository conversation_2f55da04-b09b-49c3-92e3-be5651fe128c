"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionPaper = void 0;
const swagger_1 = require("@nestjs/swagger");
const typeorm_1 = require("typeorm");
const user_entity_1 = require("../../users/entities/user.entity");
const question_entity_1 = require("../../questions/entities/question.entity");
let QuestionPaper = class QuestionPaper {
};
exports.QuestionPaper = QuestionPaper;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The unique identifier of the question paper',
        example: '123e4567-e89b-12d3-a456-************',
    }),
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    __metadata("design:type", String)
], QuestionPaper.prototype, "id", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The title of the question paper',
        example: 'Mathematics Final Exam',
    }),
    (0, typeorm_1.Column)(),
    __metadata("design:type", String)
], QuestionPaper.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The description of the question paper',
        example: 'Final examination for Mathematics course',
        required: false,
    }),
    (0, typeorm_1.Column)({ nullable: true }),
    __metadata("design:type", String)
], QuestionPaper.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The maximum number of questions allowed in this paper',
        example: 10,
        required: false,
    }),
    (0, typeorm_1.Column)({ default: 10 }),
    __metadata("design:type", Number)
], QuestionPaper.prototype, "maxQuestions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The user who created this question paper',
        type: () => user_entity_1.User,
    }),
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, (user) => user.questionPapers),
    __metadata("design:type", user_entity_1.User)
], QuestionPaper.prototype, "generatedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The questions in this question paper',
        type: [question_entity_1.Question],
    }),
    (0, typeorm_1.OneToMany)(() => question_entity_1.Question, (question) => question.questionPaper),
    __metadata("design:type", Array)
], QuestionPaper.prototype, "questions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The date when the question paper was created',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], QuestionPaper.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The date when the question paper was last updated',
        example: '2023-01-01T00:00:00.000Z',
    }),
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], QuestionPaper.prototype, "updatedAt", void 0);
exports.QuestionPaper = QuestionPaper = __decorate([
    (0, typeorm_1.Entity)()
], QuestionPaper);
//# sourceMappingURL=question-paper.entity.js.map