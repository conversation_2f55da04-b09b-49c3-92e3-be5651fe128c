"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrateQuestionUsage = migrateQuestionUsage;
const core_1 = require("@nestjs/core");
const app_module_1 = require("../app.module");
const question_usage_service_1 = require("../question-usage/question-usage.service");
const mongoose_1 = require("@nestjs/mongoose");
const question_paper_schema_1 = require("../schema/question-paper.schema");
const common_1 = require("@nestjs/common");
async function migrateQuestionUsage() {
    const logger = new common_1.Logger('QuestionUsageMigration');
    try {
        const app = await core_1.NestFactory.createApplicationContext(app_module_1.AppModule);
        const questionUsageService = app.get(question_usage_service_1.QuestionUsageService);
        const questionPaperModel = app.get((0, mongoose_1.getModelToken)(question_paper_schema_1.QuestionPaper.name));
        logger.log('Starting question usage migration...');
        const questionPapers = await questionPaperModel
            .find({
            type: { $ne: 'limit' },
            questions: { $exists: true, $not: { $size: 0 } },
            collegeId: { $exists: true },
        })
            .populate('questions')
            .exec();
        logger.log(`Found ${questionPapers.length} question papers to migrate`);
        let totalProcessed = 0;
        let totalRecorded = 0;
        let totalSkipped = 0;
        for (const paper of questionPapers) {
            try {
                if (!paper.collegeId ||
                    !paper.questions ||
                    paper.questions.length === 0) {
                    logger.warn(`Skipping paper ${paper._id}: Missing collegeId or questions`);
                    continue;
                }
                const usageData = paper.questions.map((question) => ({
                    collegeId: paper.collegeId.toString(),
                    questionId: question._id.toString(),
                    questionPaperId: paper._id.toString(),
                    usedBy: paper.generatedBy.toString(),
                    subjectId: paper.subjectId ? paper.subjectId.toString() : question.subjectId?.toString(),
                    topicId: paper.topicId?.toString() || question.topicId?.toString(),
                    metadata: {
                        examType: paper.examType,
                        difficulty: question.difficulty,
                        marks: question.marks || 1,
                        migrated: true,
                    },
                }));
                const result = await questionUsageService.recordMultipleQuestionUsage(usageData);
                totalProcessed++;
                totalRecorded += result.recorded;
                totalSkipped += result.skipped;
                if (totalProcessed % 10 === 0) {
                    logger.log(`Processed ${totalProcessed}/${questionPapers.length} papers...`);
                }
            }
            catch (error) {
                logger.error(`Error processing paper ${paper._id}: ${error.message}`);
            }
        }
        logger.log('Migration completed successfully!');
        logger.log(`Summary:`);
        logger.log(`- Question papers processed: ${totalProcessed}`);
        logger.log(`- Question usage records created: ${totalRecorded}`);
        logger.log(`- Duplicate records skipped: ${totalSkipped}`);
        await app.close();
    }
    catch (error) {
        logger.error(`Migration failed: ${error.message}`, error.stack);
        process.exit(1);
    }
}
if (require.main === module) {
    migrateQuestionUsage()
        .then(() => {
        console.log('Migration completed successfully');
        process.exit(0);
    })
        .catch((error) => {
        console.error('Migration failed:', error);
        process.exit(1);
    });
}
//# sourceMappingURL=migrate-question-usage.js.map