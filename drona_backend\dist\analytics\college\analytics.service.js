"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_activity_schema_1 = require("../../schema/user-activity.schema");
const user_schema_1 = require("../../schema/user.schema");
const question_paper_schema_1 = require("../../schema/question-paper.schema");
const question_schema_1 = require("../../schema/question.schema");
const subject_schema_1 = require("../../schema/subject.schema");
const download_schema_1 = require("../../schema/download.schema");
const services_1 = require("../../common/services");
let AnalyticsService = AnalyticsService_1 = class AnalyticsService {
    constructor(userActivityModel, userModel, questionPaperModel, subjectModel, questionModel, downloadModel, cacheService) {
        this.userActivityModel = userActivityModel;
        this.userModel = userModel;
        this.questionPaperModel = questionPaperModel;
        this.subjectModel = subjectModel;
        this.questionModel = questionModel;
        this.downloadModel = downloadModel;
        this.cacheService = cacheService;
        this.logger = new common_1.Logger(AnalyticsService_1.name);
    }
    async getCollegeSummary(collegeId) {
        const cacheKey = `college_summary_${collegeId}`;
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log(`Computing college summary for collegeId: ${collegeId} (cache miss)`);
            try {
                if (!collegeId || !mongoose_2.Types.ObjectId.isValid(collegeId)) {
                    throw new Error('Invalid collegeId');
                }
                const collegeObjectId = new mongoose_2.Types.ObjectId(collegeId);
                const [totalTeachers, activityCounts, totalDownloads] = await Promise.all([
                    this.userModel.countDocuments({
                        collegeId: collegeObjectId,
                        role: 'teacher',
                        status: 'active',
                    }),
                    this.userActivityModel
                        .aggregate([
                        {
                            $match: {
                                collegeId: collegeObjectId,
                            },
                        },
                        {
                            $group: {
                                _id: '$activityType',
                                count: { $sum: 1 },
                            },
                        },
                    ])
                        .exec(),
                    this.downloadModel.countDocuments({
                        collegeId: collegeObjectId,
                    }),
                ]);
                const activityMap = activityCounts.reduce((acc, curr) => {
                    acc[curr._id] = curr.count;
                    return acc;
                }, {});
                return {
                    totalTeachers,
                    totalPapersGenerated: activityMap['paper_generation'] || 0,
                    totalDownloads,
                };
            }
            catch (error) {
                this.logger.error(`Error getting college summary: ${error.message}`, error.stack);
                throw error;
            }
        });
    }
    async getTeacherActivityLogs(collegeId, limit = 100, page = 1, filters = {}) {
        const filterKey = JSON.stringify(filters);
        const cacheKey = `teacher_downloads_${collegeId}_${page}_${limit}_${Buffer.from(filterKey).toString('base64')}`;
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log(`Computing teacher download history for collegeId: ${collegeId}, page: ${page}, limit: ${limit}, filters: ${filterKey} (cache miss)`);
            try {
                if (!collegeId || !mongoose_2.Types.ObjectId.isValid(collegeId)) {
                    throw new Error('Invalid collegeId');
                }
                const collegeObjectId = new mongoose_2.Types.ObjectId(collegeId);
                const downloadQuery = { collegeId: collegeObjectId };
                if (filters.teacherId) {
                    if (!mongoose_2.Types.ObjectId.isValid(filters.teacherId)) {
                        throw new Error('Invalid teacherId');
                    }
                    downloadQuery.userId = new mongoose_2.Types.ObjectId(filters.teacherId);
                }
                if (filters.startDate || filters.endDate) {
                    downloadQuery.downloadDate = {};
                    if (filters.startDate) {
                        downloadQuery.downloadDate.$gte = filters.startDate;
                    }
                    if (filters.endDate) {
                        downloadQuery.downloadDate.$lte = filters.endDate;
                    }
                }
                const teacherDownloads = await this.downloadModel
                    .aggregate([
                    { $match: downloadQuery },
                    {
                        $lookup: {
                            from: 'users',
                            localField: 'userId',
                            foreignField: '_id',
                            as: 'teacher',
                        },
                    },
                    { $unwind: '$teacher' },
                    {
                        $lookup: {
                            from: 'questionpapers',
                            localField: 'paperId',
                            foreignField: '_id',
                            as: 'questionPaper',
                        },
                    },
                    { $unwind: '$questionPaper' },
                    {
                        $lookup: {
                            from: 'subjects',
                            localField: 'questionPaper.subjectId',
                            foreignField: '_id',
                            as: 'subject',
                        },
                    },
                    { $unwind: '$subject' },
                    {
                        $group: {
                            _id: {
                                teacherId: '$teacher._id',
                                teacherName: '$teacher.displayName',
                                teacherEmail: '$teacher.email',
                                subjectId: '$subject._id',
                                subjectName: '$subject.name',
                            },
                            downloadCount: { $sum: 1 },
                            lastDownload: { $max: '$downloadDate' },
                        },
                    },
                    {
                        $group: {
                            _id: {
                                teacherId: '$_id.teacherId',
                                teacherName: '$_id.teacherName',
                                teacherEmail: '$_id.teacherEmail',
                            },
                            subjectWiseDownloads: {
                                $push: {
                                    subjectId: '$_id.subjectId',
                                    subjectName: '$_id.subjectName',
                                    downloadCount: '$downloadCount',
                                    lastDownload: '$lastDownload',
                                },
                            },
                            totalDownloads: { $sum: '$downloadCount' },
                        },
                    },
                    {
                        $project: {
                            _id: 0,
                            teacherId: '$_id.teacherId',
                            teacherName: '$_id.teacherName',
                            teacherEmail: '$_id.teacherEmail',
                            totalDownloads: 1,
                            subjectWiseDownloads: {
                                $sortArray: {
                                    input: '$subjectWiseDownloads',
                                    sortBy: { subjectName: 1 },
                                },
                            },
                        },
                    },
                    { $sort: { teacherName: 1 } },
                    { $skip: (page - 1) * limit },
                    { $limit: limit },
                ])
                    .exec();
                const totalCountResult = await this.downloadModel
                    .aggregate([
                    { $match: downloadQuery },
                    {
                        $lookup: {
                            from: 'users',
                            localField: 'userId',
                            foreignField: '_id',
                            as: 'teacher',
                        },
                    },
                    { $unwind: '$teacher' },
                    {
                        $group: {
                            _id: '$teacher._id',
                        },
                    },
                    {
                        $count: 'total',
                    },
                ])
                    .exec();
                const totalCount = totalCountResult.length > 0 ? totalCountResult[0].total : 0;
                return {
                    teachers: teacherDownloads,
                    pagination: {
                        total: totalCount,
                        page,
                        limit,
                        pages: Math.ceil(totalCount / limit),
                    },
                    filters: filters,
                };
            }
            catch (error) {
                this.logger.error(`Error getting teacher download history: ${error.message}`, error.stack);
                throw error;
            }
        });
    }
    async getQuestionPaperDownloadStats(collegeId, filters = {}) {
        const filterKey = JSON.stringify(filters);
        const cacheKey = `question_paper_stats_${collegeId}_${Buffer.from(filterKey).toString('base64')}`;
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log(`Computing question paper stats for collegeId: ${collegeId}, filters: ${filterKey} (cache miss)`);
            try {
                if (!collegeId || !mongoose_2.Types.ObjectId.isValid(collegeId)) {
                    throw new Error('Invalid collegeId');
                }
                const collegeObjectId = new mongoose_2.Types.ObjectId(collegeId);
                const paperGeneratedQuery = { collegeId: collegeObjectId };
                if (filters.startDate || filters.endDate) {
                    paperGeneratedQuery.createdAt = {};
                    if (filters.startDate) {
                        paperGeneratedQuery.createdAt.$gte = filters.startDate;
                    }
                    if (filters.endDate) {
                        paperGeneratedQuery.createdAt.$lte = filters.endDate;
                    }
                }
                const downloadQuery = { collegeId: collegeObjectId };
                if (filters.startDate || filters.endDate) {
                    downloadQuery.downloadDate = {};
                    if (filters.startDate) {
                        downloadQuery.downloadDate.$gte = filters.startDate;
                    }
                    if (filters.endDate) {
                        downloadQuery.downloadDate.$lte = filters.endDate;
                    }
                }
                const papersGenerated = await this.questionPaperModel
                    .aggregate([
                    { $match: paperGeneratedQuery },
                    {
                        $group: {
                            _id: {
                                subjectId: '$subjectId',
                                year: { $year: '$createdAt' },
                                month: { $month: '$createdAt' },
                                day: { $dayOfMonth: '$createdAt' },
                            },
                            count: { $sum: 1 },
                        },
                    },
                    {
                        $lookup: {
                            from: 'subjects',
                            localField: '_id.subjectId',
                            foreignField: '_id',
                            as: 'subject',
                        },
                    },
                    { $unwind: '$subject' },
                    {
                        $project: {
                            subjectId: '$_id.subjectId',
                            subjectName: '$subject.name',
                            date: {
                                $dateFromParts: {
                                    year: '$_id.year',
                                    month: '$_id.month',
                                    day: '$_id.day',
                                },
                            },
                            generated: '$count',
                            _id: 0,
                        },
                    },
                ])
                    .exec();
                const papersDownloaded = await this.downloadModel
                    .aggregate([
                    { $match: downloadQuery },
                    {
                        $lookup: {
                            from: 'questionpapers',
                            localField: 'paperId',
                            foreignField: '_id',
                            as: 'questionPaper',
                        },
                    },
                    { $unwind: '$questionPaper' },
                    {
                        $group: {
                            _id: {
                                subjectId: '$questionPaper.subjectId',
                                year: { $year: '$downloadDate' },
                                month: { $month: '$downloadDate' },
                                day: { $dayOfMonth: '$downloadDate' },
                            },
                            count: { $sum: 1 },
                        },
                    },
                    {
                        $lookup: {
                            from: 'subjects',
                            localField: '_id.subjectId',
                            foreignField: '_id',
                            as: 'subject',
                        },
                    },
                    { $unwind: '$subject' },
                    {
                        $project: {
                            subjectId: '$_id.subjectId',
                            subjectName: '$subject.name',
                            date: {
                                $dateFromParts: {
                                    year: '$_id.year',
                                    month: '$_id.month',
                                    day: '$_id.day',
                                },
                            },
                            downloaded: '$count',
                            _id: 0,
                        },
                    },
                ])
                    .exec();
                const dateMap = new Map();
                if (filters.startDate && filters.endDate) {
                    const currentDate = new Date(filters.startDate);
                    const endDate = new Date(filters.endDate);
                    while (currentDate <= endDate) {
                        const dateKey = currentDate.toISOString().split('T')[0];
                        dateMap.set(dateKey, {
                            date: new Date(currentDate),
                            subjects: new Map(),
                        });
                        currentDate.setDate(currentDate.getDate() + 1);
                    }
                }
                papersGenerated.forEach((item) => {
                    const dateKey = item.date.toISOString().split('T')[0];
                    const subjectKey = item.subjectId.toString();
                    if (!dateMap.has(dateKey)) {
                        dateMap.set(dateKey, {
                            date: item.date,
                            subjects: new Map(),
                        });
                    }
                    if (!dateMap.get(dateKey).subjects.has(subjectKey)) {
                        dateMap.get(dateKey).subjects.set(subjectKey, {
                            subjectId: item.subjectId,
                            subjectName: item.subjectName,
                            generated: 0,
                            downloaded: 0,
                        });
                    }
                    dateMap.get(dateKey).subjects.get(subjectKey).generated =
                        item.generated;
                });
                papersDownloaded.forEach((item) => {
                    const dateKey = item.date.toISOString().split('T')[0];
                    const subjectKey = item.subjectId.toString();
                    if (!dateMap.has(dateKey)) {
                        dateMap.set(dateKey, {
                            date: item.date,
                            subjects: new Map(),
                        });
                    }
                    if (!dateMap.get(dateKey).subjects.has(subjectKey)) {
                        dateMap.get(dateKey).subjects.set(subjectKey, {
                            subjectId: item.subjectId,
                            subjectName: item.subjectName,
                            generated: 0,
                            downloaded: 0,
                        });
                    }
                    dateMap.get(dateKey).subjects.get(subjectKey).downloaded =
                        item.downloaded;
                });
                const dailyData = Array.from(dateMap.entries())
                    .map(([dateKey, dayData]) => ({
                    date: dayData.date,
                    subjects: Array.from(dayData.subjects.values()).sort((a, b) => a.subjectName.localeCompare(b.subjectName)),
                }))
                    .sort((a, b) => a.date.getTime() - b.date.getTime());
                return {
                    data: dailyData,
                    totalDays: dailyData.length,
                    dateRange: {
                        startDate: filters.startDate || null,
                        endDate: filters.endDate || null,
                    },
                };
            }
            catch (error) {
                this.logger.error(`Error getting question paper stats: ${error.message}`, error.stack);
                throw error;
            }
        });
    }
    async getSubjectWiseAnalytics(collegeId) {
        const cacheKey = `subject_wise_analytics_${collegeId}`;
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log(`Computing subject-wise analytics for collegeId: ${collegeId} (cache miss)`);
            try {
                if (!collegeId || !mongoose_2.Types.ObjectId.isValid(collegeId)) {
                    throw new Error('Invalid collegeId');
                }
                const collegeObjectId = new mongoose_2.Types.ObjectId(collegeId);
                const papersBySubject = await this.questionPaperModel
                    .aggregate([
                    { $match: { collegeId: collegeObjectId } },
                    {
                        $group: {
                            _id: '$subjectId',
                            questionPapersGenerated: { $sum: 1 },
                        },
                    },
                    {
                        $lookup: {
                            from: 'subjects',
                            localField: '_id',
                            foreignField: '_id',
                            as: 'subject',
                        },
                    },
                    { $unwind: '$subject' },
                    {
                        $project: {
                            subjectId: '$_id',
                            subjectName: '$subject.name',
                            questionPapersGenerated: 1,
                            _id: 0,
                        },
                    },
                ])
                    .exec();
                const questionsBySubject = await this.questionModel
                    .aggregate([
                    { $match: { collegeId: collegeObjectId, status: 'active' } },
                    {
                        $group: {
                            _id: '$subjectId',
                            questionsGenerated: { $sum: 1 },
                        },
                    },
                    {
                        $lookup: {
                            from: 'subjects',
                            localField: '_id',
                            foreignField: '_id',
                            as: 'subject',
                        },
                    },
                    { $unwind: '$subject' },
                    {
                        $project: {
                            subjectId: '$_id',
                            subjectName: '$subject.name',
                            questionsGenerated: 1,
                            _id: 0,
                        },
                    },
                ])
                    .exec();
                const downloadsBySubject = await this.downloadModel
                    .aggregate([
                    { $match: { collegeId: collegeObjectId } },
                    {
                        $lookup: {
                            from: 'questionpapers',
                            localField: 'paperId',
                            foreignField: '_id',
                            as: 'questionPaper',
                        },
                    },
                    { $unwind: '$questionPaper' },
                    {
                        $group: {
                            _id: '$questionPaper.subjectId',
                            questionPapersDownloaded: { $sum: 1 },
                        },
                    },
                    {
                        $lookup: {
                            from: 'subjects',
                            localField: '_id',
                            foreignField: '_id',
                            as: 'subject',
                        },
                    },
                    { $unwind: '$subject' },
                    {
                        $project: {
                            subjectId: '$_id',
                            subjectName: '$subject.name',
                            questionPapersDownloaded: 1,
                            _id: 0,
                        },
                    },
                ])
                    .exec();
                const subjectMap = new Map();
                papersBySubject.forEach((item) => {
                    subjectMap.set(item.subjectId.toString(), {
                        subjectId: item.subjectId,
                        subjectName: item.subjectName,
                        questionPapersGenerated: item.questionPapersGenerated,
                        questionsGenerated: 0,
                        questionPapersDownloaded: 0,
                    });
                });
                questionsBySubject.forEach((item) => {
                    const key = item.subjectId.toString();
                    if (subjectMap.has(key)) {
                        subjectMap.get(key).questionsGenerated = item.questionsGenerated;
                    }
                    else {
                        subjectMap.set(key, {
                            subjectId: item.subjectId,
                            subjectName: item.subjectName,
                            questionPapersGenerated: 0,
                            questionsGenerated: item.questionsGenerated,
                            questionPapersDownloaded: 0,
                        });
                    }
                });
                downloadsBySubject.forEach((item) => {
                    const key = item.subjectId.toString();
                    if (subjectMap.has(key)) {
                        subjectMap.get(key).questionPapersDownloaded =
                            item.questionPapersDownloaded;
                    }
                    else {
                        subjectMap.set(key, {
                            subjectId: item.subjectId,
                            subjectName: item.subjectName,
                            questionPapersGenerated: 0,
                            questionsGenerated: 0,
                            questionPapersDownloaded: item.questionPapersDownloaded,
                        });
                    }
                });
                const result = Array.from(subjectMap.values()).sort((a, b) => a.subjectName.localeCompare(b.subjectName));
                return result;
            }
            catch (error) {
                this.logger.error(`Error getting subject-wise analytics: ${error.message}`, error.stack);
                throw error;
            }
        });
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = AnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_activity_schema_1.UserActivity.name)),
    __param(1, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __param(2, (0, mongoose_1.InjectModel)(question_paper_schema_1.QuestionPaper.name)),
    __param(3, (0, mongoose_1.InjectModel)(subject_schema_1.Subject.name)),
    __param(4, (0, mongoose_1.InjectModel)(question_schema_1.Question.name)),
    __param(5, (0, mongoose_1.InjectModel)(download_schema_1.Download.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        services_1.AnalyticsCacheService])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map