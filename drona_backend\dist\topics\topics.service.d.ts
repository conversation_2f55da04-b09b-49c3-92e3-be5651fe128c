import { Model } from 'mongoose';
import { Topic, TopicDocument } from '../schema/topic.schema';
import { SubjectDocument } from '../schema/subject.schema';
import { CreateTopicDto } from './dto/create-topic.dto';
import { UpdateTopicDto } from './dto/update-topic.dto';
export declare class TopicsService {
    private topicModel;
    private subjectModel;
    private readonly logger;
    constructor(topicModel: Model<TopicDocument>, subjectModel: Model<SubjectDocument>);
    create(createTopicDto: CreateTopicDto): Promise<Topic>;
    findAll(): Promise<Topic[]>;
    findBySubject(subjectId: string): Promise<Topic[]>;
    findOne(id: string): Promise<Topic>;
    update(id: string, updateTopicDto: UpdateTopicDto): Promise<Topic>;
    remove(id: string): Promise<void>;
}
