export declare enum QuestionType {
    MCQ = "mcq",
    MULTIPLE_CHOICE = "multiple-choice",
    TRUE_FALSE = "true-false",
    DESCRIPTIVE = "descriptive"
}
export declare enum Difficulty {
    EASY = "easy",
    MEDIUM = "medium",
    HARD = "hard"
}
export declare class FilterQuestionsDto {
    subjectId?: string;
    topicId?: string;
    difficulty?: Difficulty;
    type?: QuestionType;
    search?: string;
    reviewStatus?: 'pending' | 'approved' | 'rejected';
    page?: number;
    limit?: number;
}
