import { TeachersService } from './teachers.service';
import { CreateTeacherDto } from './dto/create-teacher.dto';
import { UpdateTeacherProfileDto } from './dto/update-teacher-profile.dto';
import { UpdateTeacherAdminDto } from './dto/update-teacher-admin.dto';
import { TrackingService } from '../common/services';
export declare class TeachersController {
    private readonly teachersService;
    private readonly trackingService;
    constructor(teachersService: TeachersService, trackingService: TrackingService);
    addTeacherToCollege(collegeId: string, createTeacherDto: CreateTeacherDto): Promise<import("../schema/user.schema").User>;
    findAllTeachersInCollege(collegeId: string): Promise<import("../schema/user.schema").User[]>;
    findOne(id: string): Promise<import("../schema/user.schema").User>;
    updateOwnProfile(user: any, updateTeacherProfileDto: UpdateTeacherProfileDto): Promise<import("../schema/user.schema").User>;
    update(id: string, updateTeacherAdminDto: UpdateTeacherAdminDto): Promise<import("../schema/user.schema").User>;
    remove(id: string): Promise<import("../schema/user.schema").User>;
    getTeacherAnalytics(id: string, startDate?: string, endDate?: string, subjectId?: string): Promise<{
        teacherId: string;
        downloads: any[];
        paperGeneration: any[];
    }>;
}
