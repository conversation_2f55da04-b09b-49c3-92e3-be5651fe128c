import { Response } from 'express';
import { QuestionPapersService } from './question-papers.service';
import { CreateQuestionPaperDto } from './dto/create-question-paper.dto';
import { UpdateQuestionPaperDto } from './dto/update-question-paper.dto';
import { SetQuestionLimitDto } from './dto/set-question-limit.dto';
import { RequestWithUser } from '../auth/interfaces/request-with-user.interface';
import { TrackingService } from '../common/services';
export declare class QuestionPapersController {
    private readonly questionPapersService;
    private readonly trackingService;
    private readonly logger;
    constructor(questionPapersService: QuestionPapersService, trackingService: TrackingService);
    create(req: RequestWithUser, createQuestionPaperDto: CreateQuestionPaperDto): Promise<import("../schema/question-paper.schema").QuestionPaper>;
    findAll(req: RequestWithUser): Promise<(import("mongoose").Document<unknown, {}, import("../schema/question-paper.schema").QuestionPaper> & import("../schema/question-paper.schema").QuestionPaper & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    })[]>;
    findOne(req: RequestWithUser, id: string): Promise<import("mongoose").Document<unknown, {}, import("../schema/question-paper.schema").QuestionPaper> & import("../schema/question-paper.schema").QuestionPaper & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
    download(id: string, format: "pdf" | "docx" | undefined, req: RequestWithUser, res: Response): Promise<void | Response<any, Record<string, any>>>;
    setQuestionLimit(setQuestionLimitDto: SetQuestionLimitDto): Promise<void>;
    update(req: RequestWithUser, id: string, updateQuestionPaperDto: UpdateQuestionPaperDto): Promise<(import("mongoose").Document<unknown, {}, import("../schema/question-paper.schema").QuestionPaper> & import("../schema/question-paper.schema").QuestionPaper & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }) | null>;
}
