"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateQuestionPaperDto = exports.SubjectConfiguration = exports.CustomiseConfig = exports.CustomDifficultyConfig = exports.SubjectShortCode = exports.ExamType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
const mongoose_1 = require("mongoose");
const class_transformer_1 = require("class-transformer");
var ExamType;
(function (ExamType) {
    ExamType["NEET"] = "NEET";
    ExamType["CET"] = "CET";
    ExamType["JEE"] = "JEE";
    ExamType["AIIMS"] = "AIIMS";
    ExamType["JIPMER"] = "JIPMER";
    ExamType["CUSTOM"] = "CUSTOM";
})(ExamType || (exports.ExamType = ExamType = {}));
var SubjectShortCode;
(function (SubjectShortCode) {
    SubjectShortCode["PHYSICS"] = "physics";
    SubjectShortCode["CHEMISTRY"] = "chemistry";
    SubjectShortCode["BIOLOGY"] = "biology";
    SubjectShortCode["MATHEMATICS"] = "mathematics";
    SubjectShortCode["MATH"] = "math";
    SubjectShortCode["PHY"] = "phy";
    SubjectShortCode["CHEM"] = "chem";
    SubjectShortCode["BIO"] = "bio";
})(SubjectShortCode || (exports.SubjectShortCode = SubjectShortCode = {}));
class CustomDifficultyConfig {
}
exports.CustomDifficultyConfig = CustomDifficultyConfig;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of easy questions (0-100)',
        minimum: 0,
        maximum: 100,
        example: 30,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CustomDifficultyConfig.prototype, "easyPercentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of medium questions (0-100)',
        minimum: 0,
        maximum: 100,
        example: 50,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CustomDifficultyConfig.prototype, "mediumPercentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of hard questions (0-100)',
        minimum: 0,
        maximum: 100,
        example: 20,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CustomDifficultyConfig.prototype, "hardPercentage", void 0);
class CustomiseConfig {
}
exports.CustomiseConfig = CustomiseConfig;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Custom difficulty configuration',
        type: CustomDifficultyConfig,
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CustomDifficultyConfig),
    __metadata("design:type", CustomDifficultyConfig)
], CustomiseConfig.prototype, "customDifficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of questions',
        minimum: 1,
        maximum: 200,
        example: 50,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(200),
    __metadata("design:type", Number)
], CustomiseConfig.prototype, "numberOfQuestions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total marks for the paper',
        minimum: 1,
        example: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CustomiseConfig.prototype, "totalMarks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Duration in minutes',
        minimum: 1,
        example: 180,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CustomiseConfig.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Include answers in the generated paper',
        example: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CustomiseConfig.prototype, "includeAnswers", void 0);
class SubjectConfiguration {
}
exports.SubjectConfiguration = SubjectConfiguration;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Subject selection',
        enum: SubjectShortCode,
        example: SubjectShortCode.PHYSICS,
    }),
    (0, class_validator_1.IsEnum)(SubjectShortCode),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], SubjectConfiguration.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of questions for this subject',
        minimum: 1,
        maximum: 200,
        example: 50,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(200),
    __metadata("design:type", Number)
], SubjectConfiguration.prototype, "numberOfQuestions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total marks for this subject',
        minimum: 1,
        example: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], SubjectConfiguration.prototype, "totalMarks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Custom difficulty configuration for this subject',
        type: CustomDifficultyConfig,
    }),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CustomDifficultyConfig),
    __metadata("design:type", CustomDifficultyConfig)
], SubjectConfiguration.prototype, "customDifficulty", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Topic ID for specific topic filtering (optional)',
        example: '60d21b4667d0d8992e610c86',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], SubjectConfiguration.prototype, "topicId", void 0);
class CreateQuestionPaperDto {
}
exports.CreateQuestionPaperDto = CreateQuestionPaperDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The title of the question paper',
        example: 'NEET Physics Mock Test 2024',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateQuestionPaperDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'The description of the question paper',
        example: 'Final examination for Mathematics course',
        required: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateQuestionPaperDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Subject shortcode (physics/chemistry/biology/mathematics) or ObjectId. Required for single subject papers.',
        example: 'physics',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], CreateQuestionPaperDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'ID of the topic (optional)' }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", mongoose_1.Types.ObjectId)
], CreateQuestionPaperDto.prototype, "topicId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Total marks for the paper (for single subject)', minimum: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateQuestionPaperDto.prototype, "totalMarks", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Duration in minutes', minimum: 1 }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], CreateQuestionPaperDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({ description: 'Instructions for the question paper' }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], CreateQuestionPaperDto.prototype, "instructions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Exam type for categorization',
        enum: ExamType,
        example: ExamType.NEET,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(ExamType),
    __metadata("design:type", String)
], CreateQuestionPaperDto.prototype, "examType", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Customization configuration for single subject. If provided, generates customized question paper. If not provided, generates automatic question paper.',
        type: CustomiseConfig,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.ValidateNested)(),
    (0, class_transformer_1.Type)(() => CustomiseConfig),
    __metadata("design:type", CustomiseConfig)
], CreateQuestionPaperDto.prototype, "customise", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Array of subject configurations for multi-subject papers. If provided, creates a multi-subject paper. Minimum 2 subjects required for multi-subject papers.',
        type: [SubjectConfiguration],
        example: [
            {
                subject: 'physics',
                numberOfQuestions: 45,
                totalMarks: 180,
                customDifficulty: {
                    easyPercentage: 30,
                    mediumPercentage: 50,
                    hardPercentage: 20,
                },
            },
            {
                subject: 'chemistry',
                numberOfQuestions: 45,
                totalMarks: 180,
                customDifficulty: {
                    easyPercentage: 25,
                    mediumPercentage: 55,
                    hardPercentage: 20,
                },
            },
            {
                subject: 'biology',
                numberOfQuestions: 90,
                totalMarks: 360,
                customDifficulty: {
                    easyPercentage: 35,
                    mediumPercentage: 45,
                    hardPercentage: 20,
                },
            },
        ],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(2, { message: 'Multi-subject papers must contain at least 2 subjects' }),
    (0, class_validator_1.ValidateNested)({ each: true }),
    (0, class_transformer_1.Type)(() => SubjectConfiguration),
    __metadata("design:type", Array)
], CreateQuestionPaperDto.prototype, "subjects", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Include answers in the generated paper (for multi-subject)',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateQuestionPaperDto.prototype, "includeAnswers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Bypass status checks (active and approved) when selecting questions. Note: This is automatically set to true for question paper generation to allow access to all available questions.',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], CreateQuestionPaperDto.prototype, "bypassStatusChecks", void 0);
//# sourceMappingURL=create-question-paper.dto.js.map