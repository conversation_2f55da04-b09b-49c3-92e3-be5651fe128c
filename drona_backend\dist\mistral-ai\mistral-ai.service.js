"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var MistralAiService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MistralAiService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const question_schema_1 = require("../schema/question.schema");
const topic_schema_1 = require("../schema/topic.schema");
const mistralai_1 = require("@mistralai/mistralai");
let MistralAiService = MistralAiService_1 = class MistralAiService {
    constructor(configService, questionModel, topicModel) {
        this.configService = configService;
        this.questionModel = questionModel;
        this.topicModel = topicModel;
        this.logger = new common_1.Logger(MistralAiService_1.name);
        const mistralApiKey = this.configService.get('MISTRAL_API_KEY');
        if (!mistralApiKey) {
            throw new Error('Mistral API key not configured');
        }
        this.mistralClient = new mistralai_1.Mistral({
            apiKey: mistralApiKey,
        });
    }
    async processPdfWithOCR(file) {
        try {
            const base64Pdf = file.buffer.toString('base64');
            this.logger.log('Processing PDF with Mistral OCR SDK...');
            const ocrResult = await this.mistralClient.ocr.process({
                model: 'mistral-ocr-latest',
                document: {
                    type: 'document_url',
                    documentUrl: `data:application/pdf;base64,${base64Pdf}`,
                },
                includeImageBase64: true,
            });
            this.logger.log(`OCR processing completed. Pages: ${ocrResult.pages?.length || 0}`);
            this.logger.log(`OCR response structure: ${JSON.stringify(Object.keys(ocrResult))}`);
            this.logger.log(`Full OCR response: ${JSON.stringify(ocrResult, null, 2).substring(0, 2000)}...`);
            if (ocrResult.documentAnnotation) {
                this.logger.log(`Document annotation found: ${JSON.stringify(ocrResult.documentAnnotation).substring(0, 200)}...`);
            }
            if (ocrResult.pages) {
                ocrResult.pages.forEach((page, index) => {
                    this.logger.log(`Page ${index} structure: ${JSON.stringify(Object.keys(page))}`);
                    if (page.images) {
                        this.logger.log(`Page ${index} has ${page.images.length} images`);
                    }
                    if (page.markdown) {
                        this.logger.log(`Page ${index} markdown length: ${page.markdown.length}`);
                    }
                    if (page.text) {
                        this.logger.log(`Page ${index} text length: ${page.text.length}`);
                    }
                });
            }
            return ocrResult;
        }
        catch (error) {
            this.logger.error(`Error processing PDF with OCR: ${error.message}`, error.stack);
            throw error;
        }
    }
    async parseQuestionsFromOCR(ocrResult) {
        try {
            this.logger.log('🔍 Starting OCR result parsing...');
            this.logger.log(`OCR result keys: ${Object.keys(ocrResult).join(', ')}`);
            const combinedMarkdown = this.getCombinedMarkdown(ocrResult);
            this.logger.log(`📄 Combined markdown content length: ${combinedMarkdown.length}`);
            this.logger.log(`📄 Combined markdown preview: ${combinedMarkdown ? combinedMarkdown.substring(0, 300) + '...' : 'EMPTY'}`);
            if (!combinedMarkdown || combinedMarkdown.trim().length === 0) {
                throw new Error(`No content found in the PDF. The Mistral AI response contains ${ocrResult.pages?.length || 0} pages, but no extractable markdown content.`);
            }
            this.logger.log('🔄 Starting question parsing from markdown...');
            const questions = this.parseQuestionsFromMarkdown(combinedMarkdown);
            this.logger.log(`✅ Parsed ${questions.length} questions from OCR result`);
            return questions;
        }
        catch (error) {
            this.logger.error(`❌ Error parsing questions from OCR: ${error.message}`, error.stack);
            throw error;
        }
    }
    getCombinedMarkdown(ocrResponse) {
        const markdowns = [];
        if (!ocrResponse.pages) {
            this.logger.warn('No pages found in OCR response');
            return '';
        }
        this.logger.log(`Processing ${ocrResponse.pages.length} pages from OCR response`);
        for (const [pageIndex, page] of ocrResponse.pages.entries()) {
            const imageData = {};
            if (page.images && page.images.length > 0) {
                this.logger.log(`Page ${pageIndex}: Found ${page.images.length} images`);
                for (const [imgIndex, img] of page.images.entries()) {
                    this.logger.log(`Image ${imgIndex} structure: ${JSON.stringify(Object.keys(img))}`);
                    this.logger.log(`Image ${imgIndex} - ID: ${img.id}, has image_base64: ${!!img.image_base64}, has imageBase64: ${!!img.imageBase64}`);
                    const imageId = img.id || `img_${pageIndex}_${imgIndex}`;
                    const base64Data = img.image_base64 || img.imageBase64 || img.base64;
                    if (imageId && base64Data) {
                        imageData[imageId] = base64Data;
                        this.logger.log(`✅ Added image to dictionary: ${imageId}`);
                    }
                    else {
                        this.logger.warn(`❌ Could not extract image data for image ${imgIndex} on page ${pageIndex}`);
                    }
                }
            }
            else {
                this.logger.log(`Page ${pageIndex}: No images found`);
            }
            const pageMarkdown = page.markdown || page.text || page.content || '';
            this.logger.log(`Page ${pageIndex}: Available fields: ${Object.keys(page).join(', ')}`);
            this.logger.log(`Page ${pageIndex}: Markdown length: ${pageMarkdown.length}, Images to replace: ${Object.keys(imageData).length}`);
            if (pageMarkdown.length === 0) {
                this.logger.warn(`Page ${pageIndex}: No text content found in any field`);
            }
            const processedMarkdown = this.replaceImagesInMarkdown(pageMarkdown, imageData);
            if (processedMarkdown.trim()) {
                markdowns.push(processedMarkdown);
                this.logger.log(`Page ${pageIndex}: ✅ Added processed markdown (${processedMarkdown.length} chars)`);
            }
            else {
                this.logger.warn(`Page ${pageIndex}: ❌ No content to add after processing`);
            }
        }
        const combinedContent = markdowns.join('\n\n');
        this.logger.log(`Combined ${markdowns.length} pages of markdown content (total: ${combinedContent.length} chars)`);
        return combinedContent;
    }
    replaceImagesInMarkdown(markdownStr, imagesDict) {
        let processedMarkdown = markdownStr;
        this.logger.log(`Replacing images in markdown. Available images: ${Object.keys(imagesDict).join(', ')}`);
        this.logger.log(`Original markdown preview: ${markdownStr.substring(0, 200)}...`);
        for (const [imgName, base64Str] of Object.entries(imagesDict)) {
            const placeholder = `![${imgName}](${imgName})`;
            const replacement = `![${imgName}](data:image/jpeg;base64,${base64Str})`;
            this.logger.log(`Looking for placeholder: ${placeholder}`);
            const beforeReplace = processedMarkdown;
            processedMarkdown = processedMarkdown.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacement);
            if (beforeReplace !== processedMarkdown) {
                this.logger.log(`Successfully replaced image placeholder for: ${imgName}`);
            }
            else {
                this.logger.warn(`No placeholder found for image: ${imgName}`);
            }
        }
        this.logger.log(`Processed markdown preview: ${processedMarkdown.substring(0, 200)}...`);
        return processedMarkdown;
    }
    parseQuestionBlockAdvanced(block) {
        const optionPattern = this.detectOptionPattern(block);
        if (!optionPattern) {
            return null;
        }
        const optionMatch = block.search(optionPattern.regex);
        if (optionMatch === -1) {
            return null;
        }
        const questionContent = block.substring(0, optionMatch).trim();
        const optionsText = block.substring(optionMatch);
        const options = this.extractOptionsAdvanced(optionsText, optionPattern);
        if (options.length < 2) {
            return null;
        }
        const answer = this.extractAnswerAdvanced(block, optionPattern, options);
        return {
            content: questionContent,
            options,
            answer,
            difficulty: 'medium',
            type: 'multiple-choice',
        };
    }
    detectOptionPattern(block) {
        const characterPatterns = [
            /(?:^|\n)\s*[A-D][\.\)]\s+/i,
            /(?:^|\n)\s*\([A-D]\)\s+/i,
            /(?:^|\n)\s*[A-D]\s*[\-\:]\s+/i,
        ];
        const romanPatterns = [
            /(?:^|\n)\s*(?:i{1,3}|iv)[\.\)]\s+/i,
            /(?:^|\n)\s*\((?:i{1,3}|iv)\)\s+/i,
        ];
        for (const pattern of characterPatterns) {
            if (pattern.test(block)) {
                return { type: 'character', regex: pattern };
            }
        }
        for (const pattern of romanPatterns) {
            if (pattern.test(block)) {
                return { type: 'roman', regex: pattern };
            }
        }
        return null;
    }
    extractOptionsAdvanced(optionsText, optionPattern) {
        const options = [];
        const optionLines = optionsText.split(/\n/);
        for (const line of optionLines) {
            let match = null;
            if (optionPattern.type === 'character') {
                match = line.match(/^\s*(?:[A-D][\.\)\-\:]|\([A-D]\))\s+(.+)/i);
            }
            else if (optionPattern.type === 'roman') {
                match = line.match(/^\s*(?:(?:i{1,3}|iv)[\.\)]|\((?:i{1,3}|iv)\))\s+(.+)/i);
            }
            if (match && match[1]) {
                options.push(match[1].trim());
            }
        }
        return options;
    }
    extractAnswerAdvanced(block, optionPattern, options) {
        let answerPattern;
        if (optionPattern.type === 'character') {
            answerPattern = /(?:Answer|Ans|Correct)\.?\s*:?\s*([A-D])/i;
        }
        else {
            answerPattern = /(?:Answer|Ans|Correct)\.?\s*:?\s*(i{1,3}|iv)/i;
        }
        const answerMatch = block.match(answerPattern);
        if (answerMatch) {
            const answerKey = answerMatch[1].toLowerCase();
            if (optionPattern.type === 'character') {
                const answerIndex = answerKey.toUpperCase().charCodeAt(0) - 'A'.charCodeAt(0);
                return options[answerIndex] || options[0];
            }
            else {
                const romanToIndex = { 'i': 0, 'ii': 1, 'iii': 2, 'iv': 3 };
                const answerIndex = romanToIndex[answerKey] || 0;
                return options[answerIndex] || options[0];
            }
        }
        return options[0];
    }
    parseQuestionsFromMarkdown(markdownContent) {
        this.logger.log(`🔄 Splitting markdown into question blocks...`);
        const questionBlocks = this.splitQuestionsAdvanced(markdownContent);
        const questions = [];
        for (const [blockIndex, block] of questionBlocks.entries()) {
            if (!block.trim())
                continue;
            this.logger.log(`📝 Processing question block ${blockIndex + 1}/${questionBlocks.length}`);
            this.logger.log(`Block preview: ${block.substring(0, 200)}...`);
            try {
                const imageUrls = this.extractImagesFromMarkdown(block);
                this.logger.log(`🖼️ Found ${imageUrls.length} images in block ${blockIndex + 1}`);
                const parsedQuestion = this.parseQuestionBlockAdvanced(block);
                if (parsedQuestion) {
                    if (imageUrls.length > 0) {
                        parsedQuestion.imageUrls = imageUrls;
                    }
                    questions.push(parsedQuestion);
                    this.logger.log(`✅ Successfully parsed question ${questions.length} with ${imageUrls.length} images`);
                }
                else {
                    this.logger.warn(`❌ Failed to parse question structure for block ${blockIndex + 1}`);
                }
            }
            catch (error) {
                this.logger.warn(`❌ Failed to parse question block ${blockIndex + 1}: ${error.message}`);
            }
        }
        this.logger.log(`📊 Final result: Parsed ${questions.length} questions from ${questionBlocks.length} blocks`);
        return questions;
    }
    splitQuestionsAdvanced(markdownContent) {
        const lines = markdownContent.split('\n');
        const questionBlocks = [];
        let currentBlock = '';
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            const isQuestionStart = this.isQuestionStart(line);
            if (isQuestionStart && currentBlock.trim()) {
                questionBlocks.push(currentBlock.trim());
                currentBlock = line;
            }
            else {
                if (currentBlock) {
                    currentBlock += '\n' + line;
                }
                else {
                    currentBlock = line;
                }
            }
        }
        if (currentBlock.trim()) {
            questionBlocks.push(currentBlock.trim());
        }
        this.logger.log(`Split content into ${questionBlocks.length} question blocks`);
        return questionBlocks;
    }
    isQuestionStart(line) {
        const hasImage = /!\[.*?\]\(.*?\)/.test(line);
        const hasNumberPattern = /^\s*\d+[\.\)]\s*/.test(line);
        return hasImage || hasNumberPattern;
    }
    extractImagesFromMarkdown(markdownBlock) {
        const imageRegex = /!\[.*?\]\((data:image\/[^;]+;base64,[^)]+)\)/g;
        const imageUrls = [];
        let match;
        this.logger.log(`Extracting images from block: ${markdownBlock.substring(0, 200)}...`);
        while ((match = imageRegex.exec(markdownBlock)) !== null) {
            imageUrls.push(match[1]);
            this.logger.log(`Found image URL: ${match[1].substring(0, 50)}...`);
        }
        this.logger.log(`Extracted ${imageUrls.length} images from markdown block`);
        return imageUrls;
    }
    async bulkCreateQuestions(parsedQuestions, subjectId, userId, topicId) {
        this.logger.log(`🚀 Starting bulk creation of ${parsedQuestions.length} questions`);
        this.logger.log(`📋 Parameters: subjectId=${subjectId}, userId=${userId}, topicId=${topicId}`);
        const topic = await this.topicModel.findById(topicId).exec();
        if (!topic) {
            throw new common_1.BadRequestException(`Topic with ID ${topicId} not found`);
        }
        if (topic.subjectId.toString() !== subjectId) {
            throw new common_1.BadRequestException(`Topic ${topicId} does not belong to subject ${subjectId}`);
        }
        this.logger.log(`✅ Topic validation passed: ${topic.name} belongs to subject ${subjectId}`);
        try {
            const testCount = await this.questionModel.countDocuments();
            this.logger.log(`📊 Database connection test: Found ${testCount} existing questions`);
        }
        catch (dbError) {
            this.logger.error(`❌ Database connection test failed: ${dbError.message}`);
            throw new Error(`Database connection failed: ${dbError.message}`);
        }
        const result = {
            questionsAdded: 0,
            questionsFailed: 0,
            questions: [],
            errors: [],
        };
        for (const [index, parsedQuestion] of parsedQuestions.entries()) {
            try {
                this.logger.log(`💾 Processing question ${index + 1}/${parsedQuestions.length} for database storage`);
                this.logger.log(`📝 Question content: ${parsedQuestion.content.substring(0, 100)}...`);
                this.logger.log(`📋 Question options: ${JSON.stringify(parsedQuestion.options)}`);
                this.logger.log(`✅ Question answer: ${parsedQuestion.answer}`);
                this.logger.log(`🖼️ Question images: ${parsedQuestion.imageUrls?.length || 0} images`);
                if (!parsedQuestion.content || !parsedQuestion.options || !parsedQuestion.answer) {
                    throw new Error(`Missing required fields: content=${!!parsedQuestion.content}, options=${!!parsedQuestion.options}, answer=${!!parsedQuestion.answer}`);
                }
                if (parsedQuestion.options.length < 2) {
                    throw new Error(`Insufficient options: ${parsedQuestion.options.length} (minimum 2 required)`);
                }
                this.logger.log(`🔍 Validating IDs - subjectId: ${subjectId}, userId: ${userId}`);
                if (!subjectId || !userId) {
                    throw new Error(`Missing required IDs: subjectId=${subjectId}, userId=${userId}`);
                }
                if (!mongoose_2.Types.ObjectId.isValid(subjectId)) {
                    throw new Error(`Invalid subjectId format: ${subjectId}`);
                }
                if (!mongoose_2.Types.ObjectId.isValid(userId)) {
                    throw new Error(`Invalid userId format: ${userId}`);
                }
                if (!mongoose_2.Types.ObjectId.isValid(topicId)) {
                    throw new Error(`Invalid topicId format: ${topicId}`);
                }
                this.logger.log(`🔍 Checking for duplicate content...`);
                const duplicateQuestion = await this.checkForDuplicateQuestion(parsedQuestion.content);
                if (duplicateQuestion) {
                    throw new Error(`A question with similar content already exists in the database (ID: ${duplicateQuestion._id}). Skipping duplicate question.`);
                }
                const questionData = {
                    content: parsedQuestion.content.trim(),
                    options: parsedQuestion.options.filter(opt => opt && opt.trim()),
                    answer: parsedQuestion.answer.trim(),
                    imageUrls: parsedQuestion.imageUrls || [],
                    subjectId: new mongoose_2.Types.ObjectId(subjectId),
                    difficulty: parsedQuestion.difficulty || 'medium',
                    type: parsedQuestion.type || 'multiple-choice',
                    createdBy: new mongoose_2.Types.ObjectId(userId),
                    reviewStatus: 'pending',
                    status: 'inactive',
                    source: 'pdf-ocr',
                };
                questionData.topicId = new mongoose_2.Types.ObjectId(topicId);
                this.logger.log(`📊 Final question data structure:`);
                this.logger.log(`   - Content length: ${questionData.content.length}`);
                this.logger.log(`   - Options count: ${questionData.options.length}`);
                this.logger.log(`   - Answer: ${questionData.answer}`);
                this.logger.log(`   - Images count: ${questionData.imageUrls.length}`);
                this.logger.log(`   - Subject ID: ${questionData.subjectId}`);
                this.logger.log(`   - Topic ID: ${questionData.topicId}`);
                this.logger.log(`   - Created by: ${questionData.createdBy}`);
                this.logger.log(`💾 Creating Mongoose document...`);
                const createdQuestion = new this.questionModel(questionData);
                this.logger.log(`🔄 Validating document before save...`);
                await createdQuestion.validate();
                this.logger.log(`💾 Saving to MongoDB...`);
                const savedQuestion = await createdQuestion.save();
                this.logger.log(`🔍 Verifying question was saved...`);
                const verifyQuestion = await this.questionModel.findById(savedQuestion._id).lean().exec();
                if (!verifyQuestion) {
                    throw new Error(`Question was not found in database after save operation`);
                }
                result.questions.push(savedQuestion);
                result.questionsAdded++;
                this.logger.log(`✅ Successfully created and verified question: ${savedQuestion._id} with ${questionData.imageUrls.length} images`);
                this.logger.log(`📋 Question saved to database: ${verifyQuestion.content.substring(0, 50)}...`);
            }
            catch (error) {
                result.questionsFailed++;
                const errorMessage = `Failed to create question ${index + 1}: ${error.message}`;
                result.errors.push(errorMessage);
                this.logger.error(`❌ ${errorMessage}`);
                this.logger.error(`📋 Error details: ${JSON.stringify({
                    name: error.name,
                    message: error.message,
                    stack: error.stack?.split('\n').slice(0, 3).join('\n')
                })}`);
                this.logger.error(`📋 Failed question data: ${JSON.stringify({
                    contentLength: parsedQuestion.content?.length || 0,
                    optionsCount: parsedQuestion.options?.length || 0,
                    hasAnswer: !!parsedQuestion.answer,
                    imagesCount: parsedQuestion.imageUrls?.length || 0
                })}`);
            }
        }
        this.logger.log(`📊 Bulk creation completed:`);
        this.logger.log(`   ✅ Questions added: ${result.questionsAdded}`);
        this.logger.log(`   ❌ Questions failed: ${result.questionsFailed}`);
        this.logger.log(`   📋 Total errors: ${result.errors.length}`);
        if (result.errors.length > 0) {
            this.logger.error(`❌ Errors encountered:`);
            result.errors.forEach((error, index) => {
                this.logger.error(`   ${index + 1}. ${error}`);
            });
        }
        return result;
    }
    normalizeQuestionContent(content) {
        if (!content || typeof content !== 'string') {
            return '';
        }
        return content
            .replace(/\s+/g, '')
            .replace(/[\n\r]/g, '')
            .replace(/[^\w\d]/g, '')
            .toLowerCase()
            .trim();
    }
    async checkForDuplicateQuestion(content) {
        const normalizedContent = this.normalizeQuestionContent(content);
        const allQuestions = await this.questionModel
            .find({}, { content: 1 })
            .lean()
            .exec();
        for (const question of allQuestions) {
            const existingNormalizedContent = this.normalizeQuestionContent(question.content);
            if (existingNormalizedContent === normalizedContent) {
                return question;
            }
        }
        return null;
    }
};
exports.MistralAiService = MistralAiService;
exports.MistralAiService = MistralAiService = MistralAiService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(1, (0, mongoose_1.InjectModel)(question_schema_1.Question.name)),
    __param(2, (0, mongoose_1.InjectModel)(topic_schema_1.Topic.name)),
    __metadata("design:paramtypes", [config_1.ConfigService,
        mongoose_2.Model,
        mongoose_2.Model])
], MistralAiService);
//# sourceMappingURL=mistral-ai.service.js.map