import { Model } from 'mongoose';
import { User } from '../schema/user.schema';
import { College } from '../schema/college.schema';
import { CreateTeacherDto } from './dto/create-teacher.dto';
import { UpdateTeacherDto } from './dto/update-teacher.dto';
export declare class TeachersService {
    private userModel;
    private collegeModel;
    constructor(userModel: Model<User>, collegeModel: Model<College>);
    addTeacherToCollege(collegeId: string, createTeacherDto: CreateTeacherDto): Promise<User>;
    findAllTeachersInCollege(collegeId: string): Promise<User[]>;
    findOne(id: string): Promise<User>;
    update(id: string, updateTeacherDto: UpdateTeacherDto): Promise<User>;
    remove(id: string): Promise<User>;
}
