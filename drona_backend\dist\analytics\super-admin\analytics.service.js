"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AnalyticsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_activity_schema_1 = require("../../schema/user-activity.schema");
const user_schema_1 = require("../../schema/user.schema");
const question_paper_schema_1 = require("../../schema/question-paper.schema");
const question_schema_1 = require("../../schema/question.schema");
const college_schema_1 = require("../../schema/college.schema");
const download_schema_1 = require("../../schema/download.schema");
const services_1 = require("../../common/services");
let AnalyticsService = AnalyticsService_1 = class AnalyticsService {
    constructor(userActivityModel, userModel, questionPaperModel, questionModel, collegeModel, downloadModel, cacheService) {
        this.userActivityModel = userActivityModel;
        this.userModel = userModel;
        this.questionPaperModel = questionPaperModel;
        this.questionModel = questionModel;
        this.collegeModel = collegeModel;
        this.downloadModel = downloadModel;
        this.cacheService = cacheService;
        this.logger = new common_1.Logger(AnalyticsService_1.name);
        this.CACHE_TTL = 3600000;
    }
    async getPlatformSummary() {
        const cacheKey = 'platform_summary';
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log('Computing platform summary (cache miss)');
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
            const [totalColleges, totalTeachers, totalQuestions, totalPapers, totalDownloads, recentActivity,] = await Promise.all([
                this.collegeModel.countDocuments({ status: 'active' }),
                this.userModel.countDocuments({ role: 'teacher' }),
                this.questionModel.countDocuments({ status: 'active' }),
                this.questionPaperModel.countDocuments({ status: 'published' }),
                this.downloadModel.countDocuments(),
                this.userActivityModel
                    .aggregate([
                    {
                        $match: {
                            timestamp: { $gte: thirtyDaysAgo },
                        },
                    },
                    {
                        $group: {
                            _id: '$activityType',
                            count: { $sum: 1 },
                        },
                    },
                ])
                    .exec(),
            ]);
            const activityMap = recentActivity.reduce((acc, curr) => {
                acc[curr._id] = curr.count;
                return acc;
            }, {});
            const recentDownloads = await this.downloadModel.countDocuments({
                downloadDate: { $gte: thirtyDaysAgo },
            });
            return {
                totalColleges,
                totalTeachers,
                totalQuestions,
                totalPapers,
                totalDownloads,
                recentActivity: {
                    logins: activityMap['login'] || 0,
                    paperGenerations: activityMap['paper_generation'] || 0,
                    questionCreations: activityMap['question_creation'] || 0,
                    downloads: recentDownloads,
                },
            };
        });
    }
    async getTopColleges() {
        const cacheKey = 'top_colleges';
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log('Computing top colleges (cache miss)');
            const [topCollegesByDownloads, topCollegesByTeachers] = await Promise.all([
                this.downloadModel
                    .aggregate([
                    {
                        $group: {
                            _id: '$collegeId',
                            downloadCount: { $sum: 1 },
                        },
                    },
                    { $sort: { downloadCount: -1 } },
                    { $limit: 10 },
                    {
                        $lookup: {
                            from: 'colleges',
                            localField: '_id',
                            foreignField: '_id',
                            as: 'college',
                        },
                    },
                    { $unwind: '$college' },
                    {
                        $project: {
                            _id: 1,
                            name: '$college.name',
                            downloadCount: 1,
                        },
                    },
                ])
                    .exec(),
                this.userModel
                    .aggregate([
                    { $match: { role: 'teacher' } },
                    {
                        $group: {
                            _id: '$collegeId',
                            teacherCount: { $sum: 1 },
                        },
                    },
                    { $sort: { teacherCount: -1 } },
                    { $limit: 10 },
                    {
                        $lookup: {
                            from: 'colleges',
                            localField: '_id',
                            foreignField: '_id',
                            as: 'college',
                        },
                    },
                    { $unwind: '$college' },
                    {
                        $project: {
                            _id: 1,
                            name: '$college.name',
                            teacherCount: 1,
                        },
                    },
                ])
                    .exec(),
            ]);
            return {
                byDownloads: topCollegesByDownloads,
                byTeachers: topCollegesByTeachers,
            };
        });
    }
    async getQuestionUsage() {
        const cacheKey = 'question_usage';
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log('Computing question usage statistics (cache miss)');
            const [usedQuestions, unusedQuestions, questionStats] = await Promise.all([
                this.questionPaperModel
                    .aggregate([
                    { $unwind: '$sections' },
                    { $unwind: '$sections.questions' },
                    {
                        $group: {
                            _id: '$sections.questions.questionId',
                            paperCount: { $sum: 1 },
                            firstUsedIn: { $min: '$createdAt' },
                        },
                    },
                    { $sort: { firstUsedIn: -1 } },
                    { $limit: 20 },
                    {
                        $lookup: {
                            from: 'questions',
                            localField: '_id',
                            foreignField: '_id',
                            as: 'question',
                        },
                    },
                    { $unwind: '$question' },
                    {
                        $project: {
                            _id: 1,
                            content: '$question.content',
                            subjectId: '$question.subjectId',
                            topicId: '$question.topicId',
                            difficulty: '$question.difficulty',
                            paperCount: 1,
                            firstUsedIn: 1,
                        },
                    },
                ])
                    .exec(),
                this.questionModel
                    .aggregate([
                    { $match: { status: 'active' } },
                    {
                        $lookup: {
                            from: 'questionpapers',
                            let: { questionId: '$_id' },
                            pipeline: [
                                { $match: { status: 'published' } },
                                { $unwind: '$sections' },
                                { $unwind: '$sections.questions' },
                                {
                                    $match: {
                                        $expr: {
                                            $eq: [
                                                '$sections.questions.questionId',
                                                '$$questionId',
                                            ],
                                        },
                                    },
                                },
                                { $limit: 1 },
                            ],
                            as: 'usedInPapers',
                        },
                    },
                    {
                        $match: {
                            usedInPapers: { $size: 0 },
                        },
                    },
                    {
                        $project: {
                            _id: 1,
                            content: 1,
                            subjectId: 1,
                            topicId: 1,
                            difficulty: 1,
                            type: 1,
                        },
                    },
                    { $limit: 20 },
                ])
                    .exec(),
                this.questionModel
                    .aggregate([
                    { $match: { status: 'active' } },
                    {
                        $facet: {
                            byDifficulty: [
                                {
                                    $group: {
                                        _id: '$difficulty',
                                        count: { $sum: 1 },
                                    },
                                },
                            ],
                            byType: [
                                {
                                    $group: {
                                        _id: '$type',
                                        count: { $sum: 1 },
                                    },
                                },
                            ],
                        },
                    },
                ])
                    .exec(),
            ]);
            const { byDifficulty, byType } = questionStats[0];
            return {
                usedQuestions,
                unusedQuestions,
                byDifficulty,
                byType,
            };
        });
    }
    async getQuestionStats() {
        const cacheKey = 'question_stats';
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log('Computing question statistics (cache miss)');
            const result = await this.questionModel
                .aggregate([
                { $match: { status: 'active' } },
                {
                    $facet: {
                        totalCount: [{ $count: 'count' }],
                        bySubject: [
                            {
                                $group: {
                                    _id: '$subjectId',
                                    count: { $sum: 1 },
                                },
                            },
                            {
                                $lookup: {
                                    from: 'subjects',
                                    localField: '_id',
                                    foreignField: '_id',
                                    as: 'subject',
                                },
                            },
                            { $unwind: '$subject' },
                            {
                                $project: {
                                    _id: 1,
                                    subjectName: '$subject.name',
                                    count: 1,
                                },
                            },
                            { $sort: { count: -1 } },
                        ],
                        byTopic: [
                            {
                                $group: {
                                    _id: '$topicId',
                                    count: { $sum: 1 },
                                },
                            },
                            {
                                $lookup: {
                                    from: 'topics',
                                    localField: '_id',
                                    foreignField: '_id',
                                    as: 'topic',
                                },
                            },
                            { $unwind: '$topic' },
                            {
                                $project: {
                                    _id: 1,
                                    topicName: '$topic.name',
                                    count: 1,
                                },
                            },
                            { $sort: { count: -1 } },
                        ],
                        byDifficulty: [
                            {
                                $group: {
                                    _id: '$difficulty',
                                    count: { $sum: 1 },
                                },
                            },
                            { $sort: { count: -1 } },
                        ],
                        byType: [
                            {
                                $group: {
                                    _id: '$type',
                                    count: { $sum: 1 },
                                },
                            },
                            { $sort: { count: -1 } },
                        ],
                    },
                },
            ])
                .exec();
            const facetResult = result[0];
            const totalQuestions = facetResult.totalCount.length > 0 ? facetResult.totalCount[0].count : 0;
            return {
                totalQuestions,
                bySubject: facetResult.bySubject,
                byTopic: facetResult.byTopic,
                byDifficulty: facetResult.byDifficulty,
                byType: facetResult.byType,
            };
        });
    }
    async getCollegeAnalytics(filters = {}) {
        const cacheKey = `college_analytics_${JSON.stringify(filters)}`;
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log('Computing college analytics (cache miss)');
            try {
                const limit = filters.limit || 50;
                const sortBy = filters.sortBy || 'totalActivity';
                const thirtyDaysAgo = new Date();
                thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
                const dateFilter = {};
                if (filters.startDate || filters.endDate) {
                    if (filters.startDate)
                        dateFilter.$gte = filters.startDate;
                    if (filters.endDate)
                        dateFilter.$lte = filters.endDate;
                }
                const collegeAnalytics = await this.collegeModel
                    .aggregate([
                    { $match: { status: 'active' } },
                    {
                        $lookup: {
                            from: 'users',
                            let: { collegeId: '$_id' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $eq: ['$collegeId', '$$collegeId'] },
                                        role: 'teacher',
                                    },
                                },
                            ],
                            as: 'teachers',
                        },
                    },
                    {
                        $lookup: {
                            from: 'questions',
                            let: { collegeId: '$_id' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $eq: ['$collegeId', '$$collegeId'] },
                                        status: 'active',
                                    },
                                },
                            ],
                            as: 'questions',
                        },
                    },
                    {
                        $lookup: {
                            from: 'questionpapers',
                            let: { collegeId: '$_id' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $eq: ['$collegeId', '$$collegeId'] },
                                    },
                                },
                            ],
                            as: 'questionPapers',
                        },
                    },
                    {
                        $lookup: {
                            from: 'downloads',
                            let: { collegeId: '$_id' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $eq: ['$collegeId', '$$collegeId'] },
                                    },
                                },
                            ],
                            as: 'downloads',
                        },
                    },
                    {
                        $lookup: {
                            from: 'useractivities',
                            let: { collegeId: '$_id' },
                            pipeline: [
                                {
                                    $match: {
                                        $expr: { $eq: ['$collegeId', '$$collegeId'] },
                                        timestamp: { $gte: thirtyDaysAgo },
                                    },
                                },
                            ],
                            as: 'recentActivities',
                        },
                    },
                    {
                        $project: {
                            collegeId: '$_id',
                            collegeName: '$name',
                            status: '$status',
                            metrics: {
                                teacherCount: { $size: '$teachers' },
                                activeTeachers: {
                                    $size: {
                                        $filter: {
                                            input: '$teachers',
                                            cond: { $eq: ['$$this.status', 'active'] },
                                        },
                                    },
                                },
                                questionCount: { $size: '$questions' },
                                approvedQuestions: {
                                    $size: {
                                        $filter: {
                                            input: '$questions',
                                            cond: { $eq: ['$$this.reviewStatus', 'approved'] },
                                        },
                                    },
                                },
                                pendingQuestions: {
                                    $size: {
                                        $filter: {
                                            input: '$questions',
                                            cond: { $eq: ['$$this.reviewStatus', 'pending'] },
                                        },
                                    },
                                },
                                rejectedQuestions: {
                                    $size: {
                                        $filter: {
                                            input: '$questions',
                                            cond: { $eq: ['$$this.reviewStatus', 'rejected'] },
                                        },
                                    },
                                },
                                paperCount: { $size: '$questionPapers' },
                                downloadCount: { $size: '$downloads' },
                                totalActivity: {
                                    $add: [
                                        { $size: '$questions' },
                                        { $size: '$questionPapers' },
                                        { $size: '$downloads' },
                                    ],
                                },
                            },
                            recentActivity: {
                                last30Days: {
                                    questionsCreated: {
                                        $size: {
                                            $filter: {
                                                input: '$recentActivities',
                                                cond: {
                                                    $eq: ['$$this.activityType', 'question_creation'],
                                                },
                                            },
                                        },
                                    },
                                    papersGenerated: {
                                        $size: {
                                            $filter: {
                                                input: '$recentActivities',
                                                cond: {
                                                    $eq: ['$$this.activityType', 'paper_generation'],
                                                },
                                            },
                                        },
                                    },
                                    downloads: {
                                        $size: {
                                            $filter: {
                                                input: '$downloads',
                                                cond: {
                                                    $gte: ['$$this.downloadDate', thirtyDaysAgo],
                                                },
                                            },
                                        },
                                    },
                                    activeTeachers: {
                                        $size: {
                                            $setUnion: [
                                                {
                                                    $map: {
                                                        input: '$recentActivities',
                                                        as: 'activity',
                                                        in: '$$activity.userId',
                                                    },
                                                },
                                            ],
                                        },
                                    },
                                },
                            },
                            questions: '$questions',
                            questionPapers: '$questionPapers',
                            downloads: '$downloads',
                        },
                    },
                ])
                    .exec();
                for (const college of collegeAnalytics) {
                    college.subjectBreakdown = await this.getCollegeSubjectBreakdown(college.collegeId);
                }
                collegeAnalytics.sort((a, b) => {
                    let aValue, bValue;
                    switch (sortBy) {
                        case 'questionCount':
                            aValue = a.metrics.questionCount;
                            bValue = b.metrics.questionCount;
                            break;
                        case 'downloadCount':
                            aValue = a.metrics.downloadCount;
                            bValue = b.metrics.downloadCount;
                            break;
                        case 'teacherCount':
                            aValue = a.metrics.teacherCount;
                            bValue = b.metrics.teacherCount;
                            break;
                        case 'paperCount':
                            aValue = a.metrics.paperCount;
                            bValue = b.metrics.paperCount;
                            break;
                        default:
                            aValue = a.metrics.totalActivity;
                            bValue = b.metrics.totalActivity;
                    }
                    return bValue - aValue;
                });
                const limitedColleges = collegeAnalytics.slice(0, limit);
                limitedColleges.forEach((college) => {
                    delete college.questions;
                    delete college.questionPapers;
                    delete college.downloads;
                });
                const summary = {
                    totalColleges: collegeAnalytics.length,
                    totalTeachers: collegeAnalytics.reduce((sum, c) => sum + c.metrics.teacherCount, 0),
                    totalQuestions: collegeAnalytics.reduce((sum, c) => sum + c.metrics.questionCount, 0),
                    totalPapers: collegeAnalytics.reduce((sum, c) => sum + c.metrics.paperCount, 0),
                    totalDownloads: collegeAnalytics.reduce((sum, c) => sum + c.metrics.downloadCount, 0),
                };
                return {
                    summary,
                    colleges: limitedColleges,
                    filters: {
                        startDate: filters.startDate || null,
                        endDate: filters.endDate || null,
                        limit,
                        sortBy,
                    },
                };
            }
            catch (error) {
                this.logger.error(`Error getting college analytics: ${error.message}`, error.stack);
                throw error;
            }
        });
    }
    async getCollegeSubjectBreakdown(collegeId) {
        try {
            const subjectBreakdown = await this.questionModel
                .aggregate([
                { $match: { collegeId: collegeId, status: 'active' } },
                {
                    $lookup: {
                        from: 'subjects',
                        localField: 'subjectId',
                        foreignField: '_id',
                        as: 'subject',
                    },
                },
                { $unwind: '$subject' },
                {
                    $group: {
                        _id: {
                            subjectId: '$subject._id',
                            subjectName: '$subject.name',
                        },
                        questionCount: { $sum: 1 },
                    },
                },
                {
                    $lookup: {
                        from: 'questionpapers',
                        let: { subjectId: '$_id.subjectId' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: {
                                        $and: [
                                            { $eq: ['$subjectId', '$$subjectId'] },
                                            { $eq: ['$collegeId', collegeId] },
                                        ],
                                    },
                                },
                            },
                        ],
                        as: 'papers',
                    },
                },
                {
                    $lookup: {
                        from: 'downloads',
                        let: { subjectId: '$_id.subjectId' },
                        pipeline: [
                            {
                                $match: {
                                    $expr: { $eq: ['$collegeId', collegeId] },
                                },
                            },
                            {
                                $lookup: {
                                    from: 'questionpapers',
                                    localField: 'paperId',
                                    foreignField: '_id',
                                    as: 'paper',
                                },
                            },
                            { $unwind: '$paper' },
                            {
                                $match: {
                                    $expr: { $eq: ['$paper.subjectId', '$$subjectId'] },
                                },
                            },
                        ],
                        as: 'downloads',
                    },
                },
                {
                    $project: {
                        _id: 0,
                        subjectId: '$_id.subjectId',
                        subjectName: '$_id.subjectName',
                        questionCount: 1,
                        paperCount: { $size: '$papers' },
                        downloadCount: { $size: '$downloads' },
                    },
                },
                { $sort: { subjectName: 1 } },
            ])
                .exec();
            return subjectBreakdown;
        }
        catch (error) {
            this.logger.error(`Error getting subject breakdown for college ${collegeId}: ${error.message}`, error.stack);
            return [];
        }
    }
    async getUsageTrends(filters = {}) {
        const cacheKey = `usage_trends_${JSON.stringify(filters)}`;
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log('Computing usage trends (cache miss)');
            try {
                let startDate, endDate;
                if (filters.startDate && filters.endDate) {
                    startDate = filters.startDate;
                    endDate = filters.endDate;
                }
                else if (filters.year) {
                    startDate = new Date(filters.year, 0, 1);
                    endDate = new Date(filters.year, 11, 31, 23, 59, 59);
                }
                else {
                    const currentYear = new Date().getFullYear();
                    startDate = new Date(currentYear, 0, 1);
                    endDate = new Date(currentYear, 11, 31, 23, 59, 59);
                }
                const [questionsData, papersData] = await Promise.all([
                    this.questionModel
                        .aggregate([
                        {
                            $match: {
                                createdAt: { $gte: startDate, $lte: endDate },
                                status: 'active',
                            },
                        },
                        {
                            $group: {
                                _id: {
                                    year: { $year: '$createdAt' },
                                    month: { $month: '$createdAt' },
                                },
                                count: { $sum: 1 },
                            },
                        },
                        { $sort: { '_id.year': 1, '_id.month': 1 } },
                    ])
                        .exec(),
                    this.questionPaperModel
                        .aggregate([
                        {
                            $match: {
                                createdAt: { $gte: startDate, $lte: endDate },
                                status: 'published',
                            },
                        },
                        {
                            $group: {
                                _id: {
                                    year: { $year: '$createdAt' },
                                    month: { $month: '$createdAt' },
                                },
                                count: { $sum: 1 },
                            },
                        },
                        { $sort: { '_id.year': 1, '_id.month': 1 } },
                    ])
                        .exec(),
                ]);
                const questionsMap = new Map();
                questionsData.forEach((item) => {
                    const key = `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`;
                    questionsMap.set(key, item.count);
                });
                const papersMap = new Map();
                papersData.forEach((item) => {
                    const key = `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`;
                    papersMap.set(key, item.count);
                });
                const monthlyData = [];
                const currentDate = new Date(startDate);
                while (currentDate <= endDate) {
                    const year = currentDate.getFullYear();
                    const month = currentDate.getMonth() + 1;
                    const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
                    const questionsCreated = questionsMap.get(monthKey) || 0;
                    const papersGenerated = papersMap.get(monthKey) || 0;
                    monthlyData.push({
                        month: monthKey,
                        monthName: currentDate.toLocaleDateString('en-US', {
                            month: 'long',
                            year: 'numeric',
                        }),
                        questionsCreated,
                        papersGenerated,
                        totalUsage: questionsCreated + papersGenerated,
                    });
                    currentDate.setMonth(currentDate.getMonth() + 1);
                }
                const totalQuestionsCreated = monthlyData.reduce((sum, month) => sum + month.questionsCreated, 0);
                const totalPapersGenerated = monthlyData.reduce((sum, month) => sum + month.papersGenerated, 0);
                const totalMonths = monthlyData.length;
                const summary = {
                    totalMonths,
                    totalQuestionsCreated,
                    totalPapersGenerated,
                    averageMonthlyQuestions: totalMonths > 0
                        ? Math.round(totalQuestionsCreated / totalMonths)
                        : 0,
                    averageMonthlyPapers: totalMonths > 0
                        ? Math.round(totalPapersGenerated / totalMonths)
                        : 0,
                };
                return {
                    data: monthlyData,
                    summary,
                    dateRange: {
                        startDate,
                        endDate,
                    },
                };
            }
            catch (error) {
                this.logger.error(`Error getting usage trends: ${error.message}`, error.stack);
                throw error;
            }
        });
    }
    async getCollegeGrowth(filters = {}) {
        const cacheKey = `college_growth_${JSON.stringify(filters)}`;
        return this.cacheService.getOrCompute(cacheKey, async () => {
            this.logger.log('Computing college growth trends (cache miss)');
            try {
                let startDate, endDate;
                if (filters.startDate && filters.endDate) {
                    startDate = filters.startDate;
                    endDate = filters.endDate;
                }
                else if (filters.year) {
                    startDate = new Date(filters.year, 0, 1);
                    endDate = new Date(filters.year, 11, 31, 23, 59, 59);
                }
                else {
                    const currentYear = new Date().getFullYear();
                    startDate = new Date(currentYear, 0, 1);
                    endDate = new Date(currentYear, 11, 31, 23, 59, 59);
                }
                const collegeGrowthData = await this.collegeModel
                    .aggregate([
                    {
                        $match: {
                            createdAt: { $gte: startDate, $lte: endDate },
                        },
                    },
                    {
                        $group: {
                            _id: {
                                year: { $year: '$createdAt' },
                                month: { $month: '$createdAt' },
                            },
                            collegesAdded: { $sum: 1 },
                        },
                    },
                    { $sort: { '_id.year': 1, '_id.month': 1 } },
                ])
                    .exec();
                const growthMap = new Map();
                collegeGrowthData.forEach((item) => {
                    const key = `${item._id.year}-${item._id.month.toString().padStart(2, '0')}`;
                    growthMap.set(key, item.collegesAdded);
                });
                const collegesBeforeStart = await this.collegeModel.countDocuments({
                    createdAt: { $lt: startDate },
                });
                const monthlyData = [];
                const currentDate = new Date(startDate);
                let cumulativeColleges = collegesBeforeStart;
                const monthlyTarget = 6;
                const yearlyTarget = monthlyTarget * 12;
                while (currentDate <= endDate) {
                    const year = currentDate.getFullYear();
                    const month = currentDate.getMonth() + 1;
                    const monthKey = `${year}-${month.toString().padStart(2, '0')}`;
                    const collegesAdded = growthMap.get(monthKey) || 0;
                    cumulativeColleges += collegesAdded;
                    const targetAchievement = monthlyTarget > 0 ? (collegesAdded / monthlyTarget) * 100 : 0;
                    const revenue = this.generateRevenueData(collegesAdded, filters.view);
                    const salesMetrics = this.generateSalesMetrics(collegesAdded, filters.view);
                    monthlyData.push({
                        month: monthKey,
                        monthName: currentDate.toLocaleDateString('en-US', {
                            month: 'long',
                            year: 'numeric',
                        }),
                        collegesAdded,
                        cumulativeColleges,
                        monthlyTarget,
                        targetAchievement: Math.round(targetAchievement * 100) / 100,
                        revenue,
                        salesMetrics,
                    });
                    currentDate.setMonth(currentDate.getMonth() + 1);
                }
                const totalCollegesAdded = monthlyData.reduce((sum, month) => sum + month.collegesAdded, 0);
                const totalMonths = monthlyData.length;
                const averageMonthlyGrowth = totalMonths > 0 ? Math.round(totalCollegesAdded / totalMonths) : 0;
                const totalTargetAchievement = totalMonths > 0
                    ? monthlyData.reduce((sum, month) => sum + month.targetAchievement, 0) / totalMonths
                    : 0;
                const totalRevenue = monthlyData.reduce((sum, month) => sum + month.revenue, 0);
                const summary = {
                    totalMonths,
                    totalCollegesAdded,
                    averageMonthlyGrowth,
                    totalTargetAchievement: Math.round(totalTargetAchievement * 100) / 100,
                    totalRevenue,
                };
                const targets = {
                    monthlyTarget,
                    yearlyTarget,
                    currentProgress: totalTargetAchievement,
                };
                return {
                    data: monthlyData,
                    summary,
                    targets,
                    dateRange: {
                        startDate,
                        endDate,
                    },
                };
            }
            catch (error) {
                this.logger.error(`Error getting college growth trends: ${error.message}`, error.stack);
                throw error;
            }
        });
    }
    generateRevenueData(collegesAdded, view) {
        const baseRevenue = 5000;
        switch (view) {
            case 'revenue':
                return collegesAdded * baseRevenue * 1.2;
            case 'sales':
                return collegesAdded * baseRevenue * 0.8;
            default:
                return collegesAdded * baseRevenue;
        }
    }
    generateSalesMetrics(collegesAdded, view) {
        const baseLeads = collegesAdded * 2.4;
        const conversionRate = view === 'sales' ? 45 : 41.67;
        return {
            conversions: collegesAdded,
            leads: Math.round(baseLeads),
            conversionRate: Math.round(conversionRate * 100) / 100,
        };
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = AnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_activity_schema_1.UserActivity.name)),
    __param(1, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __param(2, (0, mongoose_1.InjectModel)(question_paper_schema_1.QuestionPaper.name)),
    __param(3, (0, mongoose_1.InjectModel)(question_schema_1.Question.name)),
    __param(4, (0, mongoose_1.InjectModel)(college_schema_1.College.name)),
    __param(5, (0, mongoose_1.InjectModel)(download_schema_1.Download.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model,
        services_1.AnalyticsCacheService])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map