"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.addSchemaIndexes = addSchemaIndexes;
const user_schema_1 = require("../../schema/user.schema");
const question_schema_1 = require("../../schema/question.schema");
const question_paper_schema_1 = require("../../schema/question-paper.schema");
const user_activity_schema_1 = require("../../schema/user-activity.schema");
const download_schema_1 = require("../../schema/download.schema");
const college_schema_1 = require("../../schema/college.schema");
function addSchemaIndexes() {
    user_schema_1.UserSchema.index({ role: 1 });
    user_schema_1.UserSchema.index({ collegeId: 1, role: 1 });
    user_schema_1.UserSchema.index({ firebaseUid: 1 }, { sparse: true });
    user_schema_1.UserSchema.index({ status: 1 });
    question_schema_1.QuestionSchema.index({ subjectId: 1 });
    question_schema_1.QuestionSchema.index({ topicId: 1 });
    question_schema_1.QuestionSchema.index({ difficulty: 1 });
    question_schema_1.QuestionSchema.index({ type: 1 });
    question_schema_1.QuestionSchema.index({ status: 1 });
    question_schema_1.QuestionSchema.index({ reviewStatus: 1 });
    question_schema_1.QuestionSchema.index({ collegeId: 1, status: 1 });
    question_schema_1.QuestionSchema.index({ collegeId: 1, subjectId: 1, status: 1 });
    question_schema_1.QuestionSchema.index({ createdBy: 1 });
    question_schema_1.QuestionSchema.index({ createdAt: 1 });
    question_paper_schema_1.QuestionPaperSchema.index({ collegeId: 1 });
    question_paper_schema_1.QuestionPaperSchema.index({ subjectId: 1 });
    question_paper_schema_1.QuestionPaperSchema.index({ generatedBy: 1 });
    question_paper_schema_1.QuestionPaperSchema.index({ status: 1 });
    question_paper_schema_1.QuestionPaperSchema.index({ collegeId: 1, subjectId: 1 });
    question_paper_schema_1.QuestionPaperSchema.index({ collegeId: 1, createdAt: -1 });
    question_paper_schema_1.QuestionPaperSchema.index({ 'sections.questions.questionId': 1 });
    user_activity_schema_1.UserActivitySchema.index({ userId: 1 });
    user_activity_schema_1.UserActivitySchema.index({ collegeId: 1 });
    user_activity_schema_1.UserActivitySchema.index({ activityType: 1 });
    user_activity_schema_1.UserActivitySchema.index({ timestamp: -1 });
    user_activity_schema_1.UserActivitySchema.index({ collegeId: 1, activityType: 1 });
    user_activity_schema_1.UserActivitySchema.index({ collegeId: 1, timestamp: -1 });
    user_activity_schema_1.UserActivitySchema.index({ collegeId: 1, activityType: 1, timestamp: -1 });
    download_schema_1.DownloadSchema.index({ paperId: 1 });
    download_schema_1.DownloadSchema.index({ userId: 1 });
    download_schema_1.DownloadSchema.index({ collegeId: 1 });
    download_schema_1.DownloadSchema.index({ downloadDate: -1 });
    download_schema_1.DownloadSchema.index({ collegeId: 1, downloadDate: -1 });
    download_schema_1.DownloadSchema.index({ downloadFormat: 1 });
    download_schema_1.DownloadSchema.index({ userId: 1, downloadDate: -1 });
    download_schema_1.DownloadSchema.index({ collegeId: 1, userId: 1 });
    college_schema_1.CollegeSchema.index({ status: 1 });
    college_schema_1.CollegeSchema.index({ name: 1 });
}
//# sourceMappingURL=schema-indexes.js.map