import { ConfigService } from '@nestjs/config';
export declare class AnalyticsCacheService {
    private configService;
    private readonly logger;
    private readonly cache;
    private readonly ttl;
    constructor(configService: ConfigService);
    get<T>(key: string): T | null;
    set(key: string, data: any): void;
    delete(key: string): void;
    clear(): void;
    getOrCompute<T>(key: string, fn: () => Promise<T>): Promise<T>;
}
