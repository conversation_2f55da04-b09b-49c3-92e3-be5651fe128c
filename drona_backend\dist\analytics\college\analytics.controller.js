"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const analytics_service_1 = require("./analytics.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../auth/guards/roles.decorator");
const swagger_1 = require("@nestjs/swagger");
let AnalyticsController = class AnalyticsController {
    constructor(analyticsService) {
        this.analyticsService = analyticsService;
    }
    getCollegeSummary(collegeId) {
        return this.analyticsService.getCollegeSummary(collegeId);
    }
    getTeacherActivity(collegeId, page = 1, limit = 100, teacherId, startDate, endDate) {
        return this.analyticsService.getTeacherActivityLogs(collegeId, limit, page, {
            teacherId,
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
        });
    }
    getQuestionPaperStats(collegeId, startDate, endDate) {
        return this.analyticsService.getQuestionPaperDownloadStats(collegeId, {
            startDate: startDate ? new Date(startDate) : undefined,
            endDate: endDate ? new Date(endDate) : undefined,
        });
    }
    getSubjectWiseAnalytics(collegeId) {
        return this.analyticsService.getSubjectWiseAnalytics(collegeId);
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Get)('summary'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get college summary',
        description: 'Returns a summary of college statistics',
    }),
    (0, swagger_1.ApiParam)({
        name: 'collegeId',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns college summary data',
        schema: {
            type: 'object',
            properties: {
                totalTeachers: { type: 'number', example: 45 },
                activeTeachers: { type: 'number', example: 42 },
                totalQuestionPapers: { type: 'number', example: 156 },
                totalQuestions: { type: 'number', example: 2340 },
                totalDownloads: { type: 'number', example: 320 },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('collegeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getCollegeSummary", null);
__decorate([
    (0, common_1.Get)('teachers'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get teacher download history',
        description: 'Returns teachers with their download history organized by subject',
    }),
    (0, swagger_1.ApiParam)({
        name: 'collegeId',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'page',
        description: 'Page number',
        required: false,
        type: Number,
        example: 1,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: 'Items per page',
        required: false,
        type: Number,
        example: 100,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'teacherId',
        description: 'Filter by specific teacher ID',
        required: false,
        type: String,
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        description: 'Filter downloads from this date (ISO string)',
        required: false,
        type: String,
        example: '2023-07-01T00:00:00.000Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        description: 'Filter downloads until this date (ISO string)',
        required: false,
        type: String,
        example: '2023-07-31T23:59:59.999Z',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns teachers with their download history organized by subject',
        schema: {
            type: 'object',
            properties: {
                teachers: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            teacherId: {
                                type: 'string',
                                example: '60d21b4667d0d8992e610c85',
                            },
                            teacherName: { type: 'string', example: 'John Doe' },
                            teacherEmail: { type: 'string', example: '<EMAIL>' },
                            totalDownloads: { type: 'number', example: 25 },
                            subjectWiseDownloads: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        subjectId: {
                                            type: 'string',
                                            example: '60d21b4667d0d8992e610c85',
                                        },
                                        subjectName: { type: 'string', example: 'Mathematics' },
                                        downloadCount: { type: 'number', example: 15 },
                                        lastDownload: {
                                            type: 'string',
                                            format: 'date-time',
                                            example: '2023-07-21T15:30:00.000Z',
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                pagination: {
                    type: 'object',
                    properties: {
                        total: { type: 'number', example: 45 },
                        page: { type: 'number', example: 1 },
                        limit: { type: 'number', example: 100 },
                        pages: { type: 'number', example: 1 },
                    },
                },
                filters: {
                    type: 'object',
                    description: 'Applied filters',
                    properties: {
                        teacherId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                        startDate: { type: 'string', format: 'date-time' },
                        endDate: { type: 'string', format: 'date-time' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('collegeId')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('teacherId')),
    __param(4, (0, common_1.Query)('startDate')),
    __param(5, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number, String, String, String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getTeacherActivity", null);
__decorate([
    (0, common_1.Get)('question-papers'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get question paper statistics',
        description: 'Returns daily-wise statistics for question papers generated and downloaded by subject with optional date filtering',
    }),
    (0, swagger_1.ApiParam)({
        name: 'collegeId',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        description: 'Filter from this date (ISO string)',
        required: false,
        type: String,
        example: '2023-07-01T00:00:00.000Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        description: 'Filter until this date (ISO string)',
        required: false,
        type: String,
        example: '2023-07-31T23:59:59.999Z',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns daily-wise question paper statistics by subject',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            date: {
                                type: 'string',
                                format: 'date-time',
                                example: '2023-07-21T00:00:00.000Z',
                            },
                            subjects: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        subjectId: {
                                            type: 'string',
                                            example: '60d21b4667d0d8992e610c85',
                                        },
                                        subjectName: { type: 'string', example: 'Mathematics' },
                                        generated: { type: 'number', example: 5 },
                                        downloaded: { type: 'number', example: 3 },
                                    },
                                },
                            },
                        },
                    },
                },
                totalDays: { type: 'number', example: 31 },
                dateRange: {
                    type: 'object',
                    properties: {
                        startDate: {
                            type: 'string',
                            format: 'date-time',
                            example: '2023-07-01T00:00:00.000Z',
                        },
                        endDate: {
                            type: 'string',
                            format: 'date-time',
                            example: '2023-07-31T23:59:59.999Z',
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('collegeId')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getQuestionPaperStats", null);
__decorate([
    (0, common_1.Get)('subjects'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get subject-wise analytics',
        description: 'Returns analytics data grouped by subject showing question papers generated, questions generated, and question papers downloaded per subject',
    }),
    (0, swagger_1.ApiParam)({
        name: 'collegeId',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns subject-wise analytics data',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    subjectId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    subjectName: { type: 'string', example: 'Mathematics' },
                    questionPapersGenerated: { type: 'number', example: 15 },
                    questionsGenerated: { type: 'number', example: 120 },
                    questionPapersDownloaded: { type: 'number', example: 8 },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('collegeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getSubjectWiseAnalytics", null);
exports.AnalyticsController = AnalyticsController = __decorate([
    (0, swagger_1.ApiTags)('Analytics - College'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('analytics/college/:collegeId'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('collegeAdmin'),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map