"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeacherActivityDto = void 0;
const swagger_1 = require("@nestjs/swagger");
class TeacherActivityDto {
}
exports.TeacherActivityDto = TeacherActivityDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Teacher display name',
        example: 'John Doe',
    }),
    __metadata("design:type", String)
], TeacherActivityDto.prototype, "displayName", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Teacher email address',
        example: '<EMAIL>',
    }),
    __metadata("design:type", String)
], TeacherActivityDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Last login timestamp',
        example: '2023-07-21T15:30:00.000Z',
    }),
    __metadata("design:type", Date)
], TeacherActivityDto.prototype, "lastLogin", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Account creation timestamp',
        example: '2023-01-15T10:00:00.000Z',
    }),
    __metadata("design:type", Date)
], TeacherActivityDto.prototype, "createdAt", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Account status',
        example: 'active',
        enum: ['active', 'inactive'],
    }),
    __metadata("design:type", String)
], TeacherActivityDto.prototype, "status", void 0);
//# sourceMappingURL=teacher-activity.dto.js.map