"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionUsageModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const question_usage_service_1 = require("./question-usage.service");
const question_usage_schema_1 = require("../schema/question-usage.schema");
let QuestionUsageModule = class QuestionUsageModule {
};
exports.QuestionUsageModule = QuestionUsageModule;
exports.QuestionUsageModule = QuestionUsageModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: question_usage_schema_1.QuestionUsage.name, schema: question_usage_schema_1.QuestionUsageSchema },
            ]),
        ],
        providers: [question_usage_service_1.QuestionUsageService],
        exports: [question_usage_service_1.QuestionUsageService],
    })
], QuestionUsageModule);
//# sourceMappingURL=question-usage.module.js.map