"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SubjectsController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubjectsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const subjects_service_1 = require("./subjects.service");
const create_subject_dto_1 = require("./dto/create-subject.dto");
const update_subject_dto_1 = require("./dto/update-subject.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
let SubjectsController = SubjectsController_1 = class SubjectsController {
    constructor(subjectsService) {
        this.subjectsService = subjectsService;
        this.logger = new common_1.Logger(SubjectsController_1.name);
    }
    create(createSubjectDto) {
        return this.subjectsService.create(createSubjectDto);
    }
    findAll() {
        return this.subjectsService.findAll();
    }
    findAllWithTopics() {
        return this.subjectsService.findAllWithTopics();
    }
    findOne(id) {
        return this.subjectsService.findOne(id);
    }
    update(id, updateSubjectDto) {
        return this.subjectsService.update(id, updateSubjectDto);
    }
    remove(id) {
        return this.subjectsService.remove(id);
    }
};
exports.SubjectsController = SubjectsController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create a new subject',
        description: 'Creates a new subject (super admin only)',
    }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Subject created successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                name: { type: 'string', example: 'Mathematics' },
                description: {
                    type: 'string',
                    example: 'Study of numbers, quantities, and shapes',
                },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiConflictResponse)({
        description: 'Conflict - Subject with this name already exists',
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_subject_dto_1.CreateSubjectDto]),
    __metadata("design:returntype", void 0)
], SubjectsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all subjects',
        description: 'Returns all subjects',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns list of subjects',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    name: { type: 'string', example: 'Mathematics' },
                    description: {
                        type: 'string',
                        example: 'Study of numbers, quantities, and shapes',
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SubjectsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('with-topics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all subjects with topics',
        description: 'Returns all subjects with their associated topics nested',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns list of subjects with topics',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    name: { type: 'string', example: 'Mathematics' },
                    description: {
                        type: 'string',
                        example: 'Study of numbers, quantities, and shapes',
                    },
                    topics: {
                        type: 'array',
                        items: {
                            type: 'object',
                            properties: {
                                _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                                name: { type: 'string', example: 'Calculus' },
                                description: {
                                    type: 'string',
                                    example: 'Branch of mathematics dealing with limits',
                                },
                            },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], SubjectsController.prototype, "findAllWithTopics", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get subject by ID',
        description: 'Returns a specific subject by its ID',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Subject ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the subject',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                name: { type: 'string', example: 'Mathematics' },
                description: {
                    type: 'string',
                    example: 'Study of numbers, quantities, and shapes',
                },
            },
        },
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Subject not found' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], SubjectsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update subject',
        description: 'Updates a subject (super admin only)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Subject ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Subject updated successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                name: { type: 'string', example: 'Mathematics' },
                description: {
                    type: 'string',
                    example: 'Study of numbers, quantities, and shapes',
                },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiConflictResponse)({
        description: 'Conflict - Subject with this name already exists',
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Subject not found' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_subject_dto_1.UpdateSubjectDto]),
    __metadata("design:returntype", void 0)
], SubjectsController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete subject',
        description: 'Deletes a subject (super admin only). Cannot delete if subject has associated topics.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Subject ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({ description: 'Subject deleted successfully' }),
    (0, swagger_1.ApiConflictResponse)({
        description: 'Conflict - Cannot delete subject with associated topics',
    }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Subject not found' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], SubjectsController.prototype, "remove", null);
exports.SubjectsController = SubjectsController = SubjectsController_1 = __decorate([
    (0, swagger_1.ApiTags)('Subjects'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('subjects'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [subjects_service_1.SubjectsService])
], SubjectsController);
//# sourceMappingURL=subjects.controller.js.map