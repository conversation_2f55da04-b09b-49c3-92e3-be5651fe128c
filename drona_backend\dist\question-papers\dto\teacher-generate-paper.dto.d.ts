export declare enum ExamType {
    NEET = "NEET",
    CET = "CET",
    JEE = "JEE",
    AIIMS = "AIIMS",
    JIPMER = "JIPMER",
    CUSTOM = "CUSTOM"
}
export declare enum SubjectShortCode {
    PHYSICS = "physics",
    CHEMISTRY = "chemistry",
    BIOLOGY = "biology",
    MATHEMATICS = "mathematics",
    MATH = "math",
    PHY = "phy",
    CHEM = "chem",
    BIO = "bio"
}
export declare enum DifficultyMode {
    AUTO = "auto",
    CUSTOM = "custom"
}
export declare class CustomDifficultyConfig {
    easyPercentage: number;
    mediumPercentage: number;
    hardPercentage: number;
}
export declare class TeacherGeneratePaperDto {
    title: string;
    examType: ExamType;
    subject: SubjectShortCode;
    difficultyMode: DifficultyMode;
    customDifficulty?: CustomDifficultyConfig;
    numberOfQuestions: number;
    totalMarks: number;
    duration: number;
    includeAnswers: boolean;
    instructions?: string;
    topicId?: string;
}
