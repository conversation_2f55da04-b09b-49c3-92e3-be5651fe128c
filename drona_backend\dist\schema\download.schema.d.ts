import { Document, Schema as MongooseSchema } from 'mongoose';
import { QuestionPaper } from './question-paper.schema';
import { User } from './user.schema';
import { College } from './college.schema';
export type DownloadDocument = Download & Document;
export declare class Download {
    paperId: QuestionPaper;
    userId: User;
    collegeId: College;
    downloadDate: Date;
    downloadFormat: string;
}
export declare const DownloadSchema: MongooseSchema<Download, import("mongoose").Model<Download, any, any, any, Document<unknown, any, Download> & Download & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Download, Document<unknown, {}, import("mongoose").FlatRecord<Download>> & import("mongoose").FlatRecord<Download> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
