import {
  <PERSON><PERSON><PERSON>,
  IsNot<PERSON>mpty,
  IsNumber,
  IsArray,
  IsOptional,
  Min,
  Max,
  IsBoolean,
  IsEnum,
  ValidateNested,
  ArrayMinSize,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Types } from 'mongoose';
import { Type } from 'class-transformer';

export enum ExamType {
  NEET = 'NEET',
  CET = 'CET',
  JEE = 'JEE',
  AIIMS = 'AIIMS',
  JIPMER = 'JIPMER',
  CUSTOM = 'CUSTOM',
}

export enum SubjectShortCode {
  PHYSICS = 'physics',
  CHEMISTRY = 'chemistry',
  BIOLOGY = 'biology',
  MATHEMATICS = 'mathematics',
  MATH = 'math',
  PHY = 'phy',
  CHEM = 'chem',
  BIO = 'bio',
}

export class CustomDifficultyConfig {
  @ApiProperty({
    description: 'Percentage of easy questions (0-100)',
    minimum: 0,
    maximum: 100,
    example: 30,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  easyPercentage: number;

  @ApiProperty({
    description: 'Percentage of medium questions (0-100)',
    minimum: 0,
    maximum: 100,
    example: 50,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  mediumPercentage: number;

  @ApiProperty({
    description: 'Percentage of hard questions (0-100)',
    minimum: 0,
    maximum: 100,
    example: 20,
  })
  @IsNumber()
  @Min(0)
  @Max(100)
  hardPercentage: number;
}

export class CustomiseConfig {
  @ApiProperty({
    description: 'Custom difficulty configuration',
    type: CustomDifficultyConfig,
  })
  @ValidateNested()
  @Type(() => CustomDifficultyConfig)
  customDifficulty: CustomDifficultyConfig;

  @ApiProperty({
    description: 'Number of questions',
    minimum: 1,
    maximum: 200,
    example: 50,
  })
  @IsNumber()
  @Min(1)
  @Max(200)
  numberOfQuestions: number;

  @ApiProperty({
    description: 'Total marks for the paper',
    minimum: 1,
    example: 100,
  })
  @IsNumber()
  @Min(1)
  totalMarks: number;

  @ApiProperty({
    description: 'Duration in minutes',
    minimum: 1,
    example: 180,
  })
  @IsNumber()
  @Min(1)
  duration: number;

  @ApiProperty({
    description: 'Include answers in the generated paper',
    example: true,
  })
  @IsBoolean()
  includeAnswers: boolean;
}

export class SubjectConfiguration {
  @ApiProperty({
    description: 'Subject selection',
    enum: SubjectShortCode,
    example: SubjectShortCode.PHYSICS,
  })
  @IsEnum(SubjectShortCode)
  @IsNotEmpty()
  subject: SubjectShortCode;

  @ApiProperty({
    description: 'Number of questions for this subject',
    minimum: 1,
    maximum: 200,
    example: 50,
  })
  @IsNumber()
  @Min(1)
  @Max(200)
  numberOfQuestions: number;

  @ApiProperty({
    description: 'Total marks for this subject',
    minimum: 1,
    example: 100,
  })
  @IsNumber()
  @Min(1)
  totalMarks: number;

  @ApiProperty({
    description: 'Custom difficulty configuration for this subject',
    type: CustomDifficultyConfig,
  })
  @ValidateNested()
  @Type(() => CustomDifficultyConfig)
  customDifficulty: CustomDifficultyConfig;

  @ApiPropertyOptional({
    description: 'Topic ID for specific topic filtering (optional)',
    example: '60d21b4667d0d8992e610c86',
  })
  @IsOptional()
  @IsString()
  topicId?: string;
}

export class CreateQuestionPaperDto {
  @ApiProperty({
    description: 'The title of the question paper',
    example: 'NEET Physics Mock Test 2024',
  })
  @IsNotEmpty()
  @IsString()
  title: string;

  @ApiProperty({
    description: 'The description of the question paper',
    example: 'Final examination for Mathematics course',
    required: false,
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description:
      'Subject shortcode (physics/chemistry/biology/mathematics) or ObjectId. Required for single subject papers.',
    example: 'physics',
  })
  @IsOptional()
  @IsString()
  subject?: string;

  @ApiPropertyOptional({ description: 'ID of the topic (optional)' })
  @IsOptional()
  @IsString()
  topicId?: Types.ObjectId;

  @ApiPropertyOptional({ description: 'Total marks for the paper (for single subject)', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  totalMarks?: number;

  @ApiPropertyOptional({ description: 'Duration in minutes', minimum: 1 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  duration?: number;

  @ApiPropertyOptional({ description: 'Instructions for the question paper' })
  @IsString()
  @IsOptional()
  instructions?: string;

  // New fields for unified generation
  @ApiPropertyOptional({
    description: 'Exam type for categorization',
    enum: ExamType,
    example: ExamType.NEET,
  })
  @IsOptional()
  @IsEnum(ExamType)
  examType?: ExamType;

  @ApiPropertyOptional({
    description:
      'Customization configuration for single subject. If provided, generates customized question paper. If not provided, generates automatic question paper.',
    type: CustomiseConfig,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => CustomiseConfig)
  customise?: CustomiseConfig;

  // Multi-subject support
  @ApiPropertyOptional({
    description: 'Array of subject configurations for multi-subject papers. If provided, creates a multi-subject paper. Minimum 2 subjects required for multi-subject papers.',
    type: [SubjectConfiguration],
    example: [
      {
        subject: 'physics',
        numberOfQuestions: 45,
        totalMarks: 180,
        customDifficulty: {
          easyPercentage: 30,
          mediumPercentage: 50,
          hardPercentage: 20,
        },
      },
      {
        subject: 'chemistry',
        numberOfQuestions: 45,
        totalMarks: 180,
        customDifficulty: {
          easyPercentage: 25,
          mediumPercentage: 55,
          hardPercentage: 20,
        },
      },
      {
        subject: 'biology',
        numberOfQuestions: 90,
        totalMarks: 360,
        customDifficulty: {
          easyPercentage: 35,
          mediumPercentage: 45,
          hardPercentage: 20,
        },
      },
    ],
  })
  @IsOptional()
  @IsArray()
  @ArrayMinSize(2, { message: 'Multi-subject papers must contain at least 2 subjects' })
  @ValidateNested({ each: true })
  @Type(() => SubjectConfiguration)
  subjects?: SubjectConfiguration[];

  @ApiPropertyOptional({
    description: 'Include answers in the generated paper (for multi-subject)',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  includeAnswers?: boolean;

  @ApiPropertyOptional({
    description: 'Bypass status checks (active and approved) when selecting questions. Note: This is automatically set to true for question paper generation to allow access to all available questions.',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  bypassStatusChecks?: boolean;
}
