"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const common_1 = require("@nestjs/common");
const jwt_1 = require("@nestjs/jwt");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const bcrypt = require("bcrypt");
const user_schema_1 = require("../schema/user.schema");
const firebase_auth_service_1 = require("./firebase-auth.service");
const college_schema_1 = require("../schema/college.schema");
let AuthService = class AuthService {
    constructor(userModel, collegeModel, jwtService, firebaseAuthService) {
        this.userModel = userModel;
        this.collegeModel = collegeModel;
        this.jwtService = jwtService;
        this.firebaseAuthService = firebaseAuthService;
    }
    async validateUser(email, password) {
        const user = await this.userModel.findOne({ email });
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (user.password) {
            const isPasswordValid = await bcrypt.compare(password, user.password);
            if (!isPasswordValid) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            return user;
        }
        throw new common_1.UnauthorizedException('Invalid authentication method');
    }
    async login(loginDto) {
        try {
            let user;
            if (loginDto.firebaseToken) {
                try {
                    const decodedToken = await this.firebaseAuthService.verifyToken(loginDto.firebaseToken);
                    const firebaseUid = String(decodedToken.uid);
                    user = await this.userModel.findOne({ firebaseUid });
                    if (!user) {
                        const firebaseUser = await this.firebaseAuthService.getUserByUid(firebaseUid);
                        const existingUser = await this.userModel.findOne({
                            email: firebaseUser.email,
                        });
                        if (existingUser) {
                            existingUser.firebaseUid = firebaseUid;
                            user = await existingUser.save();
                        }
                        else {
                            if (!firebaseUser.email) {
                                throw new common_1.UnauthorizedException('Firebase user email is not available. Please ensure your Firebase account has a verified email.');
                            }
                            const college = await this.collegeModel
                                .findOne({
                                $or: [
                                    { teachers: { $in: [firebaseUser.email] } },
                                    { admins: { $in: [firebaseUser.email] } }
                                ]
                            })
                                .exec();
                            if (!college) {
                                throw new common_1.UnauthorizedException('Email is not authorized. Please contact your college administrator.');
                            }
                            let userRole = 'teacher';
                            if (college.admins.includes(firebaseUser.email || '')) {
                                userRole = 'collegeAdmin';
                            }
                            user = await this.userModel.create({
                                email: firebaseUser.email,
                                displayName: firebaseUser.displayName || firebaseUser.email,
                                firebaseUid,
                                role: userRole,
                                status: 'active',
                                collegeId: college._id,
                            });
                        }
                    }
                }
                catch (error) {
                    throw new common_1.UnauthorizedException(`Firebase authentication failed: ${error.message}`);
                }
            }
            else if (loginDto.email && loginDto.password) {
                user = await this.validateUser(loginDto.email, loginDto.password);
            }
            else {
                throw new common_1.UnauthorizedException('Invalid login credentials: must provide either Firebase token or email/password');
            }
            user.lastLogin = new Date();
            await user.save();
            const payload = {
                sub: user._id,
                email: user.email,
                role: user.role,
            };
            if (user.role !== 'superAdmin' && user.collegeId) {
                payload.collegeId =
                    typeof user.collegeId === 'string'
                        ? user.collegeId
                        : user.collegeId.toString();
            }
            const userId = user._id
                ? typeof user._id === 'string'
                    ? user._id
                    : user._id.toString()
                : user.id
                    ? typeof user.id === 'string'
                        ? user.id
                        : user.id.toString()
                    : null;
            if (!userId) {
                throw new common_1.UnauthorizedException('User ID is missing or invalid');
            }
            let collegeIdStr = undefined;
            if (user.collegeId) {
                if (typeof user.collegeId === 'string') {
                    collegeIdStr = user.collegeId;
                }
                else {
                    collegeIdStr = user.collegeId.toString();
                }
            }
            return {
                accessToken: this.jwtService.sign(payload),
                user: {
                    id: userId,
                    email: user.email,
                    displayName: user.displayName,
                    role: user.role,
                    collegeId: collegeIdStr,
                },
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Authentication failed: ' + error.message);
        }
    }
    async register(registerDto) {
        const college = await this.collegeModel
            .findOne({
            $or: [
                { teachers: { $in: [registerDto.email] } },
                { admins: { $in: [registerDto.email] } }
            ]
        })
            .exec();
        if (!college) {
            throw new common_1.BadRequestException('Email is not authorized. Please contact your college administrator.');
        }
        const existingUser = await this.userModel.findOne({
            email: registerDto.email,
        });
        if (existingUser) {
            throw new common_1.ConflictException('User is already registered');
        }
        let userRole = registerDto.role;
        if (college.admins.includes(registerDto.email)) {
            userRole = 'collegeAdmin';
        }
        else if (college.teachers.includes(registerDto.email)) {
            userRole = 'teacher';
        }
        const hashedPassword = await bcrypt.hash(registerDto.password, 10);
        const { collegeId, ...restOfRegisterDto } = registerDto;
        const newUser = await this.userModel.create({
            ...restOfRegisterDto,
            role: userRole,
            password: hashedPassword,
            collegeId: college._id,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
            lastLogin: new Date(),
        });
        const payload = {
            sub: newUser._id,
            email: newUser.email,
            role: newUser.role,
        };
        if (newUser.role !== 'superAdmin' && newUser.collegeId) {
            payload.collegeId =
                typeof newUser.collegeId === 'string'
                    ? newUser.collegeId
                    : newUser.collegeId.toString();
        }
        return {
            accessToken: this.jwtService.sign(payload),
            user: {
                id: newUser._id ? newUser._id.toString() : newUser.id?.toString(),
                email: newUser.email,
                displayName: newUser.displayName,
                role: newUser.role,
                collegeId: newUser.collegeId?.toString(),
            },
        };
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __param(1, (0, mongoose_1.InjectModel)(college_schema_1.College.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        jwt_1.JwtService,
        firebase_auth_service_1.FirebaseAuthService])
], AuthService);
//# sourceMappingURL=auth.service.js.map