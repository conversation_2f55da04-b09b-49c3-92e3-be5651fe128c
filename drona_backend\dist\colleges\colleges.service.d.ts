import { Model } from 'mongoose';
import { College } from '../schema/college.schema';
import { CreateCollegeDto } from './dto/create-college.dto';
import { UpdateCollegeDto } from './dto/update-college.dto';
export declare class CollegesService {
    private collegeModel;
    constructor(collegeModel: Model<College>);
    create(createCollegeDto: CreateCollegeDto): Promise<College>;
    findAll(): Promise<College[]>;
    findOne(id: string): Promise<College>;
    update(id: string, updateCollegeDto: UpdateCollegeDto): Promise<College>;
    remove(id: string): Promise<College>;
    findAllPublic(): Promise<Array<{
        _id: string;
        name: string;
        logoUrl: string;
    }>>;
}
