import { Model } from 'mongoose';
import { UserActivityDocument } from '../../schema/user-activity.schema';
import { UserDocument } from '../../schema/user.schema';
import { QuestionPaperDocument } from '../../schema/question-paper.schema';
import { QuestionDocument } from '../../schema/question.schema';
import { SubjectDocument } from '../../schema/subject.schema';
import { DownloadDocument } from '../../schema/download.schema';
import { AnalyticsCacheService } from '../../common/services';
export declare class AnalyticsService {
    private userActivityModel;
    private userModel;
    private questionPaperModel;
    private subjectModel;
    private readonly questionModel;
    private downloadModel;
    private readonly cacheService;
    private readonly logger;
    constructor(userActivityModel: Model<UserActivityDocument>, userModel: Model<UserDocument>, questionPaperModel: Model<QuestionPaperDocument>, subjectModel: Model<SubjectDocument>, questionModel: Model<QuestionDocument>, downloadModel: Model<DownloadDocument>, cacheService: AnalyticsCacheService);
    getCollegeSummary(collegeId: string): Promise<{
        totalTeachers: number;
        totalPapersGenerated: any;
        totalDownloads: number;
    }>;
    getTeacherActivityLogs(collegeId: string, limit?: number, page?: number, filters?: {
        teacherId?: string;
        startDate?: Date;
        endDate?: Date;
    }): Promise<{
        teachers: any[];
        pagination: {
            total: any;
            page: number;
            limit: number;
            pages: number;
        };
        filters: {
            teacherId?: string;
            startDate?: Date;
            endDate?: Date;
        };
    }>;
    getQuestionPaperDownloadStats(collegeId: string, filters?: {
        startDate?: Date;
        endDate?: Date;
    }): Promise<{
        data: {
            date: any;
            subjects: unknown[];
        }[];
        totalDays: number;
        dateRange: {
            startDate: Date | null;
            endDate: Date | null;
        };
    }>;
    getSubjectWiseAnalytics(collegeId: string): Promise<any[]>;
}
