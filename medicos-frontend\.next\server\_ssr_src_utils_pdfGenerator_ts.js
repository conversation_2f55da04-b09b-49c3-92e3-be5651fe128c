"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_utils_pdfGenerator_ts";
exports.ids = ["_ssr_src_utils_pdfGenerator_ts"];
exports.modules = {

/***/ "(ssr)/./src/utils/imageUtils.ts":
/*!*********************************!*\
  !*** ./src/utils/imageUtils.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureDataUrl: () => (/* binding */ ensureDataUrl),\n/* harmony export */   extractImagesFromText: () => (/* binding */ extractImagesFromText),\n/* harmony export */   isBase64Image: () => (/* binding */ isBase64Image),\n/* harmony export */   validateBase64ImageSrc: () => (/* binding */ validateBase64ImageSrc)\n/* harmony export */ });\n/**\n * Utility functions for handling images, including base64 detection and conversion\n */ /**\n * Checks if a string is a base64 encoded image\n */ function isBase64Image(str) {\n    if (!str || typeof str !== 'string') return false;\n    // Check for data URL format\n    const dataUrlPattern = /^data:image\\/(png|jpg|jpeg|gif|webp|svg\\+xml);base64,/i;\n    if (dataUrlPattern.test(str)) return true;\n    // Check for raw base64 (without data URL prefix)\n    // Base64 strings are typically long and contain only valid base64 characters\n    const base64Pattern = /^[A-Za-z0-9+/]*={0,2}$/;\n    return str.length > 100 && base64Pattern.test(str);\n}\n/**\n * Converts a base64 string to a data URL if it's not already one\n * Also handles cases where the data URL prefix is duplicated\n */ function ensureDataUrl(base64String) {\n    if (!base64String) return '';\n    // Handle duplicated data URL prefixes (e.g., \"data:image/jpeg;base64,data:image/jpeg;base64,...\")\n    const duplicatedPrefixPattern = /^(data:image\\/[^;]+;base64,)(data:image\\/[^;]+;base64,)/;\n    if (duplicatedPrefixPattern.test(base64String)) {\n        // Remove the first occurrence of the duplicated prefix\n        base64String = base64String.replace(duplicatedPrefixPattern, '$2');\n    }\n    // If it's already a data URL, return as is\n    if (base64String.startsWith('data:image/')) {\n        return base64String;\n    }\n    // If it's raw base64, add the data URL prefix\n    // Default to PNG if we can't determine the format\n    return `data:image/png;base64,${base64String}`;\n}\n/**\n * Extracts and processes images from text content\n * Returns an object with cleaned text and extracted images\n */ function extractImagesFromText(text) {\n    if (!text) return {\n        cleanText: text,\n        images: []\n    };\n    const images = [];\n    let cleanText = text;\n    // First, look for markdown-style image syntax: ![alt](data:image/type;base64,...)\n    const markdownImagePattern = /!\\[([^\\]]*)\\]\\(([^)]+)\\)/g;\n    const markdownMatches = [\n        ...text.matchAll(markdownImagePattern)\n    ];\n    if (markdownMatches.length > 0) {\n        markdownMatches.forEach((match, index)=>{\n            const fullMatch = match[0];\n            const altText = match[1] || `Image ${images.length + 1}`;\n            let imageSrc = match[2];\n            // Handle cases where the image src might have duplicated prefixes or other issues\n            // Clean up the image source before validation\n            imageSrc = imageSrc.trim();\n            // Check if the src contains base64 data (either with data URL or raw base64)\n            const containsBase64 = imageSrc.includes('base64,') || isBase64Image(imageSrc);\n            if (containsBase64) {\n                const imageId = `extracted-image-markdown-${index}`;\n                const validatedSrc = ensureDataUrl(imageSrc);\n                images.push({\n                    id: imageId,\n                    src: validatedSrc,\n                    alt: altText\n                });\n                // Remove the markdown image syntax from the text\n                cleanText = cleanText.replace(fullMatch, '');\n            }\n        });\n    }\n    // Second, look for complete data URLs (these take priority over raw base64)\n    const dataUrlPattern = /data:image\\/[^;]+;base64,[A-Za-z0-9+/]+=*/g;\n    const dataUrlMatches = cleanText.match(dataUrlPattern); // Use cleanText to avoid already processed markdown images\n    if (dataUrlMatches) {\n        dataUrlMatches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = `extracted-image-dataurl-${index}`;\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: `Extracted image ${images.length + 1}`\n                });\n                // Remove the complete data URL from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Finally, look for raw base64 strings (only if they're not part of already processed content)\n    const rawBase64Pattern = /\\b[A-Za-z0-9+/]{200,}={0,2}\\b/g;\n    const rawBase64Matches = cleanText.match(rawBase64Pattern); // Use cleanText to avoid already processed content\n    if (rawBase64Matches) {\n        rawBase64Matches.forEach((match, index)=>{\n            if (isBase64Image(match)) {\n                const imageId = `extracted-image-raw-${index}`;\n                const imageSrc = ensureDataUrl(match);\n                images.push({\n                    id: imageId,\n                    src: imageSrc,\n                    alt: `Extracted image ${images.length + 1}`\n                });\n                // Remove the raw base64 string from the text\n                cleanText = cleanText.replace(match, '');\n            }\n        });\n    }\n    // Clean up any extra whitespace left after removing base64 strings\n    cleanText = cleanText.replace(/\\s+/g, ' ').trim();\n    return {\n        cleanText,\n        images\n    };\n}\n/**\n * Validates and sanitizes base64 image source\n */ function validateBase64ImageSrc(src) {\n    if (!src || !isBase64Image(src)) return null;\n    try {\n        const dataUrl = ensureDataUrl(src);\n        // Additional validation could be added here\n        return dataUrl;\n    } catch (error) {\n        console.warn('Invalid base64 image source:', error);\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvaW1hZ2VVdGlscy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7O0NBRUMsR0FFRDs7Q0FFQyxHQUNNLFNBQVNBLGNBQWNDLEdBQVc7SUFDdkMsSUFBSSxDQUFDQSxPQUFPLE9BQU9BLFFBQVEsVUFBVSxPQUFPO0lBRTVDLDRCQUE0QjtJQUM1QixNQUFNQyxpQkFBaUI7SUFDdkIsSUFBSUEsZUFBZUMsSUFBSSxDQUFDRixNQUFNLE9BQU87SUFFckMsaURBQWlEO0lBQ2pELDZFQUE2RTtJQUM3RSxNQUFNRyxnQkFBZ0I7SUFDdEIsT0FBT0gsSUFBSUksTUFBTSxHQUFHLE9BQU9ELGNBQWNELElBQUksQ0FBQ0Y7QUFDaEQ7QUFFQTs7O0NBR0MsR0FDTSxTQUFTSyxjQUFjQyxZQUFvQjtJQUNoRCxJQUFJLENBQUNBLGNBQWMsT0FBTztJQUUxQixrR0FBa0c7SUFDbEcsTUFBTUMsMEJBQTBCO0lBQ2hDLElBQUlBLHdCQUF3QkwsSUFBSSxDQUFDSSxlQUFlO1FBQzlDLHVEQUF1RDtRQUN2REEsZUFBZUEsYUFBYUUsT0FBTyxDQUFDRCx5QkFBeUI7SUFDL0Q7SUFFQSwyQ0FBMkM7SUFDM0MsSUFBSUQsYUFBYUcsVUFBVSxDQUFDLGdCQUFnQjtRQUMxQyxPQUFPSDtJQUNUO0lBRUEsOENBQThDO0lBQzlDLGtEQUFrRDtJQUNsRCxPQUFPLENBQUMsc0JBQXNCLEVBQUVBLGNBQWM7QUFDaEQ7QUFFQTs7O0NBR0MsR0FDTSxTQUFTSSxzQkFBc0JDLElBQVk7SUFJaEQsSUFBSSxDQUFDQSxNQUFNLE9BQU87UUFBRUMsV0FBV0Q7UUFBTUUsUUFBUSxFQUFFO0lBQUM7SUFFaEQsTUFBTUEsU0FBMEQsRUFBRTtJQUNsRSxJQUFJRCxZQUFZRDtJQUVoQixrRkFBa0Y7SUFDbEYsTUFBTUcsdUJBQXVCO0lBQzdCLE1BQU1DLGtCQUFrQjtXQUFJSixLQUFLSyxRQUFRLENBQUNGO0tBQXNCO0lBRWhFLElBQUlDLGdCQUFnQlgsTUFBTSxHQUFHLEdBQUc7UUFDOUJXLGdCQUFnQkUsT0FBTyxDQUFDLENBQUNDLE9BQU9DO1lBQzlCLE1BQU1DLFlBQVlGLEtBQUssQ0FBQyxFQUFFO1lBQzFCLE1BQU1HLFVBQVVILEtBQUssQ0FBQyxFQUFFLElBQUksQ0FBQyxNQUFNLEVBQUVMLE9BQU9ULE1BQU0sR0FBRyxHQUFHO1lBQ3hELElBQUlrQixXQUFXSixLQUFLLENBQUMsRUFBRTtZQUV2QixrRkFBa0Y7WUFDbEYsOENBQThDO1lBQzlDSSxXQUFXQSxTQUFTQyxJQUFJO1lBRXhCLDZFQUE2RTtZQUM3RSxNQUFNQyxpQkFBaUJGLFNBQVNHLFFBQVEsQ0FBQyxjQUFjMUIsY0FBY3VCO1lBRXJFLElBQUlFLGdCQUFnQjtnQkFDbEIsTUFBTUUsVUFBVSxDQUFDLHlCQUF5QixFQUFFUCxPQUFPO2dCQUNuRCxNQUFNUSxlQUFldEIsY0FBY2lCO2dCQUVuQ1QsT0FBT2UsSUFBSSxDQUFDO29CQUNWQyxJQUFJSDtvQkFDSkksS0FBS0g7b0JBQ0xJLEtBQUtWO2dCQUNQO2dCQUVBLGlEQUFpRDtnQkFDakRULFlBQVlBLFVBQVVKLE9BQU8sQ0FBQ1ksV0FBVztZQUMzQztRQUNGO0lBQ0Y7SUFFQSw0RUFBNEU7SUFDNUUsTUFBTW5CLGlCQUFpQjtJQUN2QixNQUFNK0IsaUJBQWlCcEIsVUFBVU0sS0FBSyxDQUFDakIsaUJBQWlCLDJEQUEyRDtJQUVuSCxJQUFJK0IsZ0JBQWdCO1FBQ2xCQSxlQUFlZixPQUFPLENBQUMsQ0FBQ0MsT0FBT0M7WUFDN0IsSUFBSXBCLGNBQWNtQixRQUFRO2dCQUN4QixNQUFNUSxVQUFVLENBQUMsd0JBQXdCLEVBQUVQLE9BQU87Z0JBQ2xELE1BQU1HLFdBQVdqQixjQUFjYTtnQkFFL0JMLE9BQU9lLElBQUksQ0FBQztvQkFDVkMsSUFBSUg7b0JBQ0pJLEtBQUtSO29CQUNMUyxLQUFLLENBQUMsZ0JBQWdCLEVBQUVsQixPQUFPVCxNQUFNLEdBQUcsR0FBRztnQkFDN0M7Z0JBRUEsNkNBQTZDO2dCQUM3Q1EsWUFBWUEsVUFBVUosT0FBTyxDQUFDVSxPQUFPO1lBQ3ZDO1FBQ0Y7SUFDRjtJQUVBLCtGQUErRjtJQUMvRixNQUFNZSxtQkFBbUI7SUFDekIsTUFBTUMsbUJBQW1CdEIsVUFBVU0sS0FBSyxDQUFDZSxtQkFBbUIsbURBQW1EO0lBRS9HLElBQUlDLGtCQUFrQjtRQUNwQkEsaUJBQWlCakIsT0FBTyxDQUFDLENBQUNDLE9BQU9DO1lBQy9CLElBQUlwQixjQUFjbUIsUUFBUTtnQkFDeEIsTUFBTVEsVUFBVSxDQUFDLG9CQUFvQixFQUFFUCxPQUFPO2dCQUM5QyxNQUFNRyxXQUFXakIsY0FBY2E7Z0JBRS9CTCxPQUFPZSxJQUFJLENBQUM7b0JBQ1ZDLElBQUlIO29CQUNKSSxLQUFLUjtvQkFDTFMsS0FBSyxDQUFDLGdCQUFnQixFQUFFbEIsT0FBT1QsTUFBTSxHQUFHLEdBQUc7Z0JBQzdDO2dCQUVBLDZDQUE2QztnQkFDN0NRLFlBQVlBLFVBQVVKLE9BQU8sQ0FBQ1UsT0FBTztZQUN2QztRQUNGO0lBQ0Y7SUFFQSxtRUFBbUU7SUFDbkVOLFlBQVlBLFVBQVVKLE9BQU8sQ0FBQyxRQUFRLEtBQUtlLElBQUk7SUFFL0MsT0FBTztRQUFFWDtRQUFXQztJQUFPO0FBQzdCO0FBYUE7O0NBRUMsR0FDTSxTQUFTc0IsdUJBQXVCTCxHQUFXO0lBQ2hELElBQUksQ0FBQ0EsT0FBTyxDQUFDL0IsY0FBYytCLE1BQU0sT0FBTztJQUV4QyxJQUFJO1FBQ0YsTUFBTU0sVUFBVS9CLGNBQWN5QjtRQUM5Qiw0Q0FBNEM7UUFDNUMsT0FBT007SUFDVCxFQUFFLE9BQU9DLE9BQU87UUFDZEMsUUFBUUMsSUFBSSxDQUFDLGdDQUFnQ0Y7UUFDN0MsT0FBTztJQUNUO0FBQ0YiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYWRhcnNcXERlc2t0b3BcXEZMXFxtZWRpY29zXFxtZWRpY29zLWZyb250ZW5kXFxzcmNcXHV0aWxzXFxpbWFnZVV0aWxzLnRzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVXRpbGl0eSBmdW5jdGlvbnMgZm9yIGhhbmRsaW5nIGltYWdlcywgaW5jbHVkaW5nIGJhc2U2NCBkZXRlY3Rpb24gYW5kIGNvbnZlcnNpb25cbiAqL1xuXG4vKipcbiAqIENoZWNrcyBpZiBhIHN0cmluZyBpcyBhIGJhc2U2NCBlbmNvZGVkIGltYWdlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBpc0Jhc2U2NEltYWdlKHN0cjogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGlmICghc3RyIHx8IHR5cGVvZiBzdHIgIT09ICdzdHJpbmcnKSByZXR1cm4gZmFsc2U7XG4gIFxuICAvLyBDaGVjayBmb3IgZGF0YSBVUkwgZm9ybWF0XG4gIGNvbnN0IGRhdGFVcmxQYXR0ZXJuID0gL15kYXRhOmltYWdlXFwvKHBuZ3xqcGd8anBlZ3xnaWZ8d2VicHxzdmdcXCt4bWwpO2Jhc2U2NCwvaTtcbiAgaWYgKGRhdGFVcmxQYXR0ZXJuLnRlc3Qoc3RyKSkgcmV0dXJuIHRydWU7XG4gIFxuICAvLyBDaGVjayBmb3IgcmF3IGJhc2U2NCAod2l0aG91dCBkYXRhIFVSTCBwcmVmaXgpXG4gIC8vIEJhc2U2NCBzdHJpbmdzIGFyZSB0eXBpY2FsbHkgbG9uZyBhbmQgY29udGFpbiBvbmx5IHZhbGlkIGJhc2U2NCBjaGFyYWN0ZXJzXG4gIGNvbnN0IGJhc2U2NFBhdHRlcm4gPSAvXltBLVphLXowLTkrL10qPXswLDJ9JC87XG4gIHJldHVybiBzdHIubGVuZ3RoID4gMTAwICYmIGJhc2U2NFBhdHRlcm4udGVzdChzdHIpO1xufVxuXG4vKipcbiAqIENvbnZlcnRzIGEgYmFzZTY0IHN0cmluZyB0byBhIGRhdGEgVVJMIGlmIGl0J3Mgbm90IGFscmVhZHkgb25lXG4gKiBBbHNvIGhhbmRsZXMgY2FzZXMgd2hlcmUgdGhlIGRhdGEgVVJMIHByZWZpeCBpcyBkdXBsaWNhdGVkXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBlbnN1cmVEYXRhVXJsKGJhc2U2NFN0cmluZzogc3RyaW5nKTogc3RyaW5nIHtcbiAgaWYgKCFiYXNlNjRTdHJpbmcpIHJldHVybiAnJztcblxuICAvLyBIYW5kbGUgZHVwbGljYXRlZCBkYXRhIFVSTCBwcmVmaXhlcyAoZS5nLiwgXCJkYXRhOmltYWdlL2pwZWc7YmFzZTY0LGRhdGE6aW1hZ2UvanBlZztiYXNlNjQsLi4uXCIpXG4gIGNvbnN0IGR1cGxpY2F0ZWRQcmVmaXhQYXR0ZXJuID0gL14oZGF0YTppbWFnZVxcL1teO10rO2Jhc2U2NCwpKGRhdGE6aW1hZ2VcXC9bXjtdKztiYXNlNjQsKS87XG4gIGlmIChkdXBsaWNhdGVkUHJlZml4UGF0dGVybi50ZXN0KGJhc2U2NFN0cmluZykpIHtcbiAgICAvLyBSZW1vdmUgdGhlIGZpcnN0IG9jY3VycmVuY2Ugb2YgdGhlIGR1cGxpY2F0ZWQgcHJlZml4XG4gICAgYmFzZTY0U3RyaW5nID0gYmFzZTY0U3RyaW5nLnJlcGxhY2UoZHVwbGljYXRlZFByZWZpeFBhdHRlcm4sICckMicpO1xuICB9XG5cbiAgLy8gSWYgaXQncyBhbHJlYWR5IGEgZGF0YSBVUkwsIHJldHVybiBhcyBpc1xuICBpZiAoYmFzZTY0U3RyaW5nLnN0YXJ0c1dpdGgoJ2RhdGE6aW1hZ2UvJykpIHtcbiAgICByZXR1cm4gYmFzZTY0U3RyaW5nO1xuICB9XG5cbiAgLy8gSWYgaXQncyByYXcgYmFzZTY0LCBhZGQgdGhlIGRhdGEgVVJMIHByZWZpeFxuICAvLyBEZWZhdWx0IHRvIFBORyBpZiB3ZSBjYW4ndCBkZXRlcm1pbmUgdGhlIGZvcm1hdFxuICByZXR1cm4gYGRhdGE6aW1hZ2UvcG5nO2Jhc2U2NCwke2Jhc2U2NFN0cmluZ31gO1xufVxuXG4vKipcbiAqIEV4dHJhY3RzIGFuZCBwcm9jZXNzZXMgaW1hZ2VzIGZyb20gdGV4dCBjb250ZW50XG4gKiBSZXR1cm5zIGFuIG9iamVjdCB3aXRoIGNsZWFuZWQgdGV4dCBhbmQgZXh0cmFjdGVkIGltYWdlc1xuICovXG5leHBvcnQgZnVuY3Rpb24gZXh0cmFjdEltYWdlc0Zyb21UZXh0KHRleHQ6IHN0cmluZyk6IHtcbiAgY2xlYW5UZXh0OiBzdHJpbmc7XG4gIGltYWdlczogQXJyYXk8eyBpZDogc3RyaW5nOyBzcmM6IHN0cmluZzsgYWx0OiBzdHJpbmcgfT47XG59IHtcbiAgaWYgKCF0ZXh0KSByZXR1cm4geyBjbGVhblRleHQ6IHRleHQsIGltYWdlczogW10gfTtcblxuICBjb25zdCBpbWFnZXM6IEFycmF5PHsgaWQ6IHN0cmluZzsgc3JjOiBzdHJpbmc7IGFsdDogc3RyaW5nIH0+ID0gW107XG4gIGxldCBjbGVhblRleHQgPSB0ZXh0O1xuXG4gIC8vIEZpcnN0LCBsb29rIGZvciBtYXJrZG93bi1zdHlsZSBpbWFnZSBzeW50YXg6ICFbYWx0XShkYXRhOmltYWdlL3R5cGU7YmFzZTY0LC4uLilcbiAgY29uc3QgbWFya2Rvd25JbWFnZVBhdHRlcm4gPSAvIVxcWyhbXlxcXV0qKVxcXVxcKChbXildKylcXCkvZztcbiAgY29uc3QgbWFya2Rvd25NYXRjaGVzID0gWy4uLnRleHQubWF0Y2hBbGwobWFya2Rvd25JbWFnZVBhdHRlcm4pXTtcblxuICBpZiAobWFya2Rvd25NYXRjaGVzLmxlbmd0aCA+IDApIHtcbiAgICBtYXJrZG93bk1hdGNoZXMuZm9yRWFjaCgobWF0Y2gsIGluZGV4KSA9PiB7XG4gICAgICBjb25zdCBmdWxsTWF0Y2ggPSBtYXRjaFswXTtcbiAgICAgIGNvbnN0IGFsdFRleHQgPSBtYXRjaFsxXSB8fCBgSW1hZ2UgJHtpbWFnZXMubGVuZ3RoICsgMX1gO1xuICAgICAgbGV0IGltYWdlU3JjID0gbWF0Y2hbMl07XG5cbiAgICAgIC8vIEhhbmRsZSBjYXNlcyB3aGVyZSB0aGUgaW1hZ2Ugc3JjIG1pZ2h0IGhhdmUgZHVwbGljYXRlZCBwcmVmaXhlcyBvciBvdGhlciBpc3N1ZXNcbiAgICAgIC8vIENsZWFuIHVwIHRoZSBpbWFnZSBzb3VyY2UgYmVmb3JlIHZhbGlkYXRpb25cbiAgICAgIGltYWdlU3JjID0gaW1hZ2VTcmMudHJpbSgpO1xuXG4gICAgICAvLyBDaGVjayBpZiB0aGUgc3JjIGNvbnRhaW5zIGJhc2U2NCBkYXRhIChlaXRoZXIgd2l0aCBkYXRhIFVSTCBvciByYXcgYmFzZTY0KVxuICAgICAgY29uc3QgY29udGFpbnNCYXNlNjQgPSBpbWFnZVNyYy5pbmNsdWRlcygnYmFzZTY0LCcpIHx8IGlzQmFzZTY0SW1hZ2UoaW1hZ2VTcmMpO1xuXG4gICAgICBpZiAoY29udGFpbnNCYXNlNjQpIHtcbiAgICAgICAgY29uc3QgaW1hZ2VJZCA9IGBleHRyYWN0ZWQtaW1hZ2UtbWFya2Rvd24tJHtpbmRleH1gO1xuICAgICAgICBjb25zdCB2YWxpZGF0ZWRTcmMgPSBlbnN1cmVEYXRhVXJsKGltYWdlU3JjKTtcblxuICAgICAgICBpbWFnZXMucHVzaCh7XG4gICAgICAgICAgaWQ6IGltYWdlSWQsXG4gICAgICAgICAgc3JjOiB2YWxpZGF0ZWRTcmMsXG4gICAgICAgICAgYWx0OiBhbHRUZXh0XG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFJlbW92ZSB0aGUgbWFya2Rvd24gaW1hZ2Ugc3ludGF4IGZyb20gdGhlIHRleHRcbiAgICAgICAgY2xlYW5UZXh0ID0gY2xlYW5UZXh0LnJlcGxhY2UoZnVsbE1hdGNoLCAnJyk7XG4gICAgICB9XG4gICAgfSk7XG4gIH1cblxuICAvLyBTZWNvbmQsIGxvb2sgZm9yIGNvbXBsZXRlIGRhdGEgVVJMcyAodGhlc2UgdGFrZSBwcmlvcml0eSBvdmVyIHJhdyBiYXNlNjQpXG4gIGNvbnN0IGRhdGFVcmxQYXR0ZXJuID0gL2RhdGE6aW1hZ2VcXC9bXjtdKztiYXNlNjQsW0EtWmEtejAtOSsvXSs9Ki9nO1xuICBjb25zdCBkYXRhVXJsTWF0Y2hlcyA9IGNsZWFuVGV4dC5tYXRjaChkYXRhVXJsUGF0dGVybik7IC8vIFVzZSBjbGVhblRleHQgdG8gYXZvaWQgYWxyZWFkeSBwcm9jZXNzZWQgbWFya2Rvd24gaW1hZ2VzXG5cbiAgaWYgKGRhdGFVcmxNYXRjaGVzKSB7XG4gICAgZGF0YVVybE1hdGNoZXMuZm9yRWFjaCgobWF0Y2gsIGluZGV4KSA9PiB7XG4gICAgICBpZiAoaXNCYXNlNjRJbWFnZShtYXRjaCkpIHtcbiAgICAgICAgY29uc3QgaW1hZ2VJZCA9IGBleHRyYWN0ZWQtaW1hZ2UtZGF0YXVybC0ke2luZGV4fWA7XG4gICAgICAgIGNvbnN0IGltYWdlU3JjID0gZW5zdXJlRGF0YVVybChtYXRjaCk7XG5cbiAgICAgICAgaW1hZ2VzLnB1c2goe1xuICAgICAgICAgIGlkOiBpbWFnZUlkLFxuICAgICAgICAgIHNyYzogaW1hZ2VTcmMsXG4gICAgICAgICAgYWx0OiBgRXh0cmFjdGVkIGltYWdlICR7aW1hZ2VzLmxlbmd0aCArIDF9YFxuICAgICAgICB9KTtcblxuICAgICAgICAvLyBSZW1vdmUgdGhlIGNvbXBsZXRlIGRhdGEgVVJMIGZyb20gdGhlIHRleHRcbiAgICAgICAgY2xlYW5UZXh0ID0gY2xlYW5UZXh0LnJlcGxhY2UobWF0Y2gsICcnKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfVxuXG4gIC8vIEZpbmFsbHksIGxvb2sgZm9yIHJhdyBiYXNlNjQgc3RyaW5ncyAob25seSBpZiB0aGV5J3JlIG5vdCBwYXJ0IG9mIGFscmVhZHkgcHJvY2Vzc2VkIGNvbnRlbnQpXG4gIGNvbnN0IHJhd0Jhc2U2NFBhdHRlcm4gPSAvXFxiW0EtWmEtejAtOSsvXXsyMDAsfT17MCwyfVxcYi9nO1xuICBjb25zdCByYXdCYXNlNjRNYXRjaGVzID0gY2xlYW5UZXh0Lm1hdGNoKHJhd0Jhc2U2NFBhdHRlcm4pOyAvLyBVc2UgY2xlYW5UZXh0IHRvIGF2b2lkIGFscmVhZHkgcHJvY2Vzc2VkIGNvbnRlbnRcblxuICBpZiAocmF3QmFzZTY0TWF0Y2hlcykge1xuICAgIHJhd0Jhc2U2NE1hdGNoZXMuZm9yRWFjaCgobWF0Y2gsIGluZGV4KSA9PiB7XG4gICAgICBpZiAoaXNCYXNlNjRJbWFnZShtYXRjaCkpIHtcbiAgICAgICAgY29uc3QgaW1hZ2VJZCA9IGBleHRyYWN0ZWQtaW1hZ2UtcmF3LSR7aW5kZXh9YDtcbiAgICAgICAgY29uc3QgaW1hZ2VTcmMgPSBlbnN1cmVEYXRhVXJsKG1hdGNoKTtcblxuICAgICAgICBpbWFnZXMucHVzaCh7XG4gICAgICAgICAgaWQ6IGltYWdlSWQsXG4gICAgICAgICAgc3JjOiBpbWFnZVNyYyxcbiAgICAgICAgICBhbHQ6IGBFeHRyYWN0ZWQgaW1hZ2UgJHtpbWFnZXMubGVuZ3RoICsgMX1gXG4gICAgICAgIH0pO1xuXG4gICAgICAgIC8vIFJlbW92ZSB0aGUgcmF3IGJhc2U2NCBzdHJpbmcgZnJvbSB0aGUgdGV4dFxuICAgICAgICBjbGVhblRleHQgPSBjbGVhblRleHQucmVwbGFjZShtYXRjaCwgJycpO1xuICAgICAgfVxuICAgIH0pO1xuICB9XG5cbiAgLy8gQ2xlYW4gdXAgYW55IGV4dHJhIHdoaXRlc3BhY2UgbGVmdCBhZnRlciByZW1vdmluZyBiYXNlNjQgc3RyaW5nc1xuICBjbGVhblRleHQgPSBjbGVhblRleHQucmVwbGFjZSgvXFxzKy9nLCAnICcpLnRyaW0oKTtcblxuICByZXR1cm4geyBjbGVhblRleHQsIGltYWdlcyB9O1xufVxuXG4vKipcbiAqIENvbXBvbmVudCBwcm9wcyBmb3IgcmVuZGVyaW5nIGJhc2U2NCBpbWFnZXMgc2FmZWx5XG4gKi9cbmV4cG9ydCBpbnRlcmZhY2UgQmFzZTY0SW1hZ2VQcm9wcyB7XG4gIHNyYzogc3RyaW5nO1xuICBhbHQ/OiBzdHJpbmc7XG4gIGNsYXNzTmFtZT86IHN0cmluZztcbiAgbWF4V2lkdGg/OiBudW1iZXI7XG4gIG1heEhlaWdodD86IG51bWJlcjtcbn1cblxuLyoqXG4gKiBWYWxpZGF0ZXMgYW5kIHNhbml0aXplcyBiYXNlNjQgaW1hZ2Ugc291cmNlXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiB2YWxpZGF0ZUJhc2U2NEltYWdlU3JjKHNyYzogc3RyaW5nKTogc3RyaW5nIHwgbnVsbCB7XG4gIGlmICghc3JjIHx8ICFpc0Jhc2U2NEltYWdlKHNyYykpIHJldHVybiBudWxsO1xuICBcbiAgdHJ5IHtcbiAgICBjb25zdCBkYXRhVXJsID0gZW5zdXJlRGF0YVVybChzcmMpO1xuICAgIC8vIEFkZGl0aW9uYWwgdmFsaWRhdGlvbiBjb3VsZCBiZSBhZGRlZCBoZXJlXG4gICAgcmV0dXJuIGRhdGFVcmw7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS53YXJuKCdJbnZhbGlkIGJhc2U2NCBpbWFnZSBzb3VyY2U6JywgZXJyb3IpO1xuICAgIHJldHVybiBudWxsO1xuICB9XG59XG4iXSwibmFtZXMiOlsiaXNCYXNlNjRJbWFnZSIsInN0ciIsImRhdGFVcmxQYXR0ZXJuIiwidGVzdCIsImJhc2U2NFBhdHRlcm4iLCJsZW5ndGgiLCJlbnN1cmVEYXRhVXJsIiwiYmFzZTY0U3RyaW5nIiwiZHVwbGljYXRlZFByZWZpeFBhdHRlcm4iLCJyZXBsYWNlIiwic3RhcnRzV2l0aCIsImV4dHJhY3RJbWFnZXNGcm9tVGV4dCIsInRleHQiLCJjbGVhblRleHQiLCJpbWFnZXMiLCJtYXJrZG93bkltYWdlUGF0dGVybiIsIm1hcmtkb3duTWF0Y2hlcyIsIm1hdGNoQWxsIiwiZm9yRWFjaCIsIm1hdGNoIiwiaW5kZXgiLCJmdWxsTWF0Y2giLCJhbHRUZXh0IiwiaW1hZ2VTcmMiLCJ0cmltIiwiY29udGFpbnNCYXNlNjQiLCJpbmNsdWRlcyIsImltYWdlSWQiLCJ2YWxpZGF0ZWRTcmMiLCJwdXNoIiwiaWQiLCJzcmMiLCJhbHQiLCJkYXRhVXJsTWF0Y2hlcyIsInJhd0Jhc2U2NFBhdHRlcm4iLCJyYXdCYXNlNjRNYXRjaGVzIiwidmFsaWRhdGVCYXNlNjRJbWFnZVNyYyIsImRhdGFVcmwiLCJlcnJvciIsImNvbnNvbGUiLCJ3YXJuIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/imageUtils.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/pdfGenerator.ts":
/*!***********************************!*\
  !*** ./src/utils/pdfGenerator.ts ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PDFGenerator: () => (/* binding */ PDFGenerator),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(ssr)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(ssr)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/imageUtils */ \"(ssr)/./src/utils/imageUtils.ts\");\n\n\n\nclass PDFGenerator {\n    constructor(){\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]('p', 'mm', 'a4');\n        this.pageWidth = this.doc.internal.pageSize.getWidth();\n        this.pageHeight = this.doc.internal.pageSize.getHeight();\n        this.margin = 20;\n        this.currentY = this.margin;\n        // Single column layout - full width\n        this.contentWidth = this.pageWidth - this.margin * 2;\n        // Set default font to Times\n        try {\n            this.doc.setFont('times', 'normal');\n        } catch (error) {\n            console.warn('Times font not available, using default');\n            this.doc.setFont('helvetica', 'normal');\n        }\n    }\n    addWatermark() {\n        try {\n            // Save current graphics state\n            this.doc.saveGraphicsState();\n            // Set low opacity for watermark\n            this.doc.setGState(this.doc.GState({\n                opacity: 0.08\n            }));\n            // Calculate center position\n            const centerX = this.pageWidth / 2;\n            const centerY = this.pageHeight / 2;\n            // Add watermark text with rotation\n            this.doc.setFontSize(60);\n            this.doc.setFont('times', 'bold');\n            this.doc.setTextColor(150, 150, 150);\n            // Rotate and add watermark text\n            this.doc.text('MEDICOS', centerX, centerY, {\n                angle: 45,\n                align: 'center'\n            });\n            // Restore graphics state\n            this.doc.restoreGraphicsState();\n            // Reset text color to black\n            this.doc.setTextColor(0, 0, 0);\n        } catch (error) {\n            console.warn('Could not add watermark:', error);\n        }\n    }\n    addCollegeHeader(collegeInfo) {\n        if (!collegeInfo) return;\n        // Add college logo if available (Medicos logo in top right)\n        try {\n            this.doc.setFontSize(8);\n            this.doc.setFont('times', 'normal');\n            this.doc.setTextColor(100, 100, 100);\n            this.doc.text('MEDICOS', this.pageWidth - this.margin - 15, this.currentY + 3, {\n                align: 'center'\n            });\n            this.doc.setTextColor(0, 0, 0); // Reset to black\n        } catch (error) {\n            console.warn('Could not add logo:', error);\n        }\n        this.doc.setFontSize(14);\n        this.doc.setFont('times', 'bold');\n        // College name\n        if (collegeInfo.name) {\n            this.doc.text(collegeInfo.name, this.pageWidth / 2, this.currentY, {\n                align: 'center'\n            });\n            this.currentY += 6;\n        }\n        // College address\n        if (collegeInfo.address) {\n            this.doc.setFontSize(10);\n            this.doc.setFont('times', 'normal');\n            this.doc.text(collegeInfo.address, this.pageWidth / 2, this.currentY, {\n                align: 'center'\n            });\n            this.currentY += 6;\n        }\n        // Minimal space after college info\n        this.currentY += 3;\n    }\n    async addImageToPDF(imageSrc, x, y, maxWidth = 40, maxHeight = 30) {\n        try {\n            // Ensure the image has proper data URL format\n            const dataUrl = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__.ensureDataUrl)(imageSrc);\n            // Add image to PDF with smaller size for better layout\n            this.doc.addImage(dataUrl, 'JPEG', x, y, maxWidth, maxHeight);\n            // Return the height used\n            return maxHeight + 2; // Add small margin\n        } catch (error) {\n            console.warn('Failed to add image to PDF:', error);\n            // Add placeholder text for failed images\n            this.doc.setFontSize(8);\n            this.doc.setTextColor(150, 150, 150);\n            this.doc.text('[Image]', x, y + 5);\n            this.doc.setTextColor(0, 0, 0);\n            return 8; // Return small height for placeholder\n        }\n    }\n    processTextWithImages(text) {\n        try {\n            // Use the same image extraction logic as the admin question bank\n            const { cleanText, images } = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__.extractImagesFromText)(text);\n            // Process the clean text for LaTeX rendering\n            const processedText = this.renderLatex(cleanText);\n            return {\n                cleanText: processedText,\n                images: images\n            };\n        } catch (error) {\n            console.warn('Text processing error:', error);\n            return {\n                cleanText: text,\n                images: []\n            };\n        }\n    }\n    renderLatex(text) {\n        try {\n            // Handle common LaTeX patterns - enhanced version\n            let processedText = text;\n            // Replace common LaTeX symbols with Unicode equivalents\n            const latexReplacements = {\n                // Remove LaTeX formatting\n                '\\\\mathrm{': '',\n                '\\\\text{': '',\n                '\\\\rm{': '',\n                // Greek letters\n                '\\\\phi': 'φ',\n                '\\\\Phi': 'Φ',\n                '\\\\alpha': 'α',\n                '\\\\beta': 'β',\n                '\\\\gamma': 'γ',\n                '\\\\Gamma': 'Γ',\n                '\\\\delta': 'δ',\n                '\\\\Delta': 'Δ',\n                '\\\\epsilon': 'ε',\n                '\\\\varepsilon': 'ε',\n                '\\\\zeta': 'ζ',\n                '\\\\eta': 'η',\n                '\\\\theta': 'θ',\n                '\\\\Theta': 'Θ',\n                '\\\\iota': 'ι',\n                '\\\\kappa': 'κ',\n                '\\\\lambda': 'λ',\n                '\\\\Lambda': 'Λ',\n                '\\\\mu': 'μ',\n                '\\\\nu': 'ν',\n                '\\\\xi': 'ξ',\n                '\\\\Xi': 'Ξ',\n                '\\\\pi': 'π',\n                '\\\\Pi': 'Π',\n                '\\\\rho': 'ρ',\n                '\\\\sigma': 'σ',\n                '\\\\Sigma': 'Σ',\n                '\\\\tau': 'τ',\n                '\\\\upsilon': 'υ',\n                '\\\\Upsilon': 'Υ',\n                '\\\\chi': 'χ',\n                '\\\\psi': 'ψ',\n                '\\\\Psi': 'Ψ',\n                '\\\\omega': 'ω',\n                '\\\\Omega': 'Ω',\n                // Arrows\n                '\\\\rightarrow': '→',\n                '\\\\leftarrow': '←',\n                '\\\\leftrightarrow': '↔',\n                '\\\\Rightarrow': '⇒',\n                '\\\\Leftarrow': '⇐',\n                '\\\\Leftrightarrow': '⇔',\n                '\\\\uparrow': '↑',\n                '\\\\downarrow': '↓',\n                // Mathematical operators\n                '\\\\pm': '±',\n                '\\\\mp': '∓',\n                '\\\\times': '×',\n                '\\\\div': '÷',\n                '\\\\cdot': '·',\n                '\\\\ast': '*',\n                '\\\\star': '⋆',\n                '\\\\circ': '∘',\n                '\\\\bullet': '•',\n                // Relations\n                '\\\\leq': '≤',\n                '\\\\geq': '≥',\n                '\\\\neq': '≠',\n                '\\\\equiv': '≡',\n                '\\\\approx': '≈',\n                '\\\\cong': '≅',\n                '\\\\sim': '∼',\n                '\\\\propto': '∝',\n                '\\\\parallel': '∥',\n                '\\\\perp': '⊥',\n                // Set theory\n                '\\\\in': '∈',\n                '\\\\notin': '∉',\n                '\\\\subset': '⊂',\n                '\\\\supset': '⊃',\n                '\\\\subseteq': '⊆',\n                '\\\\supseteq': '⊇',\n                '\\\\cup': '∪',\n                '\\\\cap': '∩',\n                '\\\\emptyset': '∅',\n                // Calculus\n                '\\\\infty': '∞',\n                '\\\\sum': 'Σ',\n                '\\\\prod': 'Π',\n                '\\\\int': '∫',\n                '\\\\oint': '∮',\n                '\\\\partial': '∂',\n                '\\\\nabla': '∇',\n                // Logic\n                '\\\\land': '∧',\n                '\\\\lor': '∨',\n                '\\\\neg': '¬',\n                '\\\\forall': '∀',\n                '\\\\exists': '∃',\n                // Remove dollar signs and braces\n                '$': '',\n                '{': '',\n                '}': ''\n            };\n            // Apply replacements\n            for (const [latex, replacement] of Object.entries(latexReplacements)){\n                processedText = processedText.replace(new RegExp(latex.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replacement);\n            }\n            // Handle subscripts (convert to Unicode subscripts where possible)\n            const subscriptMap = {\n                '0': '₀',\n                '1': '₁',\n                '2': '₂',\n                '3': '₃',\n                '4': '₄',\n                '5': '₅',\n                '6': '₆',\n                '7': '₇',\n                '8': '₈',\n                '9': '₉',\n                '+': '₊',\n                '-': '₋',\n                '=': '₌',\n                '(': '₍',\n                ')': '₎',\n                'a': 'ₐ',\n                'e': 'ₑ',\n                'h': 'ₕ',\n                'i': 'ᵢ',\n                'j': 'ⱼ',\n                'k': 'ₖ',\n                'l': 'ₗ',\n                'm': 'ₘ',\n                'n': 'ₙ',\n                'o': 'ₒ',\n                'p': 'ₚ',\n                'r': 'ᵣ',\n                's': 'ₛ',\n                't': 'ₜ',\n                'u': 'ᵤ',\n                'v': 'ᵥ',\n                'x': 'ₓ'\n            };\n            // Handle superscripts (convert to Unicode superscripts where possible)\n            const superscriptMap = {\n                '0': '⁰',\n                '1': '¹',\n                '2': '²',\n                '3': '³',\n                '4': '⁴',\n                '5': '⁵',\n                '6': '⁶',\n                '7': '⁷',\n                '8': '⁸',\n                '9': '⁹',\n                '+': '⁺',\n                '-': '⁻',\n                '=': '⁼',\n                '(': '⁽',\n                ')': '⁾',\n                'a': 'ᵃ',\n                'b': 'ᵇ',\n                'c': 'ᶜ',\n                'd': 'ᵈ',\n                'e': 'ᵉ',\n                'f': 'ᶠ',\n                'g': 'ᵍ',\n                'h': 'ʰ',\n                'i': 'ⁱ',\n                'j': 'ʲ',\n                'k': 'ᵏ',\n                'l': 'ˡ',\n                'm': 'ᵐ',\n                'n': 'ⁿ',\n                'o': 'ᵒ',\n                'p': 'ᵖ',\n                'r': 'ʳ',\n                's': 'ˢ',\n                't': 'ᵗ',\n                'u': 'ᵘ',\n                'v': 'ᵛ',\n                'w': 'ʷ',\n                'x': 'ˣ',\n                'y': 'ʸ',\n                'z': 'ᶻ'\n            };\n            // Process subscripts\n            processedText = processedText.replace(/_{([^}]+)}/g, (match, content)=>{\n                return content.split('').map((char)=>subscriptMap[char] || char).join('');\n            });\n            // Process superscripts\n            processedText = processedText.replace(/\\^{([^}]+)}/g, (match, content)=>{\n                return content.split('').map((char)=>superscriptMap[char] || char).join('');\n            });\n            // Handle fractions (basic conversion)\n            processedText = processedText.replace(/\\\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)');\n            // Handle square roots\n            processedText = processedText.replace(/\\\\sqrt{([^}]+)}/g, '√($1)');\n            // Clean up any remaining braces\n            processedText = processedText.replace(/[{}]/g, '');\n            return processedText;\n        } catch (error) {\n            console.warn('LaTeX rendering error:', error);\n            return text; // Return original text if rendering fails\n        }\n    }\n    addTitle(title) {\n        this.doc.setFontSize(16);\n        this.doc.setFont('times', 'bold');\n        // Add underline effect\n        const titleWidth = this.doc.getTextWidth(title);\n        const titleX = (this.pageWidth - titleWidth) / 2;\n        this.doc.text(title, this.pageWidth / 2, this.currentY, {\n            align: 'center'\n        });\n        // Draw underline\n        this.doc.line(titleX, this.currentY + 1, titleX + titleWidth, this.currentY + 1);\n        this.currentY += 8;\n    }\n    addExamDetails(questionPaper) {\n        this.doc.setFontSize(11);\n        this.doc.setFont('times', 'normal');\n        // Subject (for single subject papers)\n        if (questionPaper.subjectId?.name && !questionPaper.isMultiSubject) {\n            this.doc.text(`Subject: ${questionPaper.subjectId.name}`, this.pageWidth / 2, this.currentY, {\n                align: 'center'\n            });\n            this.currentY += 6;\n        }\n        // Duration and marks - centered layout\n        const detailsText = `Duration: ${questionPaper.duration} minutes | Total Marks: ${questionPaper.totalMarks}`;\n        this.doc.text(detailsText, this.pageWidth / 2, this.currentY, {\n            align: 'center'\n        });\n        this.currentY += 8;\n        // Instructions\n        if (questionPaper.instructions) {\n            this.doc.setFont('times', 'bold');\n            this.doc.text('Instructions:', this.margin, this.currentY);\n            this.currentY += 5;\n            this.doc.setFont('times', 'normal');\n            this.doc.setFontSize(9);\n            const instructionLines = this.doc.splitTextToSize(questionPaper.instructions, this.contentWidth);\n            this.doc.text(instructionLines, this.margin, this.currentY);\n            this.currentY += instructionLines.length * 3 + 6;\n        }\n        // Add horizontal line\n        this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);\n        this.currentY += 6;\n    }\n    checkPageBreak(requiredHeight) {\n        // More aggressive page break - only break when absolutely necessary\n        return this.currentY + requiredHeight > this.pageHeight - 10;\n    }\n    addNewPage(collegeInfo, subjectName) {\n        this.doc.addPage();\n        this.addWatermark();\n        // Always add college header on new pages\n        this.currentY = this.margin;\n        if (collegeInfo) {\n            this.addCollegeHeader(collegeInfo);\n        }\n        // Add subject header if this is a multi-subject paper\n        if (subjectName) {\n            this.addSubjectHeader(subjectName);\n        }\n    }\n    addSubjectHeader(subjectName) {\n        this.doc.setFontSize(14);\n        this.doc.setFont('times', 'bold');\n        this.doc.text(subjectName, this.pageWidth / 2, this.currentY, {\n            align: 'center'\n        });\n        this.currentY += 6;\n        // Add horizontal line under subject name\n        this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);\n        this.currentY += 8;\n    }\n    async addQuestion(question, questionNumber, withAnswers, collegeInfo, subjectName) {\n        // Estimate required height for this question\n        const estimatedHeight = this.estimateQuestionHeight(question, withAnswers);\n        // More intelligent page break - try to fit the question first\n        if (this.checkPageBreak(estimatedHeight)) {\n            // Check if we can fit at least the question text and first option\n            const minHeight = 15; // Minimum space needed for question start\n            if (this.currentY + minHeight > this.pageHeight - 8) {\n                this.addNewPage(collegeInfo, subjectName);\n            }\n        }\n        // Process question content with images\n        const questionContent = this.processTextWithImages(question.content);\n        // Question number and text\n        this.doc.setFontSize(11);\n        this.doc.setFont('times', 'bold');\n        this.doc.text(`${questionNumber}.`, this.margin, this.currentY);\n        // Question content\n        this.doc.setFont('times', 'normal');\n        const questionText = questionContent.cleanText;\n        const questionLines = this.doc.splitTextToSize(questionText, this.contentWidth - 12);\n        this.doc.text(questionLines, this.margin + 12, this.currentY);\n        this.currentY += Math.max(questionLines.length * 4, 5) + 2;\n        // Add question images if any\n        if (questionContent.images.length > 0) {\n            for (const image of questionContent.images){\n                const imageHeight = await this.addImageToPDF(image.src, this.margin + 12, this.currentY, 50, 35);\n                this.currentY += imageHeight;\n            }\n        }\n        // Options with compact formatting\n        if (question.options && question.options.length > 0) {\n            this.doc.setFontSize(10);\n            this.doc.setFont('times', 'normal');\n            for(let optIndex = 0; optIndex < question.options.length; optIndex++){\n                const option = question.options[optIndex];\n                const optionLabel = String.fromCharCode(97 + optIndex); // a, b, c, d\n                // Check if we have space for this option, if not, continue on new page\n                if (this.currentY > this.pageHeight - 20) {\n                    this.addNewPage(collegeInfo, subjectName);\n                }\n                // Process option content with images\n                const optionContent = this.processTextWithImages(option);\n                // Option label and text on same line\n                this.doc.text(`${optionLabel})`, this.margin + 15, this.currentY);\n                // Option text\n                const optionLines = this.doc.splitTextToSize(optionContent.cleanText, this.contentWidth - 25);\n                this.doc.text(optionLines, this.margin + 25, this.currentY);\n                this.currentY += Math.max(optionLines.length * 4, 4) + 1;\n                // Add option images if any\n                if (optionContent.images.length > 0) {\n                    for (const image of optionContent.images){\n                        const imageHeight = await this.addImageToPDF(image.src, this.margin + 25, this.currentY, 40, 30);\n                        this.currentY += imageHeight;\n                    }\n                }\n            }\n        }\n        // Answer (if included) - Bold black text\n        if (withAnswers && question.answer) {\n            this.currentY += 2; // Minimal space before answer\n            this.doc.setFont('times', 'bold');\n            this.doc.setTextColor(0, 0, 0);\n            const answerContent = this.processTextWithImages(question.answer);\n            // Answer label and text\n            this.doc.text('Answer:', this.margin + 15, this.currentY);\n            // Answer text\n            const answerLines = this.doc.splitTextToSize(answerContent.cleanText, this.contentWidth - 40);\n            this.doc.text(answerLines, this.margin + 40, this.currentY);\n            this.currentY += Math.max(answerLines.length * 4, 4) + 1;\n            // Add answer images if any\n            if (answerContent.images.length > 0) {\n                for (const image of answerContent.images){\n                    const imageHeight = await this.addImageToPDF(image.src, this.margin + 40, this.currentY, 40, 30);\n                    this.currentY += imageHeight;\n                }\n            }\n            // Reset to normal font\n            this.doc.setFont('times', 'normal');\n            this.doc.setTextColor(0, 0, 0);\n        }\n        // Minimal spacing after question\n        this.currentY += 4;\n    }\n    async addQuestionWithSolutions(question, questionNumber, withAnswers, withSolutions, withHints, collegeInfo, subjectName) {\n        // Estimate required height for this question with solutions\n        const estimatedHeight = this.estimateQuestionHeightWithSolutions(question, withAnswers, withSolutions, withHints);\n        // Check for page break\n        if (this.checkPageBreak(estimatedHeight)) {\n            const minHeight = 20; // Minimum space needed for question start with solutions\n            if (this.currentY + minHeight > this.pageHeight - 8) {\n                this.addNewPage(collegeInfo, subjectName);\n            }\n        }\n        // First add the basic question\n        await this.addQuestion(question, questionNumber, withAnswers, collegeInfo, subjectName);\n        // Add solutions if requested and available\n        if (withSolutions && question.solution) {\n            this.currentY += 2;\n            this.doc.setFont('times', 'bold');\n            this.doc.setTextColor(0, 0, 0);\n            this.doc.text('Solution:', this.margin + 15, this.currentY);\n            this.currentY += 5;\n            // Add methodology if available\n            if (question.solution.methodology) {\n                this.doc.setFont('times', 'italic');\n                this.doc.text('Methodology:', this.margin + 20, this.currentY);\n                this.doc.setFont('times', 'normal');\n                const methodologyLines = this.doc.splitTextToSize(question.solution.methodology, this.contentWidth - 50);\n                this.doc.text(methodologyLines, this.margin + 20, this.currentY + 4);\n                this.currentY += Math.max(methodologyLines.length * 4, 4) + 3;\n            }\n            // Add solution steps\n            if (question.solution.steps && question.solution.steps.length > 0) {\n                this.doc.setFont('times', 'italic');\n                this.doc.text('Steps:', this.margin + 20, this.currentY);\n                this.currentY += 4;\n                question.solution.steps.forEach((step, index)=>{\n                    this.doc.setFont('times', 'normal');\n                    const stepText = `${index + 1}. ${step}`;\n                    const stepLines = this.doc.splitTextToSize(stepText, this.contentWidth - 55);\n                    this.doc.text(stepLines, this.margin + 25, this.currentY);\n                    this.currentY += Math.max(stepLines.length * 4, 4) + 2;\n                });\n            }\n            // Add key concepts\n            if (question.solution.key_concepts && question.solution.key_concepts.length > 0) {\n                this.doc.setFont('times', 'italic');\n                this.doc.text('Key Concepts:', this.margin + 20, this.currentY);\n                this.doc.setFont('times', 'normal');\n                const conceptsText = question.solution.key_concepts.join(', ');\n                const conceptsLines = this.doc.splitTextToSize(conceptsText, this.contentWidth - 50);\n                this.doc.text(conceptsLines, this.margin + 20, this.currentY + 4);\n                this.currentY += Math.max(conceptsLines.length * 4, 4) + 3;\n            }\n            // Add final explanation\n            if (question.solution.final_explanation) {\n                this.doc.setFont('times', 'italic');\n                this.doc.text('Explanation:', this.margin + 20, this.currentY);\n                this.doc.setFont('times', 'normal');\n                const explanationLines = this.doc.splitTextToSize(question.solution.final_explanation, this.contentWidth - 50);\n                this.doc.text(explanationLines, this.margin + 20, this.currentY + 4);\n                this.currentY += Math.max(explanationLines.length * 4, 4) + 3;\n            }\n        }\n        // Add hints if requested and available\n        if (withHints && question.hints && question.hints.length > 0) {\n            this.currentY += 2;\n            this.doc.setFont('times', 'bold');\n            this.doc.setTextColor(0, 0, 0);\n            this.doc.text('Hints:', this.margin + 15, this.currentY);\n            this.currentY += 5;\n            question.hints.forEach((hint, index)=>{\n                this.doc.setFont('times', 'normal');\n                const hintText = `• ${hint}`;\n                const hintLines = this.doc.splitTextToSize(hintText, this.contentWidth - 50);\n                this.doc.text(hintLines, this.margin + 20, this.currentY);\n                this.currentY += Math.max(hintLines.length * 4, 4) + 2;\n            });\n        }\n        // Reset font and add spacing\n        this.doc.setFont('times', 'normal');\n        this.doc.setTextColor(0, 0, 0);\n        this.currentY += 6;\n    }\n    estimateQuestionHeight(question, withAnswers) {\n        let height = 10; // Base question height (further reduced)\n        // Add height for options (more conservative)\n        if (question.options && question.options.length > 0) {\n            height += question.options.length * 4; // Reduced from 5 to 4\n        }\n        // Add height for answer if included\n        if (withAnswers && question.answer) {\n            height += 6; // Reduced from 10 to 6\n        }\n        // Add minimal height for potential images (only if content suggests images)\n        const hasImages = question.content.includes('base64') || question.options && question.options.some((opt)=>opt.includes('base64')) || question.answer && question.answer.includes('base64');\n        if (hasImages) {\n            height += 20; // Only add image height if images are likely present\n        } else {\n            height += 5; // Minimal buffer for text-only questions\n        }\n        return height;\n    }\n    estimateQuestionHeightWithSolutions(question, withAnswers, withSolutions, withHints) {\n        let height = this.estimateQuestionHeight(question, withAnswers);\n        // Add height for solutions if included\n        if (withSolutions && question.solution) {\n            height += 15; // Base solution height\n            if (question.solution.methodology) {\n                height += 8;\n            }\n            if (question.solution.steps && question.solution.steps.length > 0) {\n                height += question.solution.steps.length * 6;\n            }\n            if (question.solution.key_concepts && question.solution.key_concepts.length > 0) {\n                height += 8;\n            }\n            if (question.solution.final_explanation) {\n                height += 12;\n            }\n        }\n        // Add height for hints if included\n        if (withHints && question.hints && question.hints.length > 0) {\n            height += 10 + question.hints.length * 6;\n        }\n        return height;\n    }\n    async generatePDF(questionPaper, collegeInfo) {\n        try {\n            console.log(\"PDF Generator - Starting generation with:\", {\n                questionPaper: questionPaper,\n                collegeInfo: collegeInfo\n            });\n            // Check if this is a multi-subject paper\n            const isMultiSubject = questionPaper.isMultiSubject && questionPaper.sections && questionPaper.sections.length > 1;\n            if (isMultiSubject) {\n                // Handle multi-subject papers - separate page for each subject\n                await this.generateMultiSubjectPDF(questionPaper, collegeInfo);\n            } else {\n                // Handle single subject papers\n                await this.generateSingleSubjectPDF(questionPaper, collegeInfo);\n            }\n            // Generate and return blob\n            const pdfBlob = this.doc.output('blob');\n            return pdfBlob;\n        } catch (error) {\n            console.error('PDF generation error:', error);\n            throw new Error('Failed to generate PDF: ' + error.message);\n        }\n    }\n    async generateSingleSubjectPDF(questionPaper, collegeInfo) {\n        // Add watermark to first page\n        this.addWatermark();\n        // Always add college header on first page\n        if (collegeInfo) {\n            this.addCollegeHeader(collegeInfo);\n        }\n        // Add title\n        this.addTitle(questionPaper.title);\n        // Add exam details\n        this.addExamDetails(questionPaper);\n        // Add questions\n        console.log(\"Adding questions to PDF:\", questionPaper.questions.length);\n        for(let index = 0; index < questionPaper.questions.length; index++){\n            const question = questionPaper.questions[index];\n            console.log(`Processing question ${index + 1}:`, question);\n            try {\n                if (questionPaper.withSolutions || questionPaper.withHints) {\n                    await this.addQuestionWithSolutions(question, index + 1, questionPaper.withAnswers, questionPaper.withSolutions || false, questionPaper.withHints || false, collegeInfo);\n                } else {\n                    await this.addQuestion(question, index + 1, questionPaper.withAnswers, collegeInfo);\n                }\n            } catch (error) {\n                console.error(`Error processing question ${index + 1}:`, error);\n            // Continue with next question\n            }\n        }\n    }\n    async generateMultiSubjectPDF(questionPaper, collegeInfo) {\n        if (!questionPaper.sections) return;\n        let overallQuestionNumber = 1;\n        for(let sectionIndex = 0; sectionIndex < questionPaper.sections.length; sectionIndex++){\n            const section = questionPaper.sections[sectionIndex];\n            const subjectName = section.subjectName || section.name || `Subject ${sectionIndex + 1}`;\n            // Add new page for each subject (except the first one)\n            if (sectionIndex === 0) {\n                // First page setup\n                this.addWatermark();\n                if (collegeInfo) {\n                    this.addCollegeHeader(collegeInfo);\n                }\n                this.addTitle(questionPaper.title);\n                this.addExamDetails(questionPaper);\n                this.addSubjectHeader(subjectName);\n            } else {\n                // New page for subsequent subjects\n                this.addNewPage(collegeInfo, subjectName);\n            }\n            // Add questions for this section\n            if (section.questions && section.questions.length > 0) {\n                console.log(`Adding questions for ${subjectName}:`, section.questions.length);\n                for (const questionItem of section.questions){\n                    const question = questionItem.question;\n                    console.log(`Processing question ${overallQuestionNumber}:`, question);\n                    try {\n                        if (questionPaper.withSolutions || questionPaper.withHints) {\n                            await this.addQuestionWithSolutions(question, overallQuestionNumber, questionPaper.withAnswers, questionPaper.withSolutions || false, questionPaper.withHints || false, collegeInfo, subjectName);\n                        } else {\n                            await this.addQuestion(question, overallQuestionNumber, questionPaper.withAnswers, collegeInfo, subjectName);\n                        }\n                        overallQuestionNumber++;\n                    } catch (error) {\n                        console.error(`Error processing question ${overallQuestionNumber}:`, error);\n                        overallQuestionNumber++;\n                    // Continue with next question\n                    }\n                }\n            }\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PDFGenerator);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/pdfGenerator.ts\n");

/***/ })

};
;