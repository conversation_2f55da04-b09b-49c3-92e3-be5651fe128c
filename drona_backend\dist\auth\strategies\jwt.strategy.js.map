{"version": 3, "file": "jwt.strategy.js", "sourceRoot": "", "sources": ["../../../src/auth/strategies/jwt.strategy.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AACA,2CAA4C;AAC5C,+CAAoD;AACpD,+CAAoD;AACpD,2CAA+C;AAC/C,+CAA+C;AAC/C,uCAAiC;AACjC,0DAA8D;AAGvD,IAAM,WAAW,GAAjB,MAAM,WAAY,SAAQ,IAAA,2BAAgB,EAAC,uBAAQ,CAAC;IACzD,YACU,aAA4B,EACJ,SAA8B;QAE9D,KAAK,CAAC;YACJ,cAAc,EAAE,yBAAU,CAAC,2BAA2B,EAAE;YACxD,gBAAgB,EAAE,KAAK;YACvB,WAAW,EAAE,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC;SACrD,CAAC,CAAC;QAPK,kBAAa,GAAb,aAAa,CAAe;QACJ,cAAS,GAAT,SAAS,CAAqB;IAOhE,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,OAAY;QAEzB,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,SAAS;aAC9B,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC;aACrB,QAAQ,CAAC,WAAW,CAAC;aACrB,IAAI,EAAE,CAAC;QAGV,IAAI,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;QAGlC,IAAI,CAAC,SAAS,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACzC,SAAS;gBACP,OAAO,IAAI,CAAC,SAAS,KAAK,QAAQ;oBAChC,CAAC,CAAC,IAAI,CAAC,SAAS;oBAChB,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;QAClC,CAAC;QAED,OAAO;YACL,GAAG,EAAE,OAAO,CAAC,GAAG;YAChB,MAAM,EAAE,OAAO,CAAC,GAAG;YACnB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,SAAS,EAAE,SAAS;YACpB,IAAI;SACL,CAAC;IACJ,CAAC;CACF,CAAA;AAvCY,kCAAW;sBAAX,WAAW;IADvB,IAAA,mBAAU,GAAE;IAIR,WAAA,IAAA,sBAAW,EAAC,kBAAI,CAAC,IAAI,CAAC,CAAA;qCADA,sBAAa;QACO,gBAAK;GAHvC,WAAW,CAuCvB"}