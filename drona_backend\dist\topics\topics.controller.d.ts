import { TopicsService } from './topics.service';
import { CreateTopicDto } from './dto/create-topic.dto';
import { UpdateTopicDto } from './dto/update-topic.dto';
export declare class TopicsController {
    private readonly topicsService;
    private readonly logger;
    constructor(topicsService: TopicsService);
    create(createTopicDto: CreateTopicDto): Promise<import("../schema/topic.schema").Topic>;
    findAll(subjectId?: string): Promise<import("../schema/topic.schema").Topic[]>;
    findOne(id: string): Promise<import("../schema/topic.schema").Topic>;
    update(id: string, updateTopicDto: UpdateTopicDto): Promise<import("../schema/topic.schema").Topic>;
    remove(id: string): Promise<void>;
}
