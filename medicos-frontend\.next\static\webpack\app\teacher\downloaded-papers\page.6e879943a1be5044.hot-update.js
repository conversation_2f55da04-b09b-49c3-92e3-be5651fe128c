"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/teacher/downloaded-papers/page",{

/***/ "(app-pages-browser)/./src/lib/api/questionPapers.ts":
/*!***************************************!*\
  !*** ./src/lib/api/questionPapers.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createQuestionPaper: () => (/* binding */ createQuestionPaper),\n/* harmony export */   downloadQuestionPaper: () => (/* binding */ downloadQuestionPaper),\n/* harmony export */   generateQuestionPaperPDF: () => (/* binding */ generateQuestionPaperPDF),\n/* harmony export */   getQuestionPaper: () => (/* binding */ getQuestionPaper),\n/* harmony export */   getQuestionPaperForPDF: () => (/* binding */ getQuestionPaperForPDF),\n/* harmony export */   getQuestionPapers: () => (/* binding */ getQuestionPapers)\n/* harmony export */ });\nconst API_BASE_URL = \"http://localhost:3000/api\" || 0;\n/**\n * Get authentication headers with proper token\n */ function getAuthHeaders() {\n    // Try different token storage keys used in the codebase\n    const backendToken = localStorage.getItem(\"backendToken\");\n    const firebaseToken = localStorage.getItem(\"firebaseToken\");\n    const token = localStorage.getItem(\"token\");\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    // Prefer backend token, then firebase token, then generic token\n    if (backendToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(backendToken);\n    } else if (firebaseToken) {\n        headers[\"Authorization\"] = \"Bearer \".concat(firebaseToken);\n    } else if (token) {\n        headers[\"Authorization\"] = \"Bearer \".concat(token);\n    } else {\n        throw new Error(\"Authentication required - Please log in again. No valid authentication token found.\");\n    }\n    return headers;\n}\n/**\n * Create a new question paper\n * @param questionPaperData The question paper data\n * @returns The created question paper or error object\n */ async function createQuestionPaper(questionPaperData) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"POST\",\n            headers,\n            body: JSON.stringify(questionPaperData)\n        });\n        if (!response.ok) {\n            let errorMessage = \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            try {\n                // Try to get error message from response body\n                const errorText = await response.text();\n                if (errorText) {\n                    try {\n                        // Try to parse as JSON first\n                        const errorData = JSON.parse(errorText);\n                        // Extract the message from the parsed JSON\n                        if (errorData && errorData.message) {\n                            errorMessage = errorData.message;\n                        } else if (errorData && errorData.error) {\n                            errorMessage = errorData.error;\n                        } else {\n                            errorMessage = errorText;\n                        }\n                    } catch (jsonError) {\n                        // If not JSON, use the text directly\n                        errorMessage = errorText;\n                    }\n                }\n            } catch (parseError) {\n            // Silently handle parse errors\n            }\n            // Provide more specific error messages based on status code if we don't have a message\n            if (!errorMessage || errorMessage === \"Error: \".concat(response.status, \" - \").concat(response.statusText)) {\n                switch(response.status){\n                    case 401:\n                        errorMessage = \"Authentication required - Please log in again.\";\n                        break;\n                    case 403:\n                        errorMessage = \"Access denied - You don't have permission to perform this action.\";\n                        break;\n                    case 404:\n                        errorMessage = \"Resource not found - The requested item could not be found.\";\n                        break;\n                    case 429:\n                        errorMessage = \"Too many requests - Please wait a moment before trying again.\";\n                        break;\n                    case 500:\n                        errorMessage = \"Server error - Please try again later.\";\n                        break;\n                    case 503:\n                        errorMessage = \"Service unavailable - The server is temporarily down.\";\n                        break;\n                    default:\n                        if (response.status >= 400 && response.status < 500) {\n                            errorMessage = \"Invalid request - Please check your input and try again.\";\n                        } else if (response.status >= 500) {\n                            errorMessage = \"Server error - Please try again later.\";\n                        }\n                }\n            }\n            return {\n                success: false,\n                error: errorMessage\n            };\n        }\n        const data = await response.json();\n        console.log(\"Raw API response from createQuestionPaper:\", data);\n        // The backend returns { questionPaper: {...}, college: {...} }\n        // But we need to return it in our expected format\n        if (data.questionPaper) {\n            return {\n                success: true,\n                data: data.questionPaper\n            };\n        } else {\n            // If the response structure is different, return as is\n            return {\n                success: true,\n                data\n            };\n        }\n    } catch (error) {\n        return {\n            success: false,\n            error: \"Network error - Please check your connection and try again.\"\n        };\n    }\n}\n/**\n * Get question paper data with college information for frontend PDF generation\n * @param questionPaperId The question paper ID\n * @returns The question paper data with college info\n */ async function getQuestionPaperForPDF(questionPaperId) {\n    try {\n        console.log(\"getQuestionPaperForPDF called with:\", questionPaperId);\n        // Validate questionPaperId\n        if (!questionPaperId || questionPaperId === 'undefined' || questionPaperId === 'null') {\n            throw new Error(\"Invalid question paper ID: \".concat(questionPaperId));\n        }\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(\"API Error Response:\", errorText);\n            throw new Error(\"Failed to fetch question paper: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const data = await response.json();\n        console.log(\"Question paper data fetched:\", data);\n        // The API returns the question paper directly, so we need to structure it properly\n        // Also try to get college information if available\n        let college = undefined;\n        if (data.collegeId) {\n            try {\n                // Import the college API function\n                const { getCollegeById } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_lib_api_college_ts\").then(__webpack_require__.bind(__webpack_require__, /*! ./college */ \"(app-pages-browser)/./src/lib/api/college.ts\"));\n                // Fetch college information\n                console.log(\"Fetching college information for ID:\", data.collegeId);\n                const collegeData = await getCollegeById(data.collegeId);\n                if (collegeData) {\n                    college = {\n                        name: collegeData.name,\n                        logoUrl: collegeData.logoUrl,\n                        address: collegeData.address\n                    };\n                    console.log(\"College information fetched:\", college);\n                }\n            } catch (error) {\n                console.warn(\"Could not fetch college information:\", error);\n            }\n        }\n        return {\n            questionPaper: data,\n            college: college\n        };\n    } catch (error) {\n        console.error(\"Error fetching question paper for PDF:\", error);\n        throw error;\n    }\n}\n/**\n * Download a question paper as PDF\n * @param questionPaperId The question paper ID\n * @param format The format (pdf or docx)\n * @param includeAnswers Whether to include answers in the download\n * @returns The file blob\n */ async function downloadQuestionPaper(questionPaperId) {\n    let format = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'pdf', includeAnswers = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true;\n    try {\n        console.log(\"downloadQuestionPaper called with:\", {\n            questionPaperId,\n            format,\n            includeAnswers\n        });\n        // Validate questionPaperId\n        if (!questionPaperId || questionPaperId === 'undefined' || questionPaperId === 'null') {\n            throw new Error(\"Invalid question paper ID: \".concat(questionPaperId));\n        }\n        const headers = getAuthHeaders();\n        delete headers[\"Content-Type\"]; // Remove content-type for blob response\n        const queryParams = new URLSearchParams({\n            format,\n            ...includeAnswers && {\n                includeAnswers: 'true'\n            }\n        });\n        const downloadUrl = \"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId, \"/download?\").concat(queryParams);\n        console.log(\"Download URL:\", downloadUrl);\n        const response = await fetch(downloadUrl, {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            let errorMessage = errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText);\n            if (response.status === 401) {\n                errorMessage = \"Authentication required - Please log in again.\";\n            } else if (response.status === 404) {\n                errorMessage = \"Question paper not found.\";\n            } else if (response.status >= 500) {\n                errorMessage = \"Server error - Please try again later.\";\n            }\n            throw new Error(errorMessage);\n        }\n        return await response.blob();\n    } catch (error) {\n        console.error(\"Error downloading question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Get all question papers\n * @returns List of question papers\n */ async function getQuestionPapers() {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers\"), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question papers:\", error);\n        throw error;\n    }\n}\n/**\n * Get a specific question paper by ID\n * @param questionPaperId The question paper ID\n * @returns The question paper\n */ async function getQuestionPaper(questionPaperId) {\n    try {\n        const headers = getAuthHeaders();\n        const response = await fetch(\"\".concat(API_BASE_URL, \"/question-papers/\").concat(questionPaperId), {\n            method: \"GET\",\n            headers\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || \"Error: \".concat(response.status, \" - \").concat(response.statusText));\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper:\", error);\n        throw error;\n    }\n}\n/**\n * Generate and download a question paper PDF using frontend generation\n * @param questionPaperId The question paper ID\n * @param includeAnswers Whether to include answers in the download\n * @returns The generated PDF blob\n */ async function generateQuestionPaperPDF(questionPaperId) {\n    let includeAnswers = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n    try {\n        console.log(\"generateQuestionPaperPDF called with:\", {\n            questionPaperId,\n            includeAnswers\n        });\n        // Import PDF generator dynamically to avoid SSR issues\n        const { default: PDFGenerator } = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_src_utils_pdfGenerator_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/utils/pdfGenerator */ \"(app-pages-browser)/./src/utils/pdfGenerator.ts\"));\n        // Fetch question paper data\n        const data = await getQuestionPaperForPDF(questionPaperId);\n        console.log(\"Fetched data structure:\", data);\n        if (!data.questionPaper) {\n            console.error(\"Question paper data not found in:\", data);\n            throw new Error('Question paper data not found');\n        }\n        console.log(\"Question paper found:\", data.questionPaper);\n        console.log(\"College info:\", data.college);\n        // Set withAnswers flag\n        const questionPaper = {\n            ...data.questionPaper,\n            withAnswers: includeAnswers\n        };\n        console.log(\"Final question paper for PDF:\", questionPaper);\n        // Generate PDF using frontend generator\n        const pdfGenerator = new PDFGenerator();\n        const pdfBlob = await pdfGenerator.generatePDF(questionPaper, data.college);\n        console.log(\"Frontend PDF generated successfully\");\n        return pdfBlob;\n    } catch (error) {\n        console.error(\"Error generating question paper PDF:\", error);\n        throw error;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api/questionPapers.ts\n"));

/***/ })

});