{"version": 3, "file": "firebase-auth.service.js", "sourceRoot": "", "sources": ["../../src/auth/firebase-auth.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AACA,2CAKwB;AACxB,2CAA+C;AAC/C,wCAAwC;AACxC,6BAA6B;AAGtB,IAAM,mBAAmB,2BAAzB,MAAM,mBAAmB;IAI9B,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAH/B,WAAM,GAAG,IAAI,eAAM,CAAC,qBAAmB,CAAC,IAAI,CAAC,CAAC;QACvD,gBAAW,GAAG,KAAK,CAAC;IAEuB,CAAC;IAEpD,KAAK,CAAC,YAAY;QAChB,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;IACvC,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACnC,IAAI,CAAC;YAEH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAEvB,MAAM,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAC/C,+BAA+B,CAChC,CAAC;gBAEF,IAAI,kBAAkB,EAAE,CAAC;oBAEvB,MAAM,sBAAsB,GAAG,kBAAkB,CAAC,UAAU,CAAC,GAAG,CAAC;wBAC/D,CAAC,CAAC,kBAAkB;wBACpB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,kBAAkB,CAAC,CAAC;oBAEjD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,8DAA8D,sBAAsB,EAAE,CACvF,CAAC;oBAEF,KAAK,CAAC,aAAa,CAAC;wBAClB,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,sBAAsB,CAAC;qBAC1D,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBAEN,MAAM,SAAS,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CACtC,qBAAqB,CACtB,CAAC;oBACF,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CACxC,uBAAuB,CACxB,CAAC;oBACF,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CACvC,sBAAsB,CACvB,CAAC;oBAGF,IAAI,CAAC,SAAS,IAAI,CAAC,WAAW,IAAI,CAAC,UAAU,EAAE,CAAC;wBAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,uDAAuD,CACxD,CAAC;wBACF,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;oBAClE,CAAC;oBAED,KAAK,CAAC,aAAa,CAAC;wBAClB,UAAU,EAAE,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC;4BAChC,SAAS;4BACT,WAAW;4BAEX,UAAU,EAAE,UAAU,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC;yBAC7C,CAAC;qBACH,CAAC,CAAC;gBACL,CAAC;gBAED,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;gBACxB,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;YAC1B,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,4CAA4C,KAAK,CAAC,OAAO,EAAE,CAC5D,CAAC;YAEF,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC3B,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,WAAW,CAAC,KAAa;QAC7B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,8BAAqB,CAC7B,0CAA0C,CAC3C,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;YACnE,OAAO,YAAY,CAAC;QACtB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACvE,MAAM,IAAI,8BAAqB,CAAC,mCAAmC,CAAC,CAAC;QACvE,CAAC;IACH,CAAC;IAQD,KAAK,CAAC,YAAY,CAAC,GAAW;QAC5B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YACrC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,8BAAqB,CAC7B,0CAA0C,CAC3C,CAAC;YACJ,CAAC;QACH,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;YAC9B,OAAO,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACjE,MAAM,IAAI,8BAAqB,CAAC,4BAA4B,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;CACF,CAAA;AA9HY,kDAAmB;8BAAnB,mBAAmB;IAD/B,IAAA,mBAAU,GAAE;qCAKwB,sBAAa;GAJrC,mBAAmB,CA8H/B"}