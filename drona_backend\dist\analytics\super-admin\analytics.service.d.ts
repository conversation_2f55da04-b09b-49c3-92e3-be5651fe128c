import { Model } from 'mongoose';
import { UserActivityDocument } from '../../schema/user-activity.schema';
import { UserDocument } from '../../schema/user.schema';
import { QuestionPaperDocument } from '../../schema/question-paper.schema';
import { QuestionDocument } from '../../schema/question.schema';
import { CollegeDocument } from '../../schema/college.schema';
import { DownloadDocument } from '../../schema/download.schema';
import { AnalyticsCacheService } from '../../common/services';
export declare class AnalyticsService {
    private userActivityModel;
    private userModel;
    private questionPaperModel;
    private questionModel;
    private collegeModel;
    private downloadModel;
    private readonly cacheService;
    private readonly logger;
    private readonly CACHE_TTL;
    constructor(userActivityModel: Model<UserActivityDocument>, userModel: Model<UserDocument>, questionPaperModel: Model<QuestionPaperDocument>, questionModel: Model<QuestionDocument>, collegeModel: Model<CollegeDocument>, downloadModel: Model<DownloadDocument>, cacheService: AnalyticsCacheService);
    getPlatformSummary(): Promise<{
        totalColleges: number;
        totalTeachers: number;
        totalQuestions: number;
        totalPapers: number;
        totalDownloads: number;
        recentActivity: {
            logins: any;
            paperGenerations: any;
            questionCreations: any;
            downloads: number;
        };
    }>;
    getTopColleges(): Promise<{
        byDownloads: any[];
        byTeachers: any[];
    }>;
    getQuestionUsage(): Promise<{
        usedQuestions: any[];
        unusedQuestions: any[];
        byDifficulty: any;
        byType: any;
    }>;
    getQuestionStats(): Promise<{
        totalQuestions: any;
        bySubject: any;
        byTopic: any;
        byDifficulty: any;
        byType: any;
    }>;
    getCollegeAnalytics(filters?: {
        startDate?: Date;
        endDate?: Date;
        limit?: number;
        sortBy?: string;
    }): Promise<{
        summary: {
            totalColleges: number;
            totalTeachers: any;
            totalQuestions: any;
            totalPapers: any;
            totalDownloads: any;
        };
        colleges: any[];
        filters: {
            startDate: Date | null;
            endDate: Date | null;
            limit: number;
            sortBy: string;
        };
    }>;
    private getCollegeSubjectBreakdown;
    getUsageTrends(filters?: {
        year?: number;
        startDate?: Date;
        endDate?: Date;
    }): Promise<{
        data: any[];
        summary: {
            totalMonths: number;
            totalQuestionsCreated: any;
            totalPapersGenerated: any;
            averageMonthlyQuestions: number;
            averageMonthlyPapers: number;
        };
        dateRange: {
            startDate: Date;
            endDate: Date;
        };
    }>;
    getCollegeGrowth(filters?: {
        year?: number;
        startDate?: Date;
        endDate?: Date;
        view?: string;
    }): Promise<{
        data: any[];
        summary: {
            totalMonths: number;
            totalCollegesAdded: any;
            averageMonthlyGrowth: number;
            totalTargetAchievement: number;
            totalRevenue: any;
        };
        targets: {
            monthlyTarget: number;
            yearlyTarget: number;
            currentProgress: number;
        };
        dateRange: {
            startDate: Date;
            endDate: Date;
        };
    }>;
    private generateRevenueData;
    private generateSalesMetrics;
}
