import { OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
export declare class FirebaseAuthService implements OnModuleInit {
    private configService;
    private readonly logger;
    private initialized;
    constructor(configService: ConfigService);
    onModuleInit(): Promise<void>;
    private initializeFirebaseAdmin;
    verifyToken(token: string): Promise<admin.auth.DecodedIdToken>;
    getUserByUid(uid: string): Promise<admin.auth.UserRecord>;
}
