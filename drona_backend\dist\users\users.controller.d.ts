import { UsersService } from './users.service';
import { UserDocument } from '../schema/user.schema';
import { AssignRoleDto } from './dto/assign-role.dto';
export declare class UsersController {
    private readonly usersService;
    constructor(usersService: UsersService);
    getCurrentUser(user: any): Promise<any>;
    private transformUserResponse;
    findAll(): Promise<UserDocument[]>;
    assignRole(id: string, assignRoleDto: AssignRoleDto): Promise<UserDocument>;
    findByCollege(collegeId: string): Promise<UserDocument[]>;
}
