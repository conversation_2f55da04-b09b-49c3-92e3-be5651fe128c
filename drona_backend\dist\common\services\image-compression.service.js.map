{"version": 3, "file": "image-compression.service.js", "sourceRoot": "", "sources": ["../../../src/common/services/image-compression.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAyE;AACzE,2CAA+C;AAC/C,+BAA+B;AAC/B,yBAAyB;AACzB,6BAA6B;AAC7B,+BAAoC;AAwB7B,IAAM,uBAAuB,+BAA7B,MAAM,uBAAuB;IAKlC,YAA6B,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJxC,WAAM,GAAG,IAAI,eAAM,CAAC,yBAAuB,CAAC,IAAI,CAAC,CAAC;QAKjE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;QAChE,IAAI,CAAC,OAAO;YACV,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,UAAU,CAAC,IAAI,uBAAuB,CAAC;QAGxE,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAKD,KAAK,CAAC,oBAAoB,CACxB,IAAyB,EACzB,UAAmC,EAAE;QAErC,IAAI,CAAC;YAEH,MAAM,cAAc,GAA4B;gBAC9C,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;gBACrD,OAAO,EAAE,EAAE;gBACX,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,IAAI;gBACf,MAAM,EAAE,MAAM;gBACd,mBAAmB,EAAE,IAAI;gBACzB,GAAG,OAAO;aACX,CAAC;YAGF,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC1C,MAAM,IAAI,4BAAmB,CAC3B,2DAA2D,CAC5D,CAAC;YACJ,CAAC;YAGD,MAAM,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,MAAO,CAAC,CAAC;YACpE,MAAM,QAAQ,GAAG,GAAG,IAAA,SAAM,GAAE,GAAG,aAAa,EAAE,CAAC;YAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAGxD,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;YACxC,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;YAEtD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,qBAAqB,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,GAAG,CAC7E,CAAC;YAGF,IAAI,eAAe,GAAG,IAAI,CAAC,MAAM,CAAC;YAClC,IAAI,cAAc,GAAG,cAAc,CAAC,OAAQ,CAAC;YAC7C,IAAI,QAAQ,GAAG,CAAC,CAAC;YACjB,MAAM,WAAW,GAAG,EAAE,CAAC;YAGvB,OACE,eAAe,CAAC,MAAM,GAAG,cAAc,CAAC,YAAa;gBACrD,QAAQ,GAAG,WAAW,EACtB,CAAC;gBACD,QAAQ,EAAE,CAAC;gBAEX,IAAI,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAGvC,IACE,CAAC,SAAS,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,cAAc,CAAC,QAAS;oBACjD,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,GAAG,cAAc,CAAC,SAAU,EACnD,CAAC;oBACD,aAAa,GAAG,aAAa,CAAC,MAAM,CAClC,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,SAAS,EACxB;wBACE,GAAG,EAAE,QAAQ;wBACb,kBAAkB,EAAE,IAAI;qBACzB,CACF,CAAC;gBACJ,CAAC;gBAGD,QAAQ,cAAc,CAAC,MAAM,EAAE,CAAC;oBAC9B,KAAK,MAAM;wBACT,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;wBAChE,MAAM;oBACR,KAAK,KAAK;wBACR,aAAa,GAAG,aAAa,CAAC,GAAG,CAAC;4BAChC,OAAO,EAAE,cAAc;4BACvB,gBAAgB,EAAE,CAAC;yBACpB,CAAC,CAAC;wBACH,MAAM;oBACR,KAAK,MAAM;wBACT,aAAa,GAAG,aAAa,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,cAAc,EAAE,CAAC,CAAC;wBAChE,MAAM;gBACV,CAAC;gBAED,eAAe,GAAG,MAAM,aAAa,CAAC,QAAQ,EAAE,CAAC;gBAGjD,IAAI,eAAe,CAAC,MAAM,GAAG,cAAc,CAAC,YAAa,EAAE,CAAC;oBAC1D,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE,EAAE,cAAc,GAAG,EAAE,CAAC,CAAC;oBACnD,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,WAAW,QAAQ,UAAU,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,yBAAyB,cAAc,EAAE,CAC/G,CAAC;gBACJ,CAAC;YACH,CAAC;YAGD,MAAM,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;YAGzD,MAAM,aAAa,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC9D,MAAM,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC;YAC9C,MAAM,gBAAgB,GACpB,CAAC,CAAC,YAAY,GAAG,cAAc,CAAC,GAAG,YAAY,CAAC,GAAG,GAAG,CAAC;YAEzD,MAAM,MAAM,GAA0B;gBACpC,QAAQ;gBACR,IAAI,EAAE,UAAU;gBAChB,GAAG,EAAE,GAAG,IAAI,CAAC,OAAO,mBAAmB,QAAQ,EAAE;gBACjD,YAAY;gBACZ,cAAc;gBACd,gBAAgB,EAAE,IAAI,CAAC,KAAK,CAAC,gBAAgB,GAAG,GAAG,CAAC,GAAG,GAAG;gBAC1D,KAAK,EAAE,aAAa,CAAC,KAAK,IAAI,CAAC;gBAC/B,MAAM,EAAE,aAAa,CAAC,MAAM,IAAI,CAAC;gBACjC,MAAM,EAAE,cAAc,CAAC,MAAO;aAC/B,CAAC;YAEF,IAAI,CAAC,MAAM,CAAC,GAAG,CACb,kCAAkC,IAAI,CAAC,YAAY,OAAO,QAAQ,GAAG;gBACnE,IAAI,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI;gBAC7E,GAAG,MAAM,CAAC,gBAAgB,cAAc,CAC3C,CAAC;YAEF,OAAO,MAAM,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,4BAA4B,KAAK,CAAC,OAAO,EAAE,EAC3C,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,4BAAmB,CAC3B,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAC7C,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,UAAkB,EAClB,UAAmC,EAAE;QAErC,IAAI,CAAC;YAEH,MAAM,WAAW,GAAG,UAAU,CAAC,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC,CAAC;YAC1E,MAAM,MAAM,GAAG,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAGlD,MAAM,QAAQ,GAAwB;gBACpC,MAAM;gBACN,YAAY,EAAE,cAAc;gBAC5B,QAAQ,EAAE,YAAY;gBACtB,IAAI,EAAE,MAAM,CAAC,MAAM;gBACnB,SAAS,EAAE,EAAE;gBACb,QAAQ,EAAE,EAAE;gBACZ,QAAQ,EAAE,EAAE;gBACZ,IAAI,EAAE,EAAE;gBACR,WAAW,EAAE,EAAE;gBACf,MAAM,EAAE,IAAW;aACpB,CAAC;YAEF,OAAO,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CACf,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAClD,KAAK,CAAC,KAAK,CACZ,CAAC;YACF,MAAM,IAAI,4BAAmB,CAC3B,oCAAoC,KAAK,CAAC,OAAO,EAAE,CACpD,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,WAAW,CAAC,QAAgB;QAChC,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAEtD,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpC,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;gBACnC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kBAAkB,QAAQ,EAAE,CAAC,CAAC;gBAC9C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wBAAwB,QAAQ,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,YAAY,CAAC,IAAyB;QAC1C,IAAI,CAAC;YACH,OAAO,MAAM,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,4BAAmB,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAKO,gBAAgB,CAAC,QAAgB;QACvC,MAAM,UAAU,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;QAC1E,OAAO,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;IACrD,CAAC;IAKO,gBAAgB,CAAC,MAAc;QACrC,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,MAAM;gBACT,OAAO,MAAM,CAAC;YAChB,KAAK,KAAK;gBACR,OAAO,MAAM,CAAC;YAChB,KAAK,MAAM;gBACT,OAAO,OAAO,CAAC;YACjB;gBACE,OAAO,MAAM,CAAC;QAClB,CAAC;IACH,CAAC;IAKO,WAAW,CAAC,KAAa;QAC/B,IAAI,KAAK,KAAK,CAAC;YAAE,OAAO,SAAS,CAAC;QAClC,MAAM,CAAC,GAAG,IAAI,CAAC;QACf,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;QAC1C,MAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,OAAO,UAAU,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC1E,CAAC;IAKO,KAAK,CAAC,UAAU,CAAC,QAAgB;QACvC,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKO,sBAAsB;QAC5B,IAAI,CAAC;YACH,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC;gBACpC,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBACnD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,IAAI,CAAC,UAAU,EAAE,CAAC,CAAC;YACnE,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;CACF,CAAA;AAtRY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAMiC,sBAAa;GAL9C,uBAAuB,CAsRnC"}