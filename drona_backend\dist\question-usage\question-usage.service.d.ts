import { Model } from 'mongoose';
import { QuestionUsage, QuestionUsageDocument } from '../schema/question-usage.schema';
export declare class QuestionUsageService {
    private questionUsageModel;
    private readonly logger;
    constructor(questionUsageModel: Model<QuestionUsageDocument>);
    recordQuestionUsage(data: {
        collegeId: string;
        questionId: string;
        questionPaperId: string;
        usedBy: string;
        subjectId: string;
        topicId?: string;
        metadata?: any;
    }): Promise<QuestionUsage | null>;
    recordMultipleQuestionUsage(usageData: Array<{
        collegeId: string;
        questionId: string;
        questionPaperId: string;
        usedBy: string;
        subjectId: string;
        topicId?: string;
        metadata?: any;
    }>): Promise<{
        recorded: number;
        skipped: number;
    }>;
    getUsedQuestionIds(collegeId: string, filters?: {
        subjectId?: string;
        topicId?: string;
        status?: string;
    }): Promise<string[]>;
    getUnusedQuestions(collegeId: string, availableQuestionIds: string[], filters?: {
        subjectId?: string;
        topicId?: string;
    }): Promise<string[]>;
}
