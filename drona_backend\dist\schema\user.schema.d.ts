import { Document, Schema as MongooseSchema } from 'mongoose';
import { College } from './college.schema';
export type UserDocument = User & Document;
export declare class User {
    email: string;
    password: string;
    firebaseUid: string;
    displayName: string;
    firstName: string;
    lastName: string;
    role: string;
    collegeId: College;
    lastLogin: Date;
    status: string;
    profileImageUrl: string;
    phone: string;
    department: string;
    designation: string;
}
export declare const UserSchema: MongooseSchema<User, import("mongoose").Model<User, any, any, any, Document<unknown, any, User> & User & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, User, Document<unknown, {}, import("mongoose").FlatRecord<User>> & import("mongoose").FlatRecord<User> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
