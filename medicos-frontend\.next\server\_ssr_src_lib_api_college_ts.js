"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_lib_api_college_ts";
exports.ids = ["_ssr_src_lib_api_college_ts"];
exports.modules = {

/***/ "(ssr)/./src/lib/api/college.ts":
/*!********************************!*\
  !*** ./src/lib/api/college.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createCollege: () => (/* binding */ createCollege),\n/* harmony export */   deleteCollege: () => (/* binding */ deleteCollege),\n/* harmony export */   getCollegeAnalytics: () => (/* binding */ getCollegeAnalytics),\n/* harmony export */   getCollegeById: () => (/* binding */ getCollegeById),\n/* harmony export */   getColleges: () => (/* binding */ getColleges),\n/* harmony export */   getQuestionPaperStatsByDateRange: () => (/* binding */ getQuestionPaperStatsByDateRange),\n/* harmony export */   updateCollege: () => (/* binding */ updateCollege)\n/* harmony export */ });\n/**\n * Interface for college data\n */ /**\n * Create a new college\n * @param collegeData The college data\n * @returns The created college\n */ async function createCollege(collegeData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(`${baseUrl}/colleges`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": `Bearer ${token}`\n            },\n            body: JSON.stringify(collegeData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || `Error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error creating college:\", error);\n        throw error;\n    }\n}\n/**\n * Get all colleges\n * @returns List of colleges\n */ async function getColleges() {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(`${baseUrl}/colleges`, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || `Error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching colleges:\", error);\n        throw error;\n    }\n}\n/**\n * Delete a college\n * @param id College ID\n * @returns The deleted college\n */ async function deleteCollege(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(`${baseUrl}/colleges/${id}`, {\n            method: \"DELETE\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || `Error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error deleting college:\", error);\n        throw error;\n    }\n}\n/**\n * Get a college by ID\n * @param id College ID\n * @returns The college\n */ async function getCollegeById(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(`${baseUrl}/colleges/${id}`, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || `Error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching college:\", error);\n        throw error;\n    }\n}\n/**\n * Update a college\n * @param id College ID\n * @param collegeData The updated college data\n * @returns The updated college\n */ async function updateCollege(id, collegeData) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(`${baseUrl}/colleges/${id}`, {\n            method: \"PUT\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Authorization\": `Bearer ${token}`\n            },\n            body: JSON.stringify(collegeData)\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || `Error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error updating college:\", error);\n        throw error;\n    }\n}\n/**\n * Get a college by ID\n * @param id College ID\n * @returns The college\n */ async function getCollegeAnalytics(id) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const response = await fetch(`${baseUrl}/analytics/college/${id}/summary`, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || `Error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching college:\", error);\n        throw error;\n    }\n}\n/**\n * Get daily-wise question paper statistics by subject for a college\n * @param id College ID\n * @param startDate ISO start date string\n * @param endDate ISO end date string\n * @returns Statistics data\n */ async function getQuestionPaperStatsByDateRange(id, startDate, endDate) {\n    const token = localStorage.getItem(\"backendToken\");\n    if (!token) {\n        throw new Error(\"Authentication required\");\n    }\n    try {\n        const baseUrl = \"http://localhost:3000/api\" || 0;\n        const url = `${baseUrl}/analytics/college/${id}/question-papers?startDate=${encodeURIComponent(startDate)}&endDate=${encodeURIComponent(endDate)}`;\n        const response = await fetch(url, {\n            method: \"GET\",\n            headers: {\n                \"Authorization\": `Bearer ${token}`,\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            const errorData = await response.json().catch(()=>({}));\n            throw new Error(errorData.message || `Error: ${response.status}`);\n        }\n        return await response.json();\n    } catch (error) {\n        console.error(\"Error fetching question paper stats:\", error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL2FwaS9jb2xsZWdlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBQTs7Q0FFQyxHQWNEOzs7O0NBSUMsR0FDTSxlQUFlQSxjQUFjQyxXQUF3QjtJQUMxRCxNQUFNQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7SUFDbkMsSUFBSSxDQUFDRixPQUFPO1FBQ1YsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1DLFVBQVVDLDJCQUErQixJQUFJLENBQTJCO1FBQzlFLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxHQUFHTCxRQUFRLFNBQVMsQ0FBQyxFQUFFO1lBQ2xETSxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsZ0JBQWdCO2dCQUNoQixpQkFBaUIsQ0FBQyxPQUFPLEVBQUVYLE9BQU87WUFDcEM7WUFDQVksTUFBTUMsS0FBS0MsU0FBUyxDQUFDZjtRQUN2QjtRQUVBLElBQUksQ0FBQ1MsU0FBU08sRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTVIsU0FBU1MsSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3RELE1BQU0sSUFBSWYsTUFBTWEsVUFBVUcsT0FBTyxJQUFJLENBQUMsT0FBTyxFQUFFWCxTQUFTWSxNQUFNLEVBQUU7UUFDbEU7UUFFQSxPQUFPLE1BQU1aLFNBQVNTLElBQUk7SUFDNUIsRUFBRSxPQUFPSSxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7Q0FHQyxHQUNNLGVBQWVFO0lBQ3BCLE1BQU12QixRQUFRQyxhQUFhQyxPQUFPLENBQUM7SUFDbkMsSUFBSSxDQUFDRixPQUFPO1FBQ1YsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1DLFVBQVVDLDJCQUErQixJQUFJLENBQTJCO1FBQzlFLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxHQUFHTCxRQUFRLFNBQVMsQ0FBQyxFQUFFO1lBQ2xETSxRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsaUJBQWlCLENBQUMsT0FBTyxFQUFFWCxPQUFPO1lBQ3BDO1FBQ0Y7UUFFQSxJQUFJLENBQUNRLFNBQVNPLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1SLFNBQVNTLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztZQUN0RCxNQUFNLElBQUlmLE1BQU1hLFVBQVVHLE9BQU8sSUFBSSxDQUFDLE9BQU8sRUFBRVgsU0FBU1ksTUFBTSxFQUFFO1FBQ2xFO1FBRUEsT0FBTyxNQUFNWixTQUFTUyxJQUFJO0lBQzVCLEVBQUUsT0FBT0ksT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtRQUMxQyxNQUFNQTtJQUNSO0FBQ0Y7QUFFQTs7OztDQUlDLEdBQ00sZUFBZUcsY0FBY0MsRUFBVTtJQUM1QyxNQUFNekIsUUFBUUMsYUFBYUMsT0FBTyxDQUFDO0lBQ25DLElBQUksQ0FBQ0YsT0FBTztRQUNWLE1BQU0sSUFBSUcsTUFBTTtJQUNsQjtJQUVBLElBQUk7UUFDRixNQUFNQyxVQUFVQywyQkFBK0IsSUFBSSxDQUEyQjtRQUM5RSxNQUFNRyxXQUFXLE1BQU1DLE1BQU0sR0FBR0wsUUFBUSxVQUFVLEVBQUVxQixJQUFJLEVBQUU7WUFDeERmLFFBQVE7WUFDUkMsU0FBUztnQkFDUCxpQkFBaUIsQ0FBQyxPQUFPLEVBQUVYLE9BQU87WUFDcEM7UUFDRjtRQUVBLElBQUksQ0FBQ1EsU0FBU08sRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTVIsU0FBU1MsSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3RELE1BQU0sSUFBSWYsTUFBTWEsVUFBVUcsT0FBTyxJQUFJLENBQUMsT0FBTyxFQUFFWCxTQUFTWSxNQUFNLEVBQUU7UUFDbEU7UUFFQSxPQUFPLE1BQU1aLFNBQVNTLElBQUk7SUFDNUIsRUFBRSxPQUFPSSxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE1BQU1BO0lBQ1I7QUFDRjtBQUVBOzs7O0NBSUMsR0FDTSxlQUFlSyxlQUFlRCxFQUFVO0lBQzdDLE1BQU16QixRQUFRQyxhQUFhQyxPQUFPLENBQUM7SUFDbkMsSUFBSSxDQUFDRixPQUFPO1FBQ1YsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1DLFVBQVVDLDJCQUErQixJQUFJLENBQTJCO1FBQzlFLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxHQUFHTCxRQUFRLFVBQVUsRUFBRXFCLElBQUksRUFBRTtZQUN4RGYsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGlCQUFpQixDQUFDLE9BQU8sRUFBRVgsT0FBTztZQUNwQztRQUNGO1FBRUEsSUFBSSxDQUFDUSxTQUFTTyxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNUixTQUFTUyxJQUFJLEdBQUdDLEtBQUssQ0FBQyxJQUFPLEVBQUM7WUFDdEQsTUFBTSxJQUFJZixNQUFNYSxVQUFVRyxPQUFPLElBQUksQ0FBQyxPQUFPLEVBQUVYLFNBQVNZLE1BQU0sRUFBRTtRQUNsRTtRQUVBLE9BQU8sTUFBTVosU0FBU1MsSUFBSTtJQUM1QixFQUFFLE9BQU9JLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7UUFDekMsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7O0NBS0MsR0FDTSxlQUFlTSxjQUFjRixFQUFVLEVBQUUxQixXQUF3QjtJQUN0RSxNQUFNQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7SUFDbkMsSUFBSSxDQUFDRixPQUFPO1FBQ1YsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1DLFVBQVVDLDJCQUErQixJQUFJLENBQTJCO1FBQzlFLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxHQUFHTCxRQUFRLFVBQVUsRUFBRXFCLElBQUksRUFBRTtZQUN4RGYsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGdCQUFnQjtnQkFDaEIsaUJBQWlCLENBQUMsT0FBTyxFQUFFWCxPQUFPO1lBQ3BDO1lBQ0FZLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ2Y7UUFDdkI7UUFFQSxJQUFJLENBQUNTLFNBQVNPLEVBQUUsRUFBRTtZQUNoQixNQUFNQyxZQUFZLE1BQU1SLFNBQVNTLElBQUksR0FBR0MsS0FBSyxDQUFDLElBQU8sRUFBQztZQUN0RCxNQUFNLElBQUlmLE1BQU1hLFVBQVVHLE9BQU8sSUFBSSxDQUFDLE9BQU8sRUFBRVgsU0FBU1ksTUFBTSxFQUFFO1FBQ2xFO1FBRUEsT0FBTyxNQUFNWixTQUFTUyxJQUFJO0lBQzVCLEVBQUUsT0FBT0ksT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtRQUN6QyxNQUFNQTtJQUNSO0FBQ0Y7QUFDQTs7OztDQUlDLEdBQ00sZUFBZU8sb0JBQW9CSCxFQUFVO0lBQ2xELE1BQU16QixRQUFRQyxhQUFhQyxPQUFPLENBQUM7SUFDbkMsSUFBSSxDQUFDRixPQUFPO1FBQ1YsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBRUEsSUFBSTtRQUNGLE1BQU1DLFVBQVVDLDJCQUErQixJQUFJLENBQTJCO1FBQzlFLE1BQU1HLFdBQVcsTUFBTUMsTUFBTSxHQUFHTCxRQUFRLG1CQUFtQixFQUFFcUIsR0FBRyxRQUFRLENBQUMsRUFBRTtZQUN6RWYsUUFBUTtZQUNSQyxTQUFTO2dCQUNQLGlCQUFpQixDQUFDLE9BQU8sRUFBRVgsT0FBTztZQUNwQztRQUNGO1FBRUEsSUFBSSxDQUFDUSxTQUFTTyxFQUFFLEVBQUU7WUFDaEIsTUFBTUMsWUFBWSxNQUFNUixTQUFTUyxJQUFJLEdBQUdDLEtBQUssQ0FBQyxJQUFPLEVBQUM7WUFDdEQsTUFBTSxJQUFJZixNQUFNYSxVQUFVRyxPQUFPLElBQUksQ0FBQyxPQUFPLEVBQUVYLFNBQVNZLE1BQU0sRUFBRTtRQUNsRTtRQUVBLE9BQU8sTUFBTVosU0FBU1MsSUFBSTtJQUM1QixFQUFFLE9BQU9JLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7UUFDekMsTUFBTUE7SUFDUjtBQUNGO0FBRUE7Ozs7OztDQU1DLEdBQ00sZUFBZVEsaUNBQWlDSixFQUFVLEVBQUVLLFNBQWlCLEVBQUVDLE9BQWU7SUFDbkcsTUFBTS9CLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztJQUNuQyxJQUFJLENBQUNGLE9BQU87UUFDVixNQUFNLElBQUlHLE1BQU07SUFDbEI7SUFFQSxJQUFJO1FBQ0YsTUFBTUMsVUFBVUMsMkJBQStCLElBQUksQ0FBMkI7UUFDOUUsTUFBTTJCLE1BQU0sR0FBRzVCLFFBQVEsbUJBQW1CLEVBQUVxQixHQUFHLDJCQUEyQixFQUFFUSxtQkFBbUJILFdBQVcsU0FBUyxFQUFFRyxtQkFBbUJGLFVBQVU7UUFFbEosTUFBTXZCLFdBQVcsTUFBTUMsTUFBTXVCLEtBQUs7WUFDaEN0QixRQUFRO1lBQ1JDLFNBQVM7Z0JBQ1AsaUJBQWlCLENBQUMsT0FBTyxFQUFFWCxPQUFPO2dCQUNsQyxnQkFBZ0I7WUFDbEI7UUFDRjtRQUVBLElBQUksQ0FBQ1EsU0FBU08sRUFBRSxFQUFFO1lBQ2hCLE1BQU1DLFlBQVksTUFBTVIsU0FBU1MsSUFBSSxHQUFHQyxLQUFLLENBQUMsSUFBTyxFQUFDO1lBQ3RELE1BQU0sSUFBSWYsTUFBTWEsVUFBVUcsT0FBTyxJQUFJLENBQUMsT0FBTyxFQUFFWCxTQUFTWSxNQUFNLEVBQUU7UUFDbEU7UUFFQSxPQUFPLE1BQU1aLFNBQVNTLElBQUk7SUFDNUIsRUFBRSxPQUFPSSxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx3Q0FBd0NBO1FBQ3RELE1BQU1BO0lBQ1I7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhZGFyc1xcRGVza3RvcFxcRkxcXG1lZGljb3NcXG1lZGljb3MtZnJvbnRlbmRcXHNyY1xcbGliXFxhcGlcXGNvbGxlZ2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBJbnRlcmZhY2UgZm9yIGNvbGxlZ2UgZGF0YVxuICovXG5leHBvcnQgaW50ZXJmYWNlIENvbGxlZ2VEYXRhIHtcbiAgbmFtZTogc3RyaW5nO1xuICBhZGRyZXNzOiBzdHJpbmc7XG4gIGNvbnRhY3RQaG9uZTogc3RyaW5nO1xuICBjb250YWN0RW1haWw6IHN0cmluZztcbiAgbG9nb1VybD86IHN0cmluZztcbiAgY2l0eT86IHN0cmluZztcbiAgc3RhdGU/OiBzdHJpbmc7XG4gIGNvdW50cnk/OiBzdHJpbmc7XG4gIHBvc3RhbENvZGU/OiBzdHJpbmc7XG4gIHdlYnNpdGU/OiBzdHJpbmc7XG59XG5cbi8qKlxuICogQ3JlYXRlIGEgbmV3IGNvbGxlZ2VcbiAqIEBwYXJhbSBjb2xsZWdlRGF0YSBUaGUgY29sbGVnZSBkYXRhXG4gKiBAcmV0dXJucyBUaGUgY3JlYXRlZCBjb2xsZWdlXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBjcmVhdGVDb2xsZWdlKGNvbGxlZ2VEYXRhOiBDb2xsZWdlRGF0YSkge1xuICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpO1xuICBpZiAoIXRva2VuKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWRcIik7XG4gIH1cbiAgXG4gIHRyeSB7XG4gICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vY29sbGVnZXNgLCB7XG4gICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgXCJBdXRob3JpemF0aW9uXCI6IGBCZWFyZXIgJHt0b2tlbn1gXG4gICAgICB9LFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoY29sbGVnZURhdGEpXG4gICAgfSk7XG4gICAgXG4gICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgY29uc3QgZXJyb3JEYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpLmNhdGNoKCgpID0+ICh7fSkpO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5tZXNzYWdlIHx8IGBFcnJvcjogJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgfVxuICAgIFxuICAgIHJldHVybiBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGNyZWF0aW5nIGNvbGxlZ2U6XCIsIGVycm9yKTtcbiAgICB0aHJvdyBlcnJvcjtcbiAgfVxufVxuXG4vKipcbiAqIEdldCBhbGwgY29sbGVnZXNcbiAqIEByZXR1cm5zIExpc3Qgb2YgY29sbGVnZXNcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldENvbGxlZ2VzKCkge1xuICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpO1xuICBpZiAoIXRva2VuKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWRcIik7XG4gIH1cbiAgXG4gIHRyeSB7XG4gICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vY29sbGVnZXNgLCB7XG4gICAgICBtZXRob2Q6IFwiR0VUXCIsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfVxuICAgIH0pO1xuICAgIFxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBjb2xsZWdlczpcIiwgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogRGVsZXRlIGEgY29sbGVnZVxuICogQHBhcmFtIGlkIENvbGxlZ2UgSURcbiAqIEByZXR1cm5zIFRoZSBkZWxldGVkIGNvbGxlZ2VcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGRlbGV0ZUNvbGxlZ2UoaWQ6IHN0cmluZykge1xuICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKFwiYmFja2VuZFRva2VuXCIpO1xuICBpZiAoIXRva2VuKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFwiQXV0aGVudGljYXRpb24gcmVxdWlyZWRcIik7XG4gIH1cbiAgXG4gIHRyeSB7XG4gICAgY29uc3QgYmFzZVVybCA9IHByb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0FQSV9VUkwgfHwgJ2h0dHA6Ly9sb2NhbGhvc3Q6MzAwMC9hcGknO1xuICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7YmFzZVVybH0vY29sbGVnZXMvJHtpZH1gLCB7XG4gICAgICBtZXRob2Q6IFwiREVMRVRFXCIsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfVxuICAgIH0pO1xuICAgIFxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBkZWxldGluZyBjb2xsZWdlOlwiLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBHZXQgYSBjb2xsZWdlIGJ5IElEXG4gKiBAcGFyYW0gaWQgQ29sbGVnZSBJRFxuICogQHJldHVybnMgVGhlIGNvbGxlZ2VcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldENvbGxlZ2VCeUlkKGlkOiBzdHJpbmcpIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG4gIFxuICB0cnkge1xuICAgIGNvbnN0IGJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpJztcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L2NvbGxlZ2VzLyR7aWR9YCwge1xuICAgICAgbWV0aG9kOiBcIkdFVFwiLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBcIkF1dGhvcml6YXRpb25cIjogYEJlYXJlciAke3Rva2VufWBcbiAgICAgIH1cbiAgICB9KTtcbiAgICBcbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICB9XG4gICAgXG4gICAgcmV0dXJuIGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgZmV0Y2hpbmcgY29sbGVnZTpcIiwgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG5cbi8qKlxuICogVXBkYXRlIGEgY29sbGVnZVxuICogQHBhcmFtIGlkIENvbGxlZ2UgSURcbiAqIEBwYXJhbSBjb2xsZWdlRGF0YSBUaGUgdXBkYXRlZCBjb2xsZWdlIGRhdGFcbiAqIEByZXR1cm5zIFRoZSB1cGRhdGVkIGNvbGxlZ2VcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHVwZGF0ZUNvbGxlZ2UoaWQ6IHN0cmluZywgY29sbGVnZURhdGE6IENvbGxlZ2VEYXRhKSB7XG4gIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oXCJiYWNrZW5kVG9rZW5cIik7XG4gIGlmICghdG9rZW4pIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJBdXRoZW50aWNhdGlvbiByZXF1aXJlZFwiKTtcbiAgfVxuICBcbiAgdHJ5IHtcbiAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG4gICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtiYXNlVXJsfS9jb2xsZWdlcy8ke2lkfWAsIHtcbiAgICAgIG1ldGhvZDogXCJQVVRcIixcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJDb250ZW50LVR5cGVcIjogXCJhcHBsaWNhdGlvbi9qc29uXCIsXG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfSxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGNvbGxlZ2VEYXRhKVxuICAgIH0pO1xuICAgIFxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciB1cGRhdGluZyBjb2xsZWdlOlwiLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cbi8qKlxuICogR2V0IGEgY29sbGVnZSBieSBJRFxuICogQHBhcmFtIGlkIENvbGxlZ2UgSURcbiAqIEByZXR1cm5zIFRoZSBjb2xsZWdlXG4gKi9cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRDb2xsZWdlQW5hbHl0aWNzKGlkOiBzdHJpbmcpIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG4gIFxuICB0cnkge1xuICAgIGNvbnN0IGJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19BUElfVVJMIHx8ICdodHRwOi8vbG9jYWxob3N0OjMwMDAvYXBpJztcbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke2Jhc2VVcmx9L2FuYWx5dGljcy9jb2xsZWdlLyR7aWR9L3N1bW1hcnlgLCB7XG4gICAgICBtZXRob2Q6IFwiR0VUXCIsXG4gICAgICBoZWFkZXJzOiB7XG4gICAgICAgIFwiQXV0aG9yaXphdGlvblwiOiBgQmVhcmVyICR7dG9rZW59YFxuICAgICAgfVxuICAgIH0pO1xuICAgIFxuICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcbiAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEubWVzc2FnZSB8fCBgRXJyb3I6ICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgIH1cbiAgICBcbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBjb2xsZWdlOlwiLCBlcnJvcik7XG4gICAgdGhyb3cgZXJyb3I7XG4gIH1cbn1cblxuLyoqXG4gKiBHZXQgZGFpbHktd2lzZSBxdWVzdGlvbiBwYXBlciBzdGF0aXN0aWNzIGJ5IHN1YmplY3QgZm9yIGEgY29sbGVnZVxuICogQHBhcmFtIGlkIENvbGxlZ2UgSURcbiAqIEBwYXJhbSBzdGFydERhdGUgSVNPIHN0YXJ0IGRhdGUgc3RyaW5nXG4gKiBAcGFyYW0gZW5kRGF0ZSBJU08gZW5kIGRhdGUgc3RyaW5nXG4gKiBAcmV0dXJucyBTdGF0aXN0aWNzIGRhdGFcbiAqL1xuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIGdldFF1ZXN0aW9uUGFwZXJTdGF0c0J5RGF0ZVJhbmdlKGlkOiBzdHJpbmcsIHN0YXJ0RGF0ZTogc3RyaW5nLCBlbmREYXRlOiBzdHJpbmcpIHtcbiAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbShcImJhY2tlbmRUb2tlblwiKTtcbiAgaWYgKCF0b2tlbikge1xuICAgIHRocm93IG5ldyBFcnJvcihcIkF1dGhlbnRpY2F0aW9uIHJlcXVpcmVkXCIpO1xuICB9XG5cbiAgdHJ5IHtcbiAgICBjb25zdCBiYXNlVXJsID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDozMDAwL2FwaSc7XG4gICAgY29uc3QgdXJsID0gYCR7YmFzZVVybH0vYW5hbHl0aWNzL2NvbGxlZ2UvJHtpZH0vcXVlc3Rpb24tcGFwZXJzP3N0YXJ0RGF0ZT0ke2VuY29kZVVSSUNvbXBvbmVudChzdGFydERhdGUpfSZlbmREYXRlPSR7ZW5jb2RlVVJJQ29tcG9uZW50KGVuZERhdGUpfWA7XG5cbiAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHVybCwge1xuICAgICAgbWV0aG9kOiBcIkdFVFwiLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICBcIkF1dGhvcml6YXRpb25cIjogYEJlYXJlciAke3Rva2VufWAsXG4gICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiXG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEVycm9yOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICB9XG5cbiAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBxdWVzdGlvbiBwYXBlciBzdGF0czpcIiwgZXJyb3IpO1xuICAgIHRocm93IGVycm9yO1xuICB9XG59XG4iXSwibmFtZXMiOlsiY3JlYXRlQ29sbGVnZSIsImNvbGxlZ2VEYXRhIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJnZXRJdGVtIiwiRXJyb3IiLCJiYXNlVXJsIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5Iiwib2siLCJlcnJvckRhdGEiLCJqc29uIiwiY2F0Y2giLCJtZXNzYWdlIiwic3RhdHVzIiwiZXJyb3IiLCJjb25zb2xlIiwiZ2V0Q29sbGVnZXMiLCJkZWxldGVDb2xsZWdlIiwiaWQiLCJnZXRDb2xsZWdlQnlJZCIsInVwZGF0ZUNvbGxlZ2UiLCJnZXRDb2xsZWdlQW5hbHl0aWNzIiwiZ2V0UXVlc3Rpb25QYXBlclN0YXRzQnlEYXRlUmFuZ2UiLCJzdGFydERhdGUiLCJlbmREYXRlIiwidXJsIiwiZW5jb2RlVVJJQ29tcG9uZW50Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api/college.ts\n");

/***/ })

};
;