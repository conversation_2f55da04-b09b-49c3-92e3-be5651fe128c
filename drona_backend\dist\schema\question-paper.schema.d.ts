import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class SectionQuestion {
    questionId: MongooseSchema.Types.ObjectId;
    order: number;
    question?: any;
}
export declare class Section {
    name: string;
    description: string;
    order: number;
    sectionMarks: number;
    questions: SectionQuestion[];
    subjectId?: MongooseSchema.Types.ObjectId;
    subjectName?: string;
}
export type QuestionPaperDocument = QuestionPaper & Document;
export declare class QuestionPaper {
    title: string;
    subjectId?: MongooseSchema.Types.ObjectId;
    topicId?: MongooseSchema.Types.ObjectId;
    totalMarks: number;
    duration: number;
    instructions?: string;
    questions: MongooseSchema.Types.ObjectId[];
    generatedBy: MongooseSchema.Types.ObjectId;
    collegeId: MongooseSchema.Types.ObjectId;
    status: string;
    maxQuestions?: number;
    maxGeneration?: number;
    maxDownloads?: number;
    type?: string;
    withAnswers: boolean;
    sections: Section[];
    examType?: string;
    difficultyMode?: string;
    numberOfQuestions?: number;
    isMultiSubject?: boolean;
    subjectCount?: number;
    subjectBreakdown?: Array<{
        subject: string;
        questionCount: number;
        marks: number;
    }>;
}
export declare const QuestionPaperSchema: MongooseSchema<QuestionPaper, import("mongoose").Model<QuestionPaper, any, any, any, Document<unknown, any, QuestionPaper> & QuestionPaper & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, QuestionPaper, Document<unknown, {}, import("mongoose").FlatRecord<QuestionPaper>> & import("mongoose").FlatRecord<QuestionPaper> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
