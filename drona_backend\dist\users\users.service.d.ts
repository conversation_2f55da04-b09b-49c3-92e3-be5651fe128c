import { Model } from 'mongoose';
import { User, UserDocument } from '../schema/user.schema';
import { AssignRoleDto } from './dto/assign-role.dto';
export declare class UsersService {
    private readonly userModel;
    constructor(userModel: Model<UserDocument>);
    findOne(id: string): Promise<UserDocument>;
    findAll(): Promise<UserDocument[]>;
    findByCollege(collegeId: string): Promise<UserDocument[]>;
    assignRole(id: string, assignRoleDto: AssignRoleDto): Promise<UserDocument>;
    create(userData: Partial<User>): Promise<UserDocument>;
    updateCollegeId(userId: string, collegeId: string): Promise<UserDocument>;
}
