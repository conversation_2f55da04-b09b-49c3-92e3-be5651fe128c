export declare enum QuestionDifficulty {
    EASY = "easy",
    MEDIUM = "medium",
    HARD = "hard"
}
export declare enum QuestionType {
    MULTIPLE_CHOICE = "multiple-choice",
    TRUE_FALSE = "true-false",
    DESCRIPTIVE = "descriptive"
}
export declare class CreateQuestionDto {
    content: string;
    options: string[];
    answer: string;
    imageUrls?: string[];
    subjectId: string;
    topicId?: string;
    difficulty: QuestionDifficulty;
    type: QuestionType;
    tags?: string[];
    images?: any;
}
