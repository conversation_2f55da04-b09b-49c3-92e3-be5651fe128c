import { SubjectsService } from './subjects.service';
import { CreateSubjectDto } from './dto/create-subject.dto';
import { UpdateSubjectDto } from './dto/update-subject.dto';
export declare class SubjectsController {
    private readonly subjectsService;
    private readonly logger;
    constructor(subjectsService: SubjectsService);
    create(createSubjectDto: CreateSubjectDto): Promise<import("../schema/subject.schema").Subject>;
    findAll(): Promise<import("../schema/subject.schema").Subject[]>;
    findAllWithTopics(): Promise<any[]>;
    findOne(id: string): Promise<import("../schema/subject.schema").Subject>;
    update(id: string, updateSubjectDto: UpdateSubjectDto): Promise<import("../schema/subject.schema").Subject>;
    remove(id: string): Promise<void>;
}
