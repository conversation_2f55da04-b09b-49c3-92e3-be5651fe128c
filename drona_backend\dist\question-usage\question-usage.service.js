"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var QuestionUsageService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionUsageService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const question_usage_schema_1 = require("../schema/question-usage.schema");
let QuestionUsageService = QuestionUsageService_1 = class QuestionUsageService {
    constructor(questionUsageModel) {
        this.questionUsageModel = questionUsageModel;
        this.logger = new common_1.Logger(QuestionUsageService_1.name);
    }
    async recordQuestionUsage(data) {
        try {
            const usageRecord = new this.questionUsageModel({
                ...data,
                usedAt: new Date(),
                status: 'active',
            });
            return await usageRecord.save();
        }
        catch (error) {
            if (error.code === 11000) {
                this.logger.warn(`Question ${data.questionId} already used by college ${data.collegeId}`);
                return null;
            }
            throw error;
        }
    }
    async recordMultipleQuestionUsage(usageData) {
        let recorded = 0;
        let skipped = 0;
        for (const data of usageData) {
            const result = await this.recordQuestionUsage(data);
            if (result !== null) {
                recorded++;
            }
            else {
                skipped++;
            }
        }
        return { recorded, skipped };
    }
    async getUsedQuestionIds(collegeId, filters) {
        const query = {
            collegeId,
            status: filters?.status || 'active',
        };
        if (filters?.subjectId) {
            query.subjectId = filters.subjectId;
        }
        if (filters?.topicId) {
            query.topicId = filters.topicId;
        }
        const usageRecords = await this.questionUsageModel
            .find(query)
            .select('questionId')
            .lean()
            .exec();
        return usageRecords.map((record) => record.questionId.toString());
    }
    async getUnusedQuestions(collegeId, availableQuestionIds, filters) {
        const usedQuestionIds = await this.getUsedQuestionIds(collegeId, filters);
        return availableQuestionIds.filter((questionId) => !usedQuestionIds.includes(questionId));
    }
};
exports.QuestionUsageService = QuestionUsageService;
exports.QuestionUsageService = QuestionUsageService = QuestionUsageService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(question_usage_schema_1.QuestionUsage.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], QuestionUsageService);
//# sourceMappingURL=question-usage.service.js.map