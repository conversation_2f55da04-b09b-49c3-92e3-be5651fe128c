"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const analytics_controller_1 = require("./analytics.controller");
const analytics_service_1 = require("./analytics.service");
const user_activity_schema_1 = require("../../schema/user-activity.schema");
const user_schema_1 = require("../../schema/user.schema");
const question_paper_schema_1 = require("../../schema/question-paper.schema");
const question_schema_1 = require("../../schema/question.schema");
const college_schema_1 = require("../../schema/college.schema");
const download_schema_1 = require("../../schema/download.schema");
let AnalyticsModule = class AnalyticsModule {
};
exports.AnalyticsModule = AnalyticsModule;
exports.AnalyticsModule = AnalyticsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: user_activity_schema_1.UserActivity.name, schema: user_activity_schema_1.UserActivitySchema },
                { name: user_schema_1.User.name, schema: user_schema_1.UserSchema },
                { name: question_paper_schema_1.QuestionPaper.name, schema: question_paper_schema_1.QuestionPaperSchema },
                { name: question_schema_1.Question.name, schema: question_schema_1.QuestionSchema },
                { name: college_schema_1.College.name, schema: college_schema_1.CollegeSchema },
                { name: download_schema_1.Download.name, schema: download_schema_1.DownloadSchema },
            ]),
        ],
        controllers: [analytics_controller_1.AnalyticsController],
        providers: [analytics_service_1.AnalyticsService],
    })
], AnalyticsModule);
//# sourceMappingURL=analytics.module.js.map