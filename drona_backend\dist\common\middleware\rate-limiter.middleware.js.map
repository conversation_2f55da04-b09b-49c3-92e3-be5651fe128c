{"version": 3, "file": "rate-limiter.middleware.js", "sourceRoot": "", "sources": ["../../../src/common/middleware/rate-limiter.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAKwB;AACxB,2CAA+C;AASxC,IAAM,qBAAqB,GAA3B,MAAM,qBAAqB;IAKhC,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAJ/B,eAAU,GAAG,IAAI,GAAG,EAAyB,CAAC;QAM7D,MAAM,SAAS,GACb,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,mBAAmB,CAAC,IAAI,KAAK,CAAC;QAC/D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;QAGhD,IAAI,CAAC,WAAW;YACd,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,yBAAyB,CAAC,IAAI,GAAG,CAAC;IACrE,CAAC;IAED,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QAEjD,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAU,mBAAmB,CAAC,KAAK,KAAK,EAAE,CAAC;YACnE,OAAO,IAAI,EAAE,CAAC;QAChB,CAAC;QAGD,MAAM,EAAE,GACN,GAAG,CAAC,EAAE;YACL,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAY,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;YAChE,SAAS,CAAC;QAGZ,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAGvB,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QAE5C,IAAI,CAAC,aAAa,IAAI,GAAG,GAAG,aAAa,CAAC,SAAS,EAAE,CAAC;YAEpD,aAAa,GAAG;gBACd,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ;aAC/B,CAAC;QACJ,CAAC;aAAM,CAAC;YAEN,aAAa,CAAC,KAAK,EAAE,CAAC;YAGtB,IAAI,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC3C,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC;gBAGpD,GAAG,CAAC,SAAS,CACX,aAAa,EACb,IAAI,CAAC,IAAI,CAAC,CAAC,aAAa,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAClD,CAAC;gBACF,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;gBACrD,GAAG,CAAC,SAAS,CAAC,uBAAuB,EAAE,CAAC,CAAC,CAAC;gBAC1C,GAAG,CAAC,SAAS,CACX,mBAAmB,EACnB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,CAC1C,CAAC;gBAEF,MAAM,IAAI,sBAAa,CACrB,wCAAwC,SAAS,CAAC,WAAW,EAAE,EAAE,EACjE,mBAAU,CAAC,iBAAiB,CAC7B,CAAC;YACJ,CAAC;QACH,CAAC;QAGD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,EAAE,aAAa,CAAC,CAAC;QAGvC,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACrD,GAAG,CAAC,SAAS,CACX,uBAAuB,EACvB,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC,KAAK,CACvC,CAAC;QACF,GAAG,CAAC,SAAS,CACX,mBAAmB,EACnB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,GAAG,IAAI,CAAC,CAC1C,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAEO,eAAe,CAAC,OAAe;QACrC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAC/C,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QACxB,CAAC;QAED,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QACrC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAEtB,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,GAAG;gBACN,OAAO,KAAK,GAAG,IAAI,CAAC;YACtB,KAAK,GAAG;gBACN,OAAO,KAAK,GAAG,EAAE,GAAG,IAAI,CAAC;YAC3B,KAAK,GAAG;gBACN,OAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YAChC,KAAK,GAAG;gBACN,OAAO,KAAK,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;YACrC;gBACE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC1B,CAAC;IACH,CAAC;CACF,CAAA;AA1GY,sDAAqB;gCAArB,qBAAqB;IADjC,IAAA,mBAAU,GAAE;qCAMwB,sBAAa;GALrC,qBAAqB,CA0GjC"}