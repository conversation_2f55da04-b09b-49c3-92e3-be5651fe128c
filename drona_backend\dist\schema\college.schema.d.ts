import { Document } from 'mongoose';
export type CollegeDocument = College & Document;
export declare class College {
    name: string;
    address: string;
    city: string;
    state: string;
    country: string;
    postalCode: string;
    contactEmail: string;
    contactPhone: string;
    website: string;
    logoUrl: string;
    status: string;
    teachers: string[];
    admins: string[];
}
export declare const CollegeSchema: import("mongoose").Schema<College, import("mongoose").Model<College, any, any, any, Document<unknown, any, College> & College & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, College, Document<unknown, {}, import("mongoose").FlatRecord<College>> & import("mongoose").FlatRecord<College> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
