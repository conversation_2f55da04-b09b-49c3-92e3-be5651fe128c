import { AnalyticsService } from './analytics.service';
export declare class AnalyticsController {
    private readonly analyticsService;
    constructor(analyticsService: AnalyticsService);
    getPlatformSummary(): Promise<{
        totalColleges: number;
        totalTeachers: number;
        totalQuestions: number;
        totalPapers: number;
        totalDownloads: number;
        recentActivity: {
            logins: any;
            paperGenerations: any;
            questionCreations: any;
            downloads: number;
        };
    }>;
    getTopColleges(): Promise<{
        byDownloads: any[];
        byTeachers: any[];
    }>;
    getQuestionUsage(): Promise<{
        usedQuestions: any[];
        unusedQuestions: any[];
        byDifficulty: any;
        byType: any;
    }>;
    getQuestionStats(): Promise<{
        totalQuestions: any;
        bySubject: any;
        byTopic: any;
        byDifficulty: any;
        byType: any;
    }>;
    getCollegeAnalytics(startDate?: string, endDate?: string, limit?: string, sortBy?: string): Promise<{
        summary: {
            totalColleges: number;
            totalTeachers: any;
            totalQuestions: any;
            totalPapers: any;
            totalDownloads: any;
        };
        colleges: any[];
        filters: {
            startDate: Date | null;
            endDate: Date | null;
            limit: number;
            sortBy: string;
        };
    }>;
    getUsageTrends(year?: string, startDate?: string, endDate?: string): Promise<{
        data: any[];
        summary: {
            totalMonths: number;
            totalQuestionsCreated: any;
            totalPapersGenerated: any;
            averageMonthlyQuestions: number;
            averageMonthlyPapers: number;
        };
        dateRange: {
            startDate: Date;
            endDate: Date;
        };
    }>;
    getCollegeGrowth(year?: string, startDate?: string, endDate?: string, view?: string): Promise<{
        data: any[];
        summary: {
            totalMonths: number;
            totalCollegesAdded: any;
            averageMonthlyGrowth: number;
            totalTargetAchievement: number;
            totalRevenue: any;
        };
        targets: {
            monthlyTarget: number;
            yearlyTarget: number;
            currentProgress: number;
        };
        dateRange: {
            startDate: Date;
            endDate: Date;
        };
    }>;
}
