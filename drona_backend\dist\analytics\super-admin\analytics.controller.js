"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsController = void 0;
const common_1 = require("@nestjs/common");
const analytics_service_1 = require("./analytics.service");
const jwt_auth_guard_1 = require("../../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../../auth/guards/roles.guard");
const roles_decorator_1 = require("../../auth/guards/roles.decorator");
const swagger_1 = require("@nestjs/swagger");
let AnalyticsController = class AnalyticsController {
    constructor(analyticsService) {
        this.analyticsService = analyticsService;
    }
    getPlatformSummary() {
        return this.analyticsService.getPlatformSummary();
    }
    getTopColleges() {
        return this.analyticsService.getTopColleges();
    }
    getQuestionUsage() {
        return this.analyticsService.getQuestionUsage();
    }
    getQuestionStats() {
        return this.analyticsService.getQuestionStats();
    }
    getCollegeAnalytics(startDate, endDate, limit, sortBy) {
        const filters = {};
        if (startDate) {
            filters.startDate = new Date(startDate);
        }
        if (endDate) {
            filters.endDate = new Date(endDate);
        }
        if (limit) {
            filters.limit = parseInt(limit, 10);
        }
        if (sortBy) {
            filters.sortBy = sortBy;
        }
        return this.analyticsService.getCollegeAnalytics(filters);
    }
    getUsageTrends(year, startDate, endDate) {
        const filters = {};
        if (year) {
            filters.year = parseInt(year, 10);
        }
        if (startDate) {
            filters.startDate = new Date(startDate);
        }
        if (endDate) {
            filters.endDate = new Date(endDate);
        }
        return this.analyticsService.getUsageTrends(filters);
    }
    getCollegeGrowth(year, startDate, endDate, view) {
        const filters = {};
        if (year) {
            filters.year = parseInt(year, 10);
        }
        if (startDate) {
            filters.startDate = new Date(startDate);
        }
        if (endDate) {
            filters.endDate = new Date(endDate);
        }
        if (view) {
            filters.view = view;
        }
        return this.analyticsService.getCollegeGrowth(filters);
    }
};
exports.AnalyticsController = AnalyticsController;
__decorate([
    (0, common_1.Get)('platform-summary'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get platform summary',
        description: 'Returns a summary of platform statistics',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns platform summary data',
        schema: {
            type: 'object',
            properties: {
                totalColleges: { type: 'number', example: 25 },
                activeColleges: { type: 'number', example: 23 },
                totalTeachers: { type: 'number', example: 450 },
                activeTeachers: { type: 'number', example: 420 },
                totalQuestionPapers: { type: 'number', example: 1560 },
                totalQuestions: { type: 'number', example: 23400 },
                totalDownloads: { type: 'number', example: 3200 },
                activeUsersLast30Days: { type: 'number', example: 380 },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getPlatformSummary", null);
__decorate([
    (0, common_1.Get)('top-colleges'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get top colleges',
        description: 'Returns statistics for the top performing colleges',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns top colleges data',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    collegeName: { type: 'string', example: 'Harvard University' },
                    teacherCount: { type: 'number', example: 45 },
                    questionPaperCount: { type: 'number', example: 156 },
                    questionCount: { type: 'number', example: 2340 },
                    downloadCount: { type: 'number', example: 320 },
                    activeTeacherPercentage: { type: 'number', example: 93.3 },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getTopColleges", null);
__decorate([
    (0, common_1.Get)('questions/usage'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get question usage statistics',
        description: 'Returns question usage statistics including questions used in papers, unused questions, and distribution by difficulty and type',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns question usage statistics',
        schema: {
            type: 'object',
            properties: {
                totalQuestions: { type: 'number', example: 23400 },
                usedQuestions: { type: 'number', example: 18560 },
                unusedQuestions: { type: 'number', example: 4840 },
                difficultyDistribution: {
                    type: 'object',
                    properties: {
                        easy: { type: 'number', example: 7800 },
                        medium: { type: 'number', example: 9360 },
                        hard: { type: 'number', example: 6240 },
                    },
                },
                typeDistribution: {
                    type: 'object',
                    properties: {
                        'multiple-choice': { type: 'number', example: 15210 },
                        'true-false': { type: 'number', example: 4680 },
                        'fill-in-the-blank': { type: 'number', example: 3510 },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getQuestionUsage", null);
__decorate([
    (0, common_1.Get)('questions/stats'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get question statistics',
        description: 'Returns detailed statistics about questions',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns question statistics',
        schema: {
            type: 'object',
            properties: {
                totalQuestions: { type: 'number', example: 23400 },
                approvedQuestions: { type: 'number', example: 21060 },
                pendingQuestions: { type: 'number', example: 1170 },
                rejectedQuestions: { type: 'number', example: 1170 },
                questionsByCollege: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            collegeId: {
                                type: 'string',
                                example: '60d21b4667d0d8992e610c85',
                            },
                            collegeName: { type: 'string', example: 'Harvard University' },
                            questionCount: { type: 'number', example: 2340 },
                        },
                    },
                },
                questionsBySubject: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            subjectId: {
                                type: 'string',
                                example: '60d21b4667d0d8992e610c85',
                            },
                            subjectName: { type: 'string', example: 'Mathematics' },
                            questionCount: { type: 'number', example: 5850 },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getQuestionStats", null);
__decorate([
    (0, common_1.Get)('colleges/analytics'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get college-wise analytics',
        description: 'Returns comprehensive analytics for each college including questions, usage, downloads, and activity metrics',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        description: 'Start date for filtering (ISO string)',
        required: false,
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        description: 'End date for filtering (ISO string)',
        required: false,
        example: '2024-12-31T23:59:59.999Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'limit',
        description: 'Number of colleges to return',
        required: false,
        example: 50,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'sortBy',
        description: 'Sort criteria',
        required: false,
        enum: [
            'totalActivity',
            'questionCount',
            'downloadCount',
            'teacherCount',
            'paperCount',
        ],
        example: 'totalActivity',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns college-wise analytics data',
        schema: {
            type: 'object',
            properties: {
                summary: {
                    type: 'object',
                    properties: {
                        totalColleges: { type: 'number', example: 25 },
                        totalTeachers: { type: 'number', example: 450 },
                        totalQuestions: { type: 'number', example: 23400 },
                        totalPapers: { type: 'number', example: 1560 },
                        totalDownloads: { type: 'number', example: 3200 },
                    },
                },
                colleges: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            collegeId: {
                                type: 'string',
                                example: '60d21b4667d0d8992e610c85',
                            },
                            collegeName: { type: 'string', example: 'Harvard University' },
                            status: { type: 'string', example: 'active' },
                            metrics: {
                                type: 'object',
                                properties: {
                                    teacherCount: { type: 'number', example: 45 },
                                    activeTeachers: { type: 'number', example: 42 },
                                    questionCount: { type: 'number', example: 2340 },
                                    approvedQuestions: { type: 'number', example: 2106 },
                                    pendingQuestions: { type: 'number', example: 117 },
                                    rejectedQuestions: { type: 'number', example: 117 },
                                    paperCount: { type: 'number', example: 156 },
                                    downloadCount: { type: 'number', example: 320 },
                                    totalActivity: { type: 'number', example: 2822 },
                                },
                            },
                            subjectBreakdown: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        subjectId: {
                                            type: 'string',
                                            example: '60d21b4667d0d8992e610c86',
                                        },
                                        subjectName: { type: 'string', example: 'Mathematics' },
                                        questionCount: { type: 'number', example: 585 },
                                        paperCount: { type: 'number', example: 39 },
                                        downloadCount: { type: 'number', example: 80 },
                                    },
                                },
                            },
                            recentActivity: {
                                type: 'object',
                                properties: {
                                    last30Days: {
                                        type: 'object',
                                        properties: {
                                            questionsCreated: { type: 'number', example: 45 },
                                            papersGenerated: { type: 'number', example: 12 },
                                            downloads: { type: 'number', example: 67 },
                                            activeTeachers: { type: 'number', example: 38 },
                                        },
                                    },
                                },
                            },
                        },
                    },
                },
                filters: {
                    type: 'object',
                    properties: {
                        startDate: { type: 'string', format: 'date-time', nullable: true },
                        endDate: { type: 'string', format: 'date-time', nullable: true },
                        limit: { type: 'number', example: 50 },
                        sortBy: { type: 'string', example: 'totalActivity' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Query)('startDate')),
    __param(1, (0, common_1.Query)('endDate')),
    __param(2, (0, common_1.Query)('limit')),
    __param(3, (0, common_1.Query)('sortBy')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getCollegeAnalytics", null);
__decorate([
    (0, common_1.Get)('usage-trends'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get usage trends',
        description: 'Returns monthly usage data for questions created and papers generated (bar chart data)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'year',
        description: 'Year to filter by (e.g., 2024)',
        required: false,
        example: 2024,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        description: 'Start date for custom range (ISO string)',
        required: false,
        example: '2024-02-05T00:00:00.000Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        description: 'End date for custom range (ISO string)',
        required: false,
        example: '2024-03-06T23:59:59.999Z',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns monthly usage trends data',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            month: { type: 'string', example: '2024-01' },
                            monthName: { type: 'string', example: 'January 2024' },
                            questionsCreated: { type: 'number', example: 1250 },
                            papersGenerated: { type: 'number', example: 340 },
                            totalUsage: { type: 'number', example: 1590 },
                        },
                    },
                },
                summary: {
                    type: 'object',
                    properties: {
                        totalMonths: { type: 'number', example: 12 },
                        totalQuestionsCreated: { type: 'number', example: 15000 },
                        totalPapersGenerated: { type: 'number', example: 4080 },
                        averageMonthlyQuestions: { type: 'number', example: 1250 },
                        averageMonthlyPapers: { type: 'number', example: 340 },
                    },
                },
                dateRange: {
                    type: 'object',
                    properties: {
                        startDate: { type: 'string', format: 'date-time' },
                        endDate: { type: 'string', format: 'date-time' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Query)('year')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getUsageTrends", null);
__decorate([
    (0, common_1.Get)('college-growth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get college growth trends',
        description: 'Returns monthly college growth data with target comparisons (line chart data)',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'year',
        description: 'Year to filter by (e.g., 2024)',
        required: false,
        example: 2024,
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        description: 'Start date for custom range (ISO string)',
        required: false,
        example: '2024-02-05T00:00:00.000Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        description: 'End date for custom range (ISO string)',
        required: false,
        example: '2024-03-06T23:59:59.999Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'view',
        description: 'Data view type',
        required: false,
        enum: ['overview', 'sales', 'revenue'],
        example: 'overview',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns monthly college growth data',
        schema: {
            type: 'object',
            properties: {
                data: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            month: { type: 'string', example: '2024-01' },
                            monthName: { type: 'string', example: 'January 2024' },
                            collegesAdded: { type: 'number', example: 5 },
                            cumulativeColleges: { type: 'number', example: 25 },
                            monthlyTarget: { type: 'number', example: 6 },
                            targetAchievement: { type: 'number', example: 83.33 },
                            revenue: { type: 'number', example: 25000 },
                            salesMetrics: {
                                type: 'object',
                                properties: {
                                    conversions: { type: 'number', example: 5 },
                                    leads: { type: 'number', example: 12 },
                                    conversionRate: { type: 'number', example: 41.67 },
                                },
                            },
                        },
                    },
                },
                summary: {
                    type: 'object',
                    properties: {
                        totalMonths: { type: 'number', example: 12 },
                        totalCollegesAdded: { type: 'number', example: 60 },
                        averageMonthlyGrowth: { type: 'number', example: 5 },
                        totalTargetAchievement: { type: 'number', example: 85.5 },
                        totalRevenue: { type: 'number', example: 300000 },
                    },
                },
                targets: {
                    type: 'object',
                    properties: {
                        monthlyTarget: { type: 'number', example: 6 },
                        yearlyTarget: { type: 'number', example: 72 },
                        currentProgress: { type: 'number', example: 83.33 },
                    },
                },
                dateRange: {
                    type: 'object',
                    properties: {
                        startDate: { type: 'string', format: 'date-time' },
                        endDate: { type: 'string', format: 'date-time' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    __param(0, (0, common_1.Query)('year')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __param(3, (0, common_1.Query)('view')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", void 0)
], AnalyticsController.prototype, "getCollegeGrowth", null);
exports.AnalyticsController = AnalyticsController = __decorate([
    (0, swagger_1.ApiTags)('Analytics - Super Admin'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('analytics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    (0, roles_decorator_1.Roles)('superAdmin'),
    __metadata("design:paramtypes", [analytics_service_1.AnalyticsService])
], AnalyticsController);
//# sourceMappingURL=analytics.controller.js.map