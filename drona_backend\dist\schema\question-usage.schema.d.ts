import { Document, Schema as MongooseSchema } from 'mongoose';
export type QuestionUsageDocument = QuestionUsage & Document;
export declare class QuestionUsage {
    collegeId: MongooseSchema.Types.ObjectId;
    questionId: MongooseSchema.Types.ObjectId;
    questionPaperId: MongooseSchema.Types.ObjectId;
    usedBy: MongooseSchema.Types.ObjectId;
    subjectId: MongooseSchema.Types.ObjectId;
    topicId?: MongooseSchema.Types.ObjectId;
    usedAt: Date;
    metadata?: {
        examType?: string;
        difficulty?: string;
        section?: string;
        marks?: number;
    };
    status: string;
}
export declare const QuestionUsageSchema: MongooseSchema<QuestionUsage, import("mongoose").Model<QuestionUsage, any, any, any, Document<unknown, any, QuestionUsage> & QuestionUsage & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, QuestionUsage, Document<unknown, {}, import("mongoose").FlatRecord<QuestionUsage>> & import("mongoose").FlatRecord<QuestionUsage> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
