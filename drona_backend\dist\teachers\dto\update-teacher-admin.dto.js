"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateTeacherAdminDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class UpdateTeacherAdminDto {
}
exports.UpdateTeacherAdminDto = UpdateTeacherAdminDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Teacher phone number',
        example: '+****************',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateTeacherAdminDto.prototype, "phone", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Department the teacher belongs to',
        example: 'Computer Science',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateTeacherAdminDto.prototype, "department", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Teacher designation/title',
        example: 'Associate Professor',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateTeacherAdminDto.prototype, "designation", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User account status',
        example: 'active',
        enum: ['active', 'inactive'],
    }),
    (0, class_validator_1.IsEnum)(['active', 'inactive']),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateTeacherAdminDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User role',
        example: 'teacher',
        enum: ['teacher', 'collegeAdmin'],
    }),
    (0, class_validator_1.IsEnum)(['teacher', 'collegeAdmin']),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], UpdateTeacherAdminDto.prototype, "role", void 0);
//# sourceMappingURL=update-teacher-admin.dto.js.map