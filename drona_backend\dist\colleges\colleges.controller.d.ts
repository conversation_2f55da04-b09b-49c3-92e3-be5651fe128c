import { CollegesService } from './colleges.service';
import { CreateCollegeDto } from './dto/create-college.dto';
import { UpdateCollegeDto } from './dto/update-college.dto';
export declare class CollegesController {
    private readonly collegesService;
    constructor(collegesService: CollegesService);
    create(createCollegeDto: CreateCollegeDto): Promise<import("../schema/college.schema").College>;
    findAll(): Promise<import("../schema/college.schema").College[]>;
    findOne(id: string): Promise<import("../schema/college.schema").College>;
    update(id: string, updateCollegeDto: UpdateCollegeDto): Promise<import("../schema/college.schema").College>;
    remove(id: string): Promise<import("../schema/college.schema").College>;
    findAllPublic(): Promise<{
        _id: string;
        name: string;
        logoUrl: string;
    }[]>;
}
