"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionUsageSchema = exports.QuestionUsage = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const swagger_1 = require("@nestjs/swagger");
let QuestionUsage = class QuestionUsage {
};
exports.QuestionUsage = QuestionUsage;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'College ID that used the question',
        example: '60d21b4667d0d8992e610c85',
        type: String,
        required: true,
    }),
    (0, mongoose_1.Prop)({
        type: mongoose_2.Schema.Types.ObjectId,
        required: true,
        ref: 'College',
        index: true,
    }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionUsage.prototype, "collegeId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Question ID that was used',
        example: '60d21b4667d0d8992e610c86',
        type: String,
        required: true,
    }),
    (0, mongoose_1.Prop)({
        type: mongoose_2.Schema.Types.ObjectId,
        required: true,
        ref: 'Question',
        index: true,
    }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionUsage.prototype, "questionId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Question Paper ID where the question was used',
        example: '60d21b4667d0d8992e610c87',
        type: String,
        required: true,
    }),
    (0, mongoose_1.Prop)({
        type: mongoose_2.Schema.Types.ObjectId,
        required: true,
        ref: 'QuestionPaper',
    }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionUsage.prototype, "questionPaperId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who generated the question paper',
        example: '60d21b4667d0d8992e610c88',
        type: String,
        required: true,
    }),
    (0, mongoose_1.Prop)({
        type: mongoose_2.Schema.Types.ObjectId,
        required: true,
        ref: 'User',
        index: true,
    }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionUsage.prototype, "usedBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Subject ID of the question',
        example: '60d21b4667d0d8992e610c89',
        type: String,
        required: true,
    }),
    (0, mongoose_1.Prop)({
        type: mongoose_2.Schema.Types.ObjectId,
        required: true,
        ref: 'Subject',
        index: true,
    }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionUsage.prototype, "subjectId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Topic ID of the question (optional)',
        example: '60d21b4667d0d8992e610c90',
        type: String,
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'Topic', index: true }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], QuestionUsage.prototype, "topicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Date when the question was first used',
        example: '2024-01-15T10:30:00.000Z',
    }),
    (0, mongoose_1.Prop)({ required: true, index: true }),
    __metadata("design:type", Date)
], QuestionUsage.prototype, "usedAt", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional metadata about the usage',
        example: { examType: 'NEET', difficulty: 'medium', section: 'Physics' },
    }),
    (0, mongoose_1.Prop)({ type: Object }),
    __metadata("design:type", Object)
], QuestionUsage.prototype, "metadata", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Status of the usage record',
        example: 'active',
        enum: ['active', 'reset'],
        default: 'active',
    }),
    (0, mongoose_1.Prop)({ enum: ['active', 'reset'], default: 'active', index: true }),
    __metadata("design:type", String)
], QuestionUsage.prototype, "status", void 0);
exports.QuestionUsage = QuestionUsage = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], QuestionUsage);
exports.QuestionUsageSchema = mongoose_1.SchemaFactory.createForClass(QuestionUsage);
exports.QuestionUsageSchema.index({ collegeId: 1, questionId: 1 }, { unique: true });
exports.QuestionUsageSchema.index({ collegeId: 1, subjectId: 1 });
exports.QuestionUsageSchema.index({ collegeId: 1, topicId: 1 });
exports.QuestionUsageSchema.index({ collegeId: 1, usedAt: -1 });
exports.QuestionUsageSchema.index({ questionPaperId: 1 });
exports.QuestionUsageSchema.index({ usedBy: 1, usedAt: -1 });
//# sourceMappingURL=question-usage.schema.js.map