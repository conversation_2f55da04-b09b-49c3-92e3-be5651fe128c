import { NestMiddleware } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request, Response, NextFunction } from 'express';
export declare class RateLimiterMiddleware implements NestMiddleware {
    private configService;
    private readonly requestMap;
    private readonly windowMs;
    private readonly maxRequests;
    constructor(configService: ConfigService);
    use(req: Request, res: Response, next: NextFunction): void;
    private parseTimeWindow;
}
