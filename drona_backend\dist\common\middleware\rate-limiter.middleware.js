"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RateLimiterMiddleware = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let RateLimiterMiddleware = class RateLimiterMiddleware {
    constructor(configService) {
        this.configService = configService;
        this.requestMap = new Map();
        const windowStr = this.configService.get('RATE_LIMIT_WINDOW') || '15m';
        this.windowMs = this.parseTimeWindow(windowStr);
        this.maxRequests =
            this.configService.get('RATE_LIMIT_MAX_REQUESTS') || 100;
    }
    use(req, res, next) {
        if (this.configService.get('ENABLE_RATE_LIMIT') === false) {
            return next();
        }
        const ip = req.ip ||
            req.headers['x-forwarded-for']?.split(',')[0].trim() ||
            'unknown';
        const now = Date.now();
        let rateLimitInfo = this.requestMap.get(ip);
        if (!rateLimitInfo || now > rateLimitInfo.resetTime) {
            rateLimitInfo = {
                count: 1,
                resetTime: now + this.windowMs,
            };
        }
        else {
            rateLimitInfo.count++;
            if (rateLimitInfo.count > this.maxRequests) {
                const resetTime = new Date(rateLimitInfo.resetTime);
                res.setHeader('Retry-After', Math.ceil((rateLimitInfo.resetTime - now) / 1000));
                res.setHeader('X-RateLimit-Limit', this.maxRequests);
                res.setHeader('X-RateLimit-Remaining', 0);
                res.setHeader('X-RateLimit-Reset', Math.ceil(rateLimitInfo.resetTime / 1000));
                throw new common_1.HttpException(`Rate limit exceeded. Try again after ${resetTime.toISOString()}`, common_1.HttpStatus.TOO_MANY_REQUESTS);
            }
        }
        this.requestMap.set(ip, rateLimitInfo);
        res.setHeader('X-RateLimit-Limit', this.maxRequests);
        res.setHeader('X-RateLimit-Remaining', this.maxRequests - rateLimitInfo.count);
        res.setHeader('X-RateLimit-Reset', Math.ceil(rateLimitInfo.resetTime / 1000));
        next();
    }
    parseTimeWindow(timeStr) {
        const match = timeStr.match(/^(\d+)([smhd])$/);
        if (!match) {
            return 15 * 60 * 1000;
        }
        const value = parseInt(match[1], 10);
        const unit = match[2];
        switch (unit) {
            case 's':
                return value * 1000;
            case 'm':
                return value * 60 * 1000;
            case 'h':
                return value * 60 * 60 * 1000;
            case 'd':
                return value * 24 * 60 * 60 * 1000;
            default:
                return 15 * 60 * 1000;
        }
    }
};
exports.RateLimiterMiddleware = RateLimiterMiddleware;
exports.RateLimiterMiddleware = RateLimiterMiddleware = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RateLimiterMiddleware);
//# sourceMappingURL=rate-limiter.middleware.js.map