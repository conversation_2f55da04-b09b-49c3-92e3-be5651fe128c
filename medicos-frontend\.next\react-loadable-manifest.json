{"..\\node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsComponent/SUXNJKMM.js": {"id": "..\\node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsComponent/SUXNJKMM.js", "files": ["static/chunks/_app-pages-browser_node_modules_tanstack_query-devtools_build_DevtoolsComponent_SUXNJKMM_js.js"]}, "..\\node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsPanelComponent/LDUQ2GE4.js": {"id": "..\\node_modules\\@tanstack\\query-devtools\\build\\dev.js -> ./DevtoolsPanelComponent/LDUQ2GE4.js", "files": ["static/chunks/_app-pages-browser_node_modules_tanstack_query-devtools_build_DevtoolsPanelComponent_LDUQ2GE4_js.js"]}, "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> canvg": {"id": "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> canvg", "files": ["static/chunks/_app-pages-browser_node_modules_canvg_lib_index_es_js.js"]}, "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> dompurify": {"id": "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> dompurify", "files": ["static/chunks/_app-pages-browser_node_modules_dompurify_dist_purify_es_mjs.js"]}, "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> html2canvas": {"id": "..\\node_modules\\jspdf\\dist\\jspdf.es.min.js -> html2canvas", "files": ["static/chunks/_app-pages-browser_node_modules_html2canvas_dist_html2canvas_js.js"]}, "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts": {"id": "..\\node_modules\\next\\dist\\client\\components\\react-dev-overlay\\utils\\use-websocket.js -> @vercel/turbopack-ecmascript-runtime/browser/dev/hmr-client/hmr-client.ts", "files": ["static/chunks/_app-pages-browser_node_modules_next_dist_client_dev_noop-turbopack-hmr_js.js"]}, "app\\teacher\\downloaded-papers\\page.tsx -> @/utils/pdfGenerator": {"id": "app\\teacher\\downloaded-papers\\page.tsx -> @/utils/pdfGenerator", "files": ["static/css/_app-pages-browser_src_utils_pdfGenerator_ts.css", "static/chunks/_app-pages-browser_src_utils_pdfGenerator_ts.js"]}, "lib\\api\\questionPapers.ts -> @/utils/pdfGenerator": {"id": "lib\\api\\questionPapers.ts -> @/utils/pdfGenerator", "files": ["static/css/_app-pages-browser_src_utils_pdfGenerator_ts.css", "static/chunks/_app-pages-browser_src_utils_pdfGenerator_ts.js"]}}