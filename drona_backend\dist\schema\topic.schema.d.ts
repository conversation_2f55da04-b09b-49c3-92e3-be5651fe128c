import { Document, Schema as MongooseSchema } from 'mongoose';
import { Subject } from './subject.schema';
export type TopicDocument = Topic & Document;
export declare class Topic {
    name: string;
    subjectId: Subject;
    description: string;
}
export declare const TopicSchema: MongooseSchema<Topic, import("mongoose").Model<Topic, any, any, any, Document<unknown, any, Topic> & Topic & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Topic, Document<unknown, {}, import("mongoose").FlatRecord<Topic>> & import("mongoose").FlatRecord<Topic> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
