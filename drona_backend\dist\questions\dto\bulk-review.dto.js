"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BulkReviewDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class BulkReviewDto {
}
exports.BulkReviewDto = BulkReviewDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Array of question IDs to review',
        example: ['60d21b4667d0d8992e610c85', '60d21b4967d0d8992e610c86'],
        type: [String],
        minItems: 1,
    }),
    (0, class_validator_1.IsArray)(),
    (0, class_validator_1.ArrayMinSize)(1, { message: 'At least one question ID must be provided' }),
    (0, class_validator_1.IsMongoId)({
        each: true,
        message: 'Each question ID must be a valid MongoDB ID',
    }),
    __metadata("design:type", Array)
], BulkReviewDto.prototype, "questionIds", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Review status (approved or rejected)',
        example: 'approved',
        enum: ['approved', 'rejected'],
    }),
    (0, class_validator_1.IsEnum)(['approved', 'rejected'], {
        message: 'Status must be either approved or rejected',
    }),
    __metadata("design:type", String)
], BulkReviewDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Optional notes for the review',
        example: 'These questions meet our quality standards',
        required: false,
        maxLength: 500,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], BulkReviewDto.prototype, "notes", void 0);
//# sourceMappingURL=bulk-review.dto.js.map