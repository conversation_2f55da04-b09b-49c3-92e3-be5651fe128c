{"version": 3, "file": "teacher-generate-paper.dto.js", "sourceRoot": "", "sources": ["../../../src/question-papers/dto/teacher-generate-paper.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDASyB;AACzB,6CAAmE;AAEnE,IAAY,QAOX;AAPD,WAAY,QAAQ;IAClB,yBAAa,CAAA;IACb,uBAAW,CAAA;IACX,uBAAW,CAAA;IACX,2BAAe,CAAA;IACf,6BAAiB,CAAA;IACjB,6BAAiB,CAAA;AACnB,CAAC,EAPW,QAAQ,wBAAR,QAAQ,QAOnB;AAED,IAAY,gBASX;AATD,WAAY,gBAAgB;IAC1B,uCAAmB,CAAA;IACnB,2CAAuB,CAAA;IACvB,uCAAmB,CAAA;IACnB,+CAA2B,CAAA;IAC3B,iCAAa,CAAA;IACb,+BAAW,CAAA;IACX,iCAAa,CAAA;IACb,+BAAW,CAAA;AACb,CAAC,EATW,gBAAgB,gCAAhB,gBAAgB,QAS3B;AAED,IAAY,cAGX;AAHD,WAAY,cAAc;IACxB,+BAAa,CAAA;IACb,mCAAiB,CAAA;AACnB,CAAC,EAHW,cAAc,8BAAd,cAAc,QAGzB;AAED,MAAa,sBAAsB;CAiClC;AAjCD,wDAiCC;AAvBC;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;QAC3C,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,GAAG;QACZ,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;8DACc;AAWvB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,gCAAgC;QAC7C,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,GAAG;QACZ,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;gEACgB;AAWzB;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,8BAA8B;QAC3C,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,GAAG;QACZ,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;8DACc;AAGzB,MAAa,uBAAuB;CA+FnC;AA/FD,0DA+FC;AAxFC;IANC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,sBAAsB;QACnC,OAAO,EAAE,6BAA6B;KACvC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACG;AASd;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,WAAW;QACxB,IAAI,EAAE,QAAQ;QACd,OAAO,EAAE,QAAQ,CAAC,IAAI;KACvB,CAAC;IACD,IAAA,wBAAM,EAAC,QAAQ,CAAC;IAChB,IAAA,4BAAU,GAAE;;yDACM;AASnB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,mBAAmB;QAChC,IAAI,EAAE,gBAAgB;QACtB,OAAO,EAAE,gBAAgB,CAAC,OAAO;KAClC,CAAC;IACD,IAAA,wBAAM,EAAC,gBAAgB,CAAC;IACxB,IAAA,4BAAU,GAAE;;wDACa;AAS1B;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,uBAAuB;QACpC,IAAI,EAAE,cAAc;QACpB,OAAO,EAAE,cAAc,CAAC,IAAI;KAC7B,CAAC;IACD,IAAA,wBAAM,EAAC,cAAc,CAAC;IACtB,IAAA,4BAAU,GAAE;;+DACkB;AAQ/B;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EACT,wEAAwE;QAC1E,IAAI,EAAE,sBAAsB;KAC7B,CAAC;IACD,IAAA,4BAAU,GAAE;8BACM,sBAAsB;iEAAC;AAW1C;IATC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,GAAG;QACZ,OAAO,EAAE,EAAE;KACZ,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;kEACiB;AAS1B;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,2BAA2B;QACxC,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,GAAG;KACb,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;2DACY;AASnB;IAPC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,qBAAqB;QAClC,OAAO,EAAE,CAAC;QACV,OAAO,EAAE,GAAG;KACb,CAAC;IACD,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;yDACU;AAOjB;IALC,IAAA,qBAAW,EAAC;QACX,WAAW,EAAE,wCAAwC;QACrD,OAAO,EAAE,IAAI;KACd,CAAC;IACD,IAAA,2BAAS,GAAE;;+DACY;AAQxB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,uCAAuC;QACpD,OAAO,EAAE,+CAA+C;KACzD,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;6DACW;AAQtB;IANC,IAAA,6BAAmB,EAAC;QACnB,WAAW,EAAE,kDAAkD;QAC/D,OAAO,EAAE,0BAA0B;KACpC,CAAC;IACD,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACM"}