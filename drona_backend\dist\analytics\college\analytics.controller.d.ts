import { AnalyticsService } from './analytics.service';
export declare class AnalyticsController {
    private readonly analyticsService;
    constructor(analyticsService: AnalyticsService);
    getCollegeSummary(collegeId: string): Promise<{
        totalTeachers: number;
        totalPapersGenerated: any;
        totalDownloads: number;
    }>;
    getTeacherActivity(collegeId: string, page?: number, limit?: number, teacherId?: string, startDate?: string, endDate?: string): Promise<{
        teachers: any[];
        pagination: {
            total: any;
            page: number;
            limit: number;
            pages: number;
        };
        filters: {
            teacherId?: string;
            startDate?: Date;
            endDate?: Date;
        };
    }>;
    getQuestionPaperStats(collegeId: string, startDate?: string, endDate?: string): Promise<{
        data: {
            date: any;
            subjects: unknown[];
        }[];
        totalDays: number;
        dateRange: {
            startDate: Date | null;
            endDate: Date | null;
        };
    }>;
    getSubjectWiseAnalytics(collegeId: string): Promise<any[]>;
}
