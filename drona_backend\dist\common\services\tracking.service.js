"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TrackingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrackingService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const download_schema_1 = require("../../schema/download.schema");
const user_activity_schema_1 = require("../../schema/user-activity.schema");
const question_paper_schema_1 = require("../../schema/question-paper.schema");
let TrackingService = TrackingService_1 = class TrackingService {
    constructor(downloadModel, userActivityModel, questionPaperModel) {
        this.downloadModel = downloadModel;
        this.userActivityModel = userActivityModel;
        this.questionPaperModel = questionPaperModel;
        this.logger = new common_1.Logger(TrackingService_1.name);
    }
    async trackDownload(trackDownloadDto) {
        try {
            if (!mongoose_2.Types.ObjectId.isValid(trackDownloadDto.userId)) {
                throw new Error('Invalid userId');
            }
            if (!mongoose_2.Types.ObjectId.isValid(trackDownloadDto.paperId)) {
                throw new Error('Invalid paperId');
            }
            if (trackDownloadDto.collegeId &&
                !mongoose_2.Types.ObjectId.isValid(trackDownloadDto.collegeId)) {
                throw new Error('Invalid collegeId');
            }
            const downloadData = {
                userId: new mongoose_2.Types.ObjectId(trackDownloadDto.userId),
                paperId: new mongoose_2.Types.ObjectId(trackDownloadDto.paperId),
                downloadFormat: trackDownloadDto.downloadFormat,
                downloadDate: new Date(),
            };
            if (trackDownloadDto.collegeId) {
                downloadData.collegeId = new mongoose_2.Types.ObjectId(trackDownloadDto.collegeId);
            }
            const download = new this.downloadModel(downloadData);
            await download.save();
            this.logger.log(`Download tracked: User ${trackDownloadDto.userId} downloaded paper ${trackDownloadDto.paperId} in ${trackDownloadDto.downloadFormat} format`);
        }
        catch (error) {
            this.logger.error(`Failed to track download: ${error.message}`, error.stack);
        }
    }
    async trackPaperGeneration(trackPaperGenerationDto) {
        try {
            if (!mongoose_2.Types.ObjectId.isValid(trackPaperGenerationDto.userId)) {
                throw new Error('Invalid userId');
            }
            if (trackPaperGenerationDto.collegeId &&
                !mongoose_2.Types.ObjectId.isValid(trackPaperGenerationDto.collegeId)) {
                throw new Error('Invalid collegeId');
            }
            const activityData = {
                userId: new mongoose_2.Types.ObjectId(trackPaperGenerationDto.userId),
                activityType: 'paper_generation',
                activityDetails: {
                    paperId: trackPaperGenerationDto.paperId,
                    subjectId: trackPaperGenerationDto.subjectId,
                },
                timestamp: new Date(),
                ipAddress: trackPaperGenerationDto.ipAddress,
            };
            if (trackPaperGenerationDto.collegeId) {
                activityData.collegeId = new mongoose_2.Types.ObjectId(trackPaperGenerationDto.collegeId);
            }
            const activity = new this.userActivityModel(activityData);
            await activity.save();
            this.logger.log(`Paper generation tracked: User ${trackPaperGenerationDto.userId} generated paper ${trackPaperGenerationDto.paperId}`);
        }
        catch (error) {
            this.logger.error(`Failed to track paper generation: ${error.message}`, error.stack);
        }
    }
    async trackUserActivity(trackUserActivityDto) {
        try {
            if (!mongoose_2.Types.ObjectId.isValid(trackUserActivityDto.userId)) {
                throw new Error('Invalid userId');
            }
            if (trackUserActivityDto.collegeId &&
                !mongoose_2.Types.ObjectId.isValid(trackUserActivityDto.collegeId)) {
                throw new Error('Invalid collegeId');
            }
            const activityData = {
                userId: new mongoose_2.Types.ObjectId(trackUserActivityDto.userId),
                activityType: trackUserActivityDto.activityType,
                activityDetails: trackUserActivityDto.activityDetails,
                timestamp: new Date(),
                ipAddress: trackUserActivityDto.ipAddress,
            };
            if (trackUserActivityDto.collegeId) {
                activityData.collegeId = new mongoose_2.Types.ObjectId(trackUserActivityDto.collegeId);
            }
            const activity = new this.userActivityModel(activityData);
            await activity.save();
            this.logger.log(`User activity tracked: User ${trackUserActivityDto.userId} performed ${trackUserActivityDto.activityType}`);
        }
        catch (error) {
            this.logger.error(`Failed to track user activity: ${error.message}`, error.stack);
        }
    }
    async getTeacherDownloadStats(teacherId, filters = {}) {
        try {
            if (!mongoose_2.Types.ObjectId.isValid(teacherId)) {
                throw new Error('Invalid teacherId');
            }
            const matchQuery = {
                userId: new mongoose_2.Types.ObjectId(teacherId),
            };
            if (filters.startDate || filters.endDate) {
                matchQuery.downloadDate = {};
                if (filters.startDate) {
                    matchQuery.downloadDate.$gte = filters.startDate;
                }
                if (filters.endDate) {
                    matchQuery.downloadDate.$lte = filters.endDate;
                }
            }
            const pipeline = [
                { $match: matchQuery },
                {
                    $lookup: {
                        from: 'questionpapers',
                        localField: 'paperId',
                        foreignField: '_id',
                        as: 'questionPaper',
                    },
                },
                { $unwind: '$questionPaper' },
            ];
            if (filters.subjectId && mongoose_2.Types.ObjectId.isValid(filters.subjectId)) {
                pipeline.push({
                    $match: {
                        'questionPaper.subjectId': new mongoose_2.Types.ObjectId(filters.subjectId),
                    },
                });
            }
            pipeline.push({
                $lookup: {
                    from: 'subjects',
                    localField: 'questionPaper.subjectId',
                    foreignField: '_id',
                    as: 'subject',
                },
            }, { $unwind: '$subject' }, {
                $group: {
                    _id: {
                        subjectId: '$subject._id',
                        subjectName: '$subject.name',
                        format: '$downloadFormat',
                    },
                    count: { $sum: 1 },
                    lastDownload: { $max: '$downloadDate' },
                },
            }, {
                $group: {
                    _id: {
                        subjectId: '$_id.subjectId',
                        subjectName: '$_id.subjectName',
                    },
                    totalDownloads: { $sum: '$count' },
                    formatBreakdown: {
                        $push: {
                            format: '$_id.format',
                            count: '$count',
                        },
                    },
                    lastDownload: { $max: '$lastDownload' },
                },
            }, {
                $project: {
                    _id: 0,
                    subjectId: '$_id.subjectId',
                    subjectName: '$_id.subjectName',
                    totalDownloads: 1,
                    formatBreakdown: 1,
                    lastDownload: 1,
                },
            }, { $sort: { subjectName: 1 } });
            return await this.downloadModel.aggregate(pipeline).exec();
        }
        catch (error) {
            this.logger.error(`Failed to get teacher download stats: ${error.message}`, error.stack);
            throw error;
        }
    }
    async getTeacherPaperGenerationStats(teacherId, filters = {}) {
        try {
            if (!mongoose_2.Types.ObjectId.isValid(teacherId)) {
                throw new Error('Invalid teacherId');
            }
            const matchQuery = {
                generatedBy: new mongoose_2.Types.ObjectId(teacherId),
            };
            if (filters.startDate || filters.endDate) {
                matchQuery.createdAt = {};
                if (filters.startDate) {
                    matchQuery.createdAt.$gte = filters.startDate;
                }
                if (filters.endDate) {
                    matchQuery.createdAt.$lte = filters.endDate;
                }
            }
            if (filters.subjectId && mongoose_2.Types.ObjectId.isValid(filters.subjectId)) {
                matchQuery.subjectId = new mongoose_2.Types.ObjectId(filters.subjectId);
            }
            const pipeline = [
                { $match: matchQuery },
                {
                    $lookup: {
                        from: 'subjects',
                        localField: 'subjectId',
                        foreignField: '_id',
                        as: 'subject',
                    },
                },
                { $unwind: '$subject' },
                {
                    $group: {
                        _id: {
                            subjectId: '$subject._id',
                            subjectName: '$subject.name',
                        },
                        totalPapers: { $sum: 1 },
                        lastGenerated: { $max: '$createdAt' },
                    },
                },
                {
                    $project: {
                        _id: 0,
                        subjectId: '$_id.subjectId',
                        subjectName: '$_id.subjectName',
                        totalPapers: 1,
                        lastGenerated: 1,
                    },
                },
                { $sort: { subjectName: 1 } },
            ];
            return await this.questionPaperModel.aggregate(pipeline).exec();
        }
        catch (error) {
            this.logger.error(`Failed to get teacher paper generation stats: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.TrackingService = TrackingService;
exports.TrackingService = TrackingService = TrackingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(download_schema_1.Download.name)),
    __param(1, (0, mongoose_1.InjectModel)(user_activity_schema_1.UserActivity.name)),
    __param(2, (0, mongoose_1.InjectModel)(question_paper_schema_1.QuestionPaper.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model,
        mongoose_2.Model])
], TrackingService);
//# sourceMappingURL=tracking.service.js.map