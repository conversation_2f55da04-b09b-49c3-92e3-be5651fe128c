"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ImageCompressionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ImageCompressionService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const sharp = require("sharp");
const fs = require("fs");
const path = require("path");
const uuid_1 = require("uuid");
let ImageCompressionService = ImageCompressionService_1 = class ImageCompressionService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(ImageCompressionService_1.name);
        this.uploadsDir = path.join(process.cwd(), 'uploads', 'images');
        this.baseUrl =
            this.configService.get('BASE_URL') || 'http://localhost:3000';
        this.ensureUploadsDirectory();
    }
    async compressAndSaveImage(file, options = {}) {
        try {
            const defaultOptions = {
                maxSizeBytes: options.maxSizeBytes || 2 * 1024 * 1024,
                quality: 85,
                maxWidth: 1920,
                maxHeight: 1080,
                format: 'jpeg',
                preserveAspectRatio: true,
                ...options,
            };
            if (!this.isValidImageType(file.mimetype)) {
                throw new common_1.BadRequestException('Invalid image type. Only JPEG, PNG, and WebP are allowed.');
            }
            const fileExtension = this.getFileExtension(defaultOptions.format);
            const filename = `${(0, uuid_1.v4)()}${fileExtension}`;
            const outputPath = path.join(this.uploadsDir, filename);
            const originalSize = file.buffer.length;
            const imageInfo = await sharp(file.buffer).metadata();
            this.logger.log(`Processing image: ${file.originalname} (${this.formatBytes(originalSize)})`);
            let processedBuffer = file.buffer;
            let currentQuality = defaultOptions.quality;
            let attempts = 0;
            const maxAttempts = 10;
            while (processedBuffer.length > defaultOptions.maxSizeBytes &&
                attempts < maxAttempts) {
                attempts++;
                let sharpInstance = sharp(file.buffer);
                if ((imageInfo.width || 0) > defaultOptions.maxWidth ||
                    (imageInfo.height || 0) > defaultOptions.maxHeight) {
                    sharpInstance = sharpInstance.resize(defaultOptions.maxWidth, defaultOptions.maxHeight, {
                        fit: 'inside',
                        withoutEnlargement: true,
                    });
                }
                switch (defaultOptions.format) {
                    case 'jpeg':
                        sharpInstance = sharpInstance.jpeg({ quality: currentQuality });
                        break;
                    case 'png':
                        sharpInstance = sharpInstance.png({
                            quality: currentQuality,
                            compressionLevel: 9,
                        });
                        break;
                    case 'webp':
                        sharpInstance = sharpInstance.webp({ quality: currentQuality });
                        break;
                }
                processedBuffer = await sharpInstance.toBuffer();
                if (processedBuffer.length > defaultOptions.maxSizeBytes) {
                    currentQuality = Math.max(20, currentQuality - 10);
                    this.logger.log(`Attempt ${attempts}: Size ${this.formatBytes(processedBuffer.length)}, reducing quality to ${currentQuality}`);
                }
            }
            await fs.promises.writeFile(outputPath, processedBuffer);
            const finalMetadata = await sharp(processedBuffer).metadata();
            const compressedSize = processedBuffer.length;
            const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;
            const result = {
                filename,
                path: outputPath,
                url: `${this.baseUrl}/uploads/images/${filename}`,
                originalSize,
                compressedSize,
                compressionRatio: Math.round(compressionRatio * 100) / 100,
                width: finalMetadata.width || 0,
                height: finalMetadata.height || 0,
                format: defaultOptions.format,
            };
            this.logger.log(`Image compressed successfully: ${file.originalname} -> ${filename} ` +
                `(${this.formatBytes(originalSize)} -> ${this.formatBytes(compressedSize)}, ` +
                `${result.compressionRatio}% reduction)`);
            return result;
        }
        catch (error) {
            this.logger.error(`Error compressing image: ${error.message}`, error.stack);
            throw new common_1.BadRequestException(`Image compression failed: ${error.message}`);
        }
    }
    async compressBase64Image(base64Data, options = {}) {
        try {
            const base64Clean = base64Data.replace(/^data:image\/[a-z]+;base64,/, '');
            const buffer = Buffer.from(base64Clean, 'base64');
            const mockFile = {
                buffer,
                originalname: 'base64-image',
                mimetype: 'image/jpeg',
                size: buffer.length,
                fieldname: '',
                encoding: '',
                filename: '',
                path: '',
                destination: '',
                stream: null,
            };
            return this.compressAndSaveImage(mockFile, options);
        }
        catch (error) {
            this.logger.error(`Error compressing base64 image: ${error.message}`, error.stack);
            throw new common_1.BadRequestException(`Base64 image compression failed: ${error.message}`);
        }
    }
    async deleteImage(filename) {
        try {
            const filePath = path.join(this.uploadsDir, filename);
            if (await this.fileExists(filePath)) {
                await fs.promises.unlink(filePath);
                this.logger.log(`Deleted image: ${filename}`);
                return true;
            }
            return false;
        }
        catch (error) {
            this.logger.error(`Error deleting image ${filename}: ${error.message}`);
            return false;
        }
    }
    async getImageInfo(file) {
        try {
            return await sharp(file.buffer).metadata();
        }
        catch (error) {
            throw new common_1.BadRequestException('Invalid image file');
        }
    }
    isValidImageType(mimetype) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        return validTypes.includes(mimetype.toLowerCase());
    }
    getFileExtension(format) {
        switch (format) {
            case 'jpeg':
                return '.jpg';
            case 'png':
                return '.png';
            case 'webp':
                return '.webp';
            default:
                return '.jpg';
        }
    }
    formatBytes(bytes) {
        if (bytes === 0)
            return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    async fileExists(filePath) {
        try {
            await fs.promises.access(filePath);
            return true;
        }
        catch {
            return false;
        }
    }
    ensureUploadsDirectory() {
        try {
            if (!fs.existsSync(this.uploadsDir)) {
                fs.mkdirSync(this.uploadsDir, { recursive: true });
                this.logger.log(`Created uploads directory: ${this.uploadsDir}`);
            }
        }
        catch (error) {
            this.logger.error(`Error creating uploads directory: ${error.message}`);
        }
    }
};
exports.ImageCompressionService = ImageCompressionService;
exports.ImageCompressionService = ImageCompressionService = ImageCompressionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], ImageCompressionService);
//# sourceMappingURL=image-compression.service.js.map