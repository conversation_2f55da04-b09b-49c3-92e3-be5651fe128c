"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_utils_pdfGenerator_ts",{

/***/ "(app-pages-browser)/./src/utils/pdfGenerator.ts":
/*!***********************************!*\
  !*** ./src/utils/pdfGenerator.ts ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PDFGenerator: () => (/* binding */ PDFGenerator),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var jspdf__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jspdf */ \"(app-pages-browser)/./node_modules/jspdf/dist/jspdf.es.min.js\");\n/* harmony import */ var katex_dist_katex_min_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! katex/dist/katex.min.css */ \"(app-pages-browser)/./node_modules/katex/dist/katex.min.css\");\n/* harmony import */ var _utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/imageUtils */ \"(app-pages-browser)/./src/utils/imageUtils.ts\");\n\n\n\nclass PDFGenerator {\n    addWatermark() {\n        try {\n            // Save current graphics state\n            this.doc.saveGraphicsState();\n            // Set low opacity for watermark\n            this.doc.setGState(this.doc.GState({\n                opacity: 0.08\n            }));\n            // Calculate center position\n            const centerX = this.pageWidth / 2;\n            const centerY = this.pageHeight / 2;\n            // Add watermark text with rotation\n            this.doc.setFontSize(60);\n            this.doc.setFont('times', 'bold');\n            this.doc.setTextColor(150, 150, 150);\n            // Rotate and add watermark text\n            this.doc.text('MEDICOS', centerX, centerY, {\n                angle: 45,\n                align: 'center'\n            });\n            // Restore graphics state\n            this.doc.restoreGraphicsState();\n            // Reset text color to black\n            this.doc.setTextColor(0, 0, 0);\n        } catch (error) {\n            console.warn('Could not add watermark:', error);\n        }\n    }\n    addCollegeHeader(collegeInfo) {\n        if (!collegeInfo) return;\n        // Add college logo if available (Medicos logo in top right)\n        try {\n            this.doc.setFontSize(8);\n            this.doc.setFont('times', 'normal');\n            this.doc.setTextColor(100, 100, 100);\n            this.doc.text('MEDICOS', this.pageWidth - this.margin - 15, this.currentY + 3, {\n                align: 'center'\n            });\n            this.doc.setTextColor(0, 0, 0); // Reset to black\n        } catch (error) {\n            console.warn('Could not add logo:', error);\n        }\n        this.doc.setFontSize(14);\n        this.doc.setFont('times', 'bold');\n        // College name\n        if (collegeInfo.name) {\n            this.doc.text(collegeInfo.name, this.pageWidth / 2, this.currentY, {\n                align: 'center'\n            });\n            this.currentY += 6;\n        }\n        // College address\n        if (collegeInfo.address) {\n            this.doc.setFontSize(10);\n            this.doc.setFont('times', 'normal');\n            this.doc.text(collegeInfo.address, this.pageWidth / 2, this.currentY, {\n                align: 'center'\n            });\n            this.currentY += 6;\n        }\n        // Minimal space after college info\n        this.currentY += 3;\n    }\n    async addImageToPDF(imageSrc, x, y) {\n        let maxWidth = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 40, maxHeight = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 30;\n        try {\n            // Ensure the image has proper data URL format\n            const dataUrl = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__.ensureDataUrl)(imageSrc);\n            // Add image to PDF with smaller size for better layout\n            this.doc.addImage(dataUrl, 'JPEG', x, y, maxWidth, maxHeight);\n            // Return the height used\n            return maxHeight + 2; // Add small margin\n        } catch (error) {\n            console.warn('Failed to add image to PDF:', error);\n            // Add placeholder text for failed images\n            this.doc.setFontSize(8);\n            this.doc.setTextColor(150, 150, 150);\n            this.doc.text('[Image]', x, y + 5);\n            this.doc.setTextColor(0, 0, 0);\n            return 8; // Return small height for placeholder\n        }\n    }\n    processTextWithImages(text) {\n        try {\n            // Use the same image extraction logic as the admin question bank\n            const { cleanText, images } = (0,_utils_imageUtils__WEBPACK_IMPORTED_MODULE_2__.extractImagesFromText)(text);\n            // Process the clean text for LaTeX rendering\n            const processedText = this.renderLatex(cleanText);\n            return {\n                cleanText: processedText,\n                images: images\n            };\n        } catch (error) {\n            console.warn('Text processing error:', error);\n            return {\n                cleanText: text,\n                images: []\n            };\n        }\n    }\n    renderLatex(text) {\n        try {\n            // Handle common LaTeX patterns - enhanced version\n            let processedText = text;\n            // Replace common LaTeX symbols with Unicode equivalents\n            const latexReplacements = {\n                // Remove LaTeX formatting\n                '\\\\mathrm{': '',\n                '\\\\text{': '',\n                '\\\\rm{': '',\n                // Greek letters\n                '\\\\phi': 'φ',\n                '\\\\Phi': 'Φ',\n                '\\\\alpha': 'α',\n                '\\\\beta': 'β',\n                '\\\\gamma': 'γ',\n                '\\\\Gamma': 'Γ',\n                '\\\\delta': 'δ',\n                '\\\\Delta': 'Δ',\n                '\\\\epsilon': 'ε',\n                '\\\\varepsilon': 'ε',\n                '\\\\zeta': 'ζ',\n                '\\\\eta': 'η',\n                '\\\\theta': 'θ',\n                '\\\\Theta': 'Θ',\n                '\\\\iota': 'ι',\n                '\\\\kappa': 'κ',\n                '\\\\lambda': 'λ',\n                '\\\\Lambda': 'Λ',\n                '\\\\mu': 'μ',\n                '\\\\nu': 'ν',\n                '\\\\xi': 'ξ',\n                '\\\\Xi': 'Ξ',\n                '\\\\pi': 'π',\n                '\\\\Pi': 'Π',\n                '\\\\rho': 'ρ',\n                '\\\\sigma': 'σ',\n                '\\\\Sigma': 'Σ',\n                '\\\\tau': 'τ',\n                '\\\\upsilon': 'υ',\n                '\\\\Upsilon': 'Υ',\n                '\\\\chi': 'χ',\n                '\\\\psi': 'ψ',\n                '\\\\Psi': 'Ψ',\n                '\\\\omega': 'ω',\n                '\\\\Omega': 'Ω',\n                // Arrows\n                '\\\\rightarrow': '→',\n                '\\\\leftarrow': '←',\n                '\\\\leftrightarrow': '↔',\n                '\\\\Rightarrow': '⇒',\n                '\\\\Leftarrow': '⇐',\n                '\\\\Leftrightarrow': '⇔',\n                '\\\\uparrow': '↑',\n                '\\\\downarrow': '↓',\n                // Mathematical operators\n                '\\\\pm': '±',\n                '\\\\mp': '∓',\n                '\\\\times': '×',\n                '\\\\div': '÷',\n                '\\\\cdot': '·',\n                '\\\\ast': '*',\n                '\\\\star': '⋆',\n                '\\\\circ': '∘',\n                '\\\\bullet': '•',\n                // Relations\n                '\\\\leq': '≤',\n                '\\\\geq': '≥',\n                '\\\\neq': '≠',\n                '\\\\equiv': '≡',\n                '\\\\approx': '≈',\n                '\\\\cong': '≅',\n                '\\\\sim': '∼',\n                '\\\\propto': '∝',\n                '\\\\parallel': '∥',\n                '\\\\perp': '⊥',\n                // Set theory\n                '\\\\in': '∈',\n                '\\\\notin': '∉',\n                '\\\\subset': '⊂',\n                '\\\\supset': '⊃',\n                '\\\\subseteq': '⊆',\n                '\\\\supseteq': '⊇',\n                '\\\\cup': '∪',\n                '\\\\cap': '∩',\n                '\\\\emptyset': '∅',\n                // Calculus\n                '\\\\infty': '∞',\n                '\\\\sum': 'Σ',\n                '\\\\prod': 'Π',\n                '\\\\int': '∫',\n                '\\\\oint': '∮',\n                '\\\\partial': '∂',\n                '\\\\nabla': '∇',\n                // Logic\n                '\\\\land': '∧',\n                '\\\\lor': '∨',\n                '\\\\neg': '¬',\n                '\\\\forall': '∀',\n                '\\\\exists': '∃',\n                '\\\\wedge': '∧',\n                '\\\\vee': '∨',\n                '\\\\wed': '∧',\n                // Logical operators that appear as special characters\n                '!Ô': '→',\n                '!Ò': '←',\n                'Ô': '→',\n                'Ò': '←',\n                // Remove dollar signs and braces\n                '$': '',\n                '{': '',\n                '}': ''\n            };\n            // Apply replacements\n            for (const [latex, replacement] of Object.entries(latexReplacements)){\n                processedText = processedText.replace(new RegExp(latex.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'), 'g'), replacement);\n            }\n            // Handle subscripts (convert to Unicode subscripts where possible)\n            const subscriptMap = {\n                '0': '₀',\n                '1': '₁',\n                '2': '₂',\n                '3': '₃',\n                '4': '₄',\n                '5': '₅',\n                '6': '₆',\n                '7': '₇',\n                '8': '₈',\n                '9': '₉',\n                '+': '₊',\n                '-': '₋',\n                '=': '₌',\n                '(': '₍',\n                ')': '₎',\n                'a': 'ₐ',\n                'e': 'ₑ',\n                'h': 'ₕ',\n                'i': 'ᵢ',\n                'j': 'ⱼ',\n                'k': 'ₖ',\n                'l': 'ₗ',\n                'm': 'ₘ',\n                'n': 'ₙ',\n                'o': 'ₒ',\n                'p': 'ₚ',\n                'r': 'ᵣ',\n                's': 'ₛ',\n                't': 'ₜ',\n                'u': 'ᵤ',\n                'v': 'ᵥ',\n                'x': 'ₓ'\n            };\n            // Handle superscripts (convert to Unicode superscripts where possible)\n            const superscriptMap = {\n                '0': '⁰',\n                '1': '¹',\n                '2': '²',\n                '3': '³',\n                '4': '⁴',\n                '5': '⁵',\n                '6': '⁶',\n                '7': '⁷',\n                '8': '⁸',\n                '9': '⁹',\n                '+': '⁺',\n                '-': '⁻',\n                '=': '⁼',\n                '(': '⁽',\n                ')': '⁾',\n                'a': 'ᵃ',\n                'b': 'ᵇ',\n                'c': 'ᶜ',\n                'd': 'ᵈ',\n                'e': 'ᵉ',\n                'f': 'ᶠ',\n                'g': 'ᵍ',\n                'h': 'ʰ',\n                'i': 'ⁱ',\n                'j': 'ʲ',\n                'k': 'ᵏ',\n                'l': 'ˡ',\n                'm': 'ᵐ',\n                'n': 'ⁿ',\n                'o': 'ᵒ',\n                'p': 'ᵖ',\n                'r': 'ʳ',\n                's': 'ˢ',\n                't': 'ᵗ',\n                'u': 'ᵘ',\n                'v': 'ᵛ',\n                'w': 'ʷ',\n                'x': 'ˣ',\n                'y': 'ʸ',\n                'z': 'ᶻ'\n            };\n            // Process subscripts\n            processedText = processedText.replace(/_{([^}]+)}/g, (match, content)=>{\n                return content.split('').map((char)=>subscriptMap[char] || char).join('');\n            });\n            // Process superscripts\n            processedText = processedText.replace(/\\^{([^}]+)}/g, (match, content)=>{\n                return content.split('').map((char)=>superscriptMap[char] || char).join('');\n            });\n            // Handle fractions (basic conversion)\n            processedText = processedText.replace(/\\\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)');\n            // Handle square roots\n            processedText = processedText.replace(/\\\\sqrt{([^}]+)}/g, '√($1)');\n            // Clean up any remaining braces\n            processedText = processedText.replace(/[{}]/g, '');\n            return processedText;\n        } catch (error) {\n            console.warn('LaTeX rendering error:', error);\n            return text; // Return original text if rendering fails\n        }\n    }\n    addTitle(title) {\n        this.doc.setFontSize(16);\n        this.doc.setFont('times', 'bold');\n        // Add underline effect\n        const titleWidth = this.doc.getTextWidth(title);\n        const titleX = (this.pageWidth - titleWidth) / 2;\n        this.doc.text(title, this.pageWidth / 2, this.currentY, {\n            align: 'center'\n        });\n        // Draw underline\n        this.doc.line(titleX, this.currentY + 1, titleX + titleWidth, this.currentY + 1);\n        this.currentY += 8;\n    }\n    addExamDetails(questionPaper) {\n        var _questionPaper_subjectId;\n        this.doc.setFontSize(11);\n        this.doc.setFont('times', 'normal');\n        // Subject (for single subject papers)\n        if (((_questionPaper_subjectId = questionPaper.subjectId) === null || _questionPaper_subjectId === void 0 ? void 0 : _questionPaper_subjectId.name) && !questionPaper.isMultiSubject) {\n            this.doc.text(\"Subject: \".concat(questionPaper.subjectId.name), this.pageWidth / 2, this.currentY, {\n                align: 'center'\n            });\n            this.currentY += 6;\n        }\n        // Duration and marks - centered layout\n        const detailsText = \"Duration: \".concat(questionPaper.duration, \" minutes | Total Marks: \").concat(questionPaper.totalMarks);\n        this.doc.text(detailsText, this.pageWidth / 2, this.currentY, {\n            align: 'center'\n        });\n        this.currentY += 8;\n        // Instructions\n        if (questionPaper.instructions) {\n            this.doc.setFont('times', 'bold');\n            this.doc.text('Instructions:', this.margin, this.currentY);\n            this.currentY += 5;\n            this.doc.setFont('times', 'normal');\n            this.doc.setFontSize(9);\n            const instructionLines = this.doc.splitTextToSize(questionPaper.instructions, this.contentWidth);\n            this.doc.text(instructionLines, this.margin, this.currentY);\n            this.currentY += instructionLines.length * 3 + 6;\n        }\n        // Add horizontal line\n        this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);\n        this.currentY += 6;\n    }\n    checkPageBreak(requiredHeight) {\n        // More aggressive page break - only break when absolutely necessary\n        return this.currentY + requiredHeight > this.pageHeight - 10;\n    }\n    addNewPage(collegeInfo, subjectName) {\n        this.doc.addPage();\n        this.addWatermark();\n        // Always add college header on new pages\n        this.currentY = this.margin;\n        if (collegeInfo) {\n            this.addCollegeHeader(collegeInfo);\n        }\n        // Add subject header if this is a multi-subject paper\n        if (subjectName) {\n            this.addSubjectHeader(subjectName);\n        }\n    }\n    addSubjectHeader(subjectName) {\n        this.doc.setFontSize(14);\n        this.doc.setFont('times', 'bold');\n        this.doc.text(subjectName, this.pageWidth / 2, this.currentY, {\n            align: 'center'\n        });\n        this.currentY += 6;\n        // Add horizontal line under subject name\n        this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);\n        this.currentY += 8;\n    }\n    async addQuestion(question, questionNumber, withAnswers, collegeInfo, subjectName) {\n        // Estimate required height for this question\n        const estimatedHeight = this.estimateQuestionHeight(question, withAnswers);\n        // More intelligent page break - try to fit the question first\n        if (this.checkPageBreak(estimatedHeight)) {\n            // Check if we can fit at least the question text and first option\n            const minHeight = 15; // Minimum space needed for question start\n            if (this.currentY + minHeight > this.pageHeight - 8) {\n                this.addNewPage(collegeInfo, subjectName);\n            }\n        }\n        // Process question content with images\n        const questionContent = this.processTextWithImages(question.content);\n        // Question number and text\n        this.doc.setFontSize(11);\n        this.doc.setFont('times', 'bold');\n        this.doc.text(\"\".concat(questionNumber, \".\"), this.margin, this.currentY);\n        // Question content\n        this.doc.setFont('times', 'normal');\n        const questionText = questionContent.cleanText;\n        const questionLines = this.doc.splitTextToSize(questionText, this.contentWidth - 15);\n        this.doc.text(questionLines, this.margin + 12, this.currentY);\n        this.currentY += Math.max(questionLines.length * 4.5, 6) + 3;\n        // Add question images if any\n        if (questionContent.images.length > 0) {\n            for (const image of questionContent.images){\n                const imageHeight = await this.addImageToPDF(image.src, this.margin + 12, this.currentY, 50, 35);\n                this.currentY += imageHeight;\n            }\n        }\n        // Options with compact formatting\n        if (question.options && question.options.length > 0) {\n            this.doc.setFontSize(10);\n            this.doc.setFont('times', 'normal');\n            for(let optIndex = 0; optIndex < question.options.length; optIndex++){\n                const option = question.options[optIndex];\n                const optionLabel = String.fromCharCode(97 + optIndex); // a, b, c, d\n                // Check if we have space for this option, if not, continue on new page\n                if (this.currentY > this.pageHeight - 20) {\n                    this.addNewPage(collegeInfo, subjectName);\n                }\n                // Process option content with images\n                const optionContent = this.processTextWithImages(option);\n                // Option label and text on same line\n                this.doc.text(\"\".concat(optionLabel, \")\"), this.margin + 15, this.currentY);\n                // Option text\n                const optionLines = this.doc.splitTextToSize(optionContent.cleanText, this.contentWidth - 30);\n                this.doc.text(optionLines, this.margin + 25, this.currentY);\n                this.currentY += Math.max(optionLines.length * 4.2, 5) + 2;\n                // Add option images if any\n                if (optionContent.images.length > 0) {\n                    for (const image of optionContent.images){\n                        const imageHeight = await this.addImageToPDF(image.src, this.margin + 25, this.currentY, 40, 30);\n                        this.currentY += imageHeight;\n                    }\n                }\n            }\n        }\n        // Answer (if included) - Bold black text\n        if (withAnswers && question.answer) {\n            this.currentY += 2; // Minimal space before answer\n            this.doc.setFont('times', 'bold');\n            this.doc.setTextColor(0, 0, 0);\n            const answerContent = this.processTextWithImages(question.answer);\n            // Answer label and text\n            this.doc.text('Answer:', this.margin + 15, this.currentY);\n            // Answer text\n            const answerLines = this.doc.splitTextToSize(answerContent.cleanText, this.contentWidth - 40);\n            this.doc.text(answerLines, this.margin + 40, this.currentY);\n            this.currentY += Math.max(answerLines.length * 4, 4) + 1;\n            // Add answer images if any\n            if (answerContent.images.length > 0) {\n                for (const image of answerContent.images){\n                    const imageHeight = await this.addImageToPDF(image.src, this.margin + 40, this.currentY, 40, 30);\n                    this.currentY += imageHeight;\n                }\n            }\n            // Reset to normal font\n            this.doc.setFont('times', 'normal');\n            this.doc.setTextColor(0, 0, 0);\n        }\n        // Minimal spacing after question\n        this.currentY += 4;\n    }\n    async addQuestionWithSolutions(question, questionNumber, withAnswers, withSolutions, withHints, collegeInfo, subjectName) {\n        // Estimate required height for this question with solutions\n        const estimatedHeight = this.estimateQuestionHeightWithSolutions(question, withAnswers, withSolutions, withHints);\n        // Check for page break\n        if (this.checkPageBreak(estimatedHeight)) {\n            const minHeight = 20; // Minimum space needed for question start with solutions\n            if (this.currentY + minHeight > this.pageHeight - 8) {\n                this.addNewPage(collegeInfo, subjectName);\n            }\n        }\n        // First add the basic question\n        await this.addQuestion(question, questionNumber, withAnswers, collegeInfo, subjectName);\n        // Add solutions if requested and available\n        if (withSolutions && question.solution) {\n            this.currentY += 2;\n            this.doc.setFont('times', 'bold');\n            this.doc.setTextColor(0, 0, 0);\n            this.doc.text('Solution:', this.margin + 15, this.currentY);\n            this.currentY += 5;\n            // Add methodology if available\n            if (question.solution.methodology) {\n                this.doc.setFont('times', 'italic');\n                this.doc.text('Methodology:', this.margin + 20, this.currentY);\n                this.doc.setFont('times', 'normal');\n                const methodologyLines = this.doc.splitTextToSize(question.solution.methodology, this.contentWidth - 50);\n                this.doc.text(methodologyLines, this.margin + 20, this.currentY + 4);\n                this.currentY += Math.max(methodologyLines.length * 4, 4) + 3;\n            }\n            // Add solution steps\n            if (question.solution.steps && question.solution.steps.length > 0) {\n                this.doc.setFont('times', 'italic');\n                this.doc.text('Steps:', this.margin + 20, this.currentY);\n                this.currentY += 4;\n                question.solution.steps.forEach((step, index)=>{\n                    this.doc.setFont('times', 'normal');\n                    const stepText = \"\".concat(index + 1, \". \").concat(step);\n                    const stepLines = this.doc.splitTextToSize(stepText, this.contentWidth - 55);\n                    this.doc.text(stepLines, this.margin + 25, this.currentY);\n                    this.currentY += Math.max(stepLines.length * 4, 4) + 2;\n                });\n            }\n            // Add key concepts\n            if (question.solution.key_concepts && question.solution.key_concepts.length > 0) {\n                this.doc.setFont('times', 'italic');\n                this.doc.text('Key Concepts:', this.margin + 20, this.currentY);\n                this.doc.setFont('times', 'normal');\n                const conceptsText = question.solution.key_concepts.join(', ');\n                const conceptsLines = this.doc.splitTextToSize(conceptsText, this.contentWidth - 50);\n                this.doc.text(conceptsLines, this.margin + 20, this.currentY + 4);\n                this.currentY += Math.max(conceptsLines.length * 4, 4) + 3;\n            }\n            // Add final explanation\n            if (question.solution.final_explanation) {\n                this.doc.setFont('times', 'italic');\n                this.doc.text('Explanation:', this.margin + 20, this.currentY);\n                this.doc.setFont('times', 'normal');\n                const explanationLines = this.doc.splitTextToSize(question.solution.final_explanation, this.contentWidth - 50);\n                this.doc.text(explanationLines, this.margin + 20, this.currentY + 4);\n                this.currentY += Math.max(explanationLines.length * 4, 4) + 3;\n            }\n        }\n        // Add hints if requested and available\n        if (withHints && question.hints && question.hints.length > 0) {\n            this.currentY += 2;\n            this.doc.setFont('times', 'bold');\n            this.doc.setTextColor(0, 0, 0);\n            this.doc.text('Hints:', this.margin + 15, this.currentY);\n            this.currentY += 5;\n            question.hints.forEach((hint, index)=>{\n                this.doc.setFont('times', 'normal');\n                const hintText = \"• \".concat(hint);\n                const hintLines = this.doc.splitTextToSize(hintText, this.contentWidth - 50);\n                this.doc.text(hintLines, this.margin + 20, this.currentY);\n                this.currentY += Math.max(hintLines.length * 4, 4) + 2;\n            });\n        }\n        // Reset font and add spacing\n        this.doc.setFont('times', 'normal');\n        this.doc.setTextColor(0, 0, 0);\n        this.currentY += 6;\n    }\n    estimateQuestionHeight(question, withAnswers) {\n        let height = 10; // Base question height (further reduced)\n        // Add height for options (more conservative)\n        if (question.options && question.options.length > 0) {\n            height += question.options.length * 4; // Reduced from 5 to 4\n        }\n        // Add height for answer if included\n        if (withAnswers && question.answer) {\n            height += 6; // Reduced from 10 to 6\n        }\n        // Add minimal height for potential images (only if content suggests images)\n        const hasImages = question.content.includes('base64') || question.options && question.options.some((opt)=>opt.includes('base64')) || question.answer && question.answer.includes('base64');\n        if (hasImages) {\n            height += 20; // Only add image height if images are likely present\n        } else {\n            height += 5; // Minimal buffer for text-only questions\n        }\n        return height;\n    }\n    estimateQuestionHeightWithSolutions(question, withAnswers, withSolutions, withHints) {\n        let height = this.estimateQuestionHeight(question, withAnswers);\n        // Add height for solutions if included\n        if (withSolutions && question.solution) {\n            height += 15; // Base solution height\n            if (question.solution.methodology) {\n                height += 8;\n            }\n            if (question.solution.steps && question.solution.steps.length > 0) {\n                height += question.solution.steps.length * 6;\n            }\n            if (question.solution.key_concepts && question.solution.key_concepts.length > 0) {\n                height += 8;\n            }\n            if (question.solution.final_explanation) {\n                height += 12;\n            }\n        }\n        // Add height for hints if included\n        if (withHints && question.hints && question.hints.length > 0) {\n            height += 10 + question.hints.length * 6;\n        }\n        return height;\n    }\n    async generatePDF(questionPaper, collegeInfo) {\n        try {\n            console.log(\"PDF Generator - Starting generation with:\", {\n                questionPaper: questionPaper,\n                collegeInfo: collegeInfo\n            });\n            // Check if this is a multi-subject paper\n            const isMultiSubject = questionPaper.isMultiSubject && questionPaper.sections && questionPaper.sections.length > 1;\n            if (isMultiSubject) {\n                // Handle multi-subject papers - separate page for each subject\n                await this.generateMultiSubjectPDF(questionPaper, collegeInfo);\n            } else {\n                // Handle single subject papers\n                await this.generateSingleSubjectPDF(questionPaper, collegeInfo);\n            }\n            // Generate and return blob\n            const pdfBlob = this.doc.output('blob');\n            return pdfBlob;\n        } catch (error) {\n            console.error('PDF generation error:', error);\n            throw new Error('Failed to generate PDF: ' + error.message);\n        }\n    }\n    async generateSingleSubjectPDF(questionPaper, collegeInfo) {\n        // Add watermark to first page\n        this.addWatermark();\n        // Always add college header on first page\n        if (collegeInfo) {\n            this.addCollegeHeader(collegeInfo);\n        }\n        // Add title\n        this.addTitle(questionPaper.title);\n        // Add exam details\n        this.addExamDetails(questionPaper);\n        // Add questions\n        console.log(\"Adding questions to PDF:\", questionPaper.questions.length);\n        for(let index = 0; index < questionPaper.questions.length; index++){\n            const question = questionPaper.questions[index];\n            console.log(\"Processing question \".concat(index + 1, \":\"), question);\n            try {\n                if (questionPaper.withSolutions || questionPaper.withHints) {\n                    await this.addQuestionWithSolutions(question, index + 1, questionPaper.withAnswers, questionPaper.withSolutions || false, questionPaper.withHints || false, collegeInfo);\n                } else {\n                    await this.addQuestion(question, index + 1, questionPaper.withAnswers, collegeInfo);\n                }\n            } catch (error) {\n                console.error(\"Error processing question \".concat(index + 1, \":\"), error);\n            // Continue with next question\n            }\n        }\n    }\n    async generateMultiSubjectPDF(questionPaper, collegeInfo) {\n        if (!questionPaper.sections) return;\n        let overallQuestionNumber = 1;\n        for(let sectionIndex = 0; sectionIndex < questionPaper.sections.length; sectionIndex++){\n            const section = questionPaper.sections[sectionIndex];\n            const subjectName = section.subjectName || section.name || \"Subject \".concat(sectionIndex + 1);\n            // Add new page for each subject (except the first one)\n            if (sectionIndex === 0) {\n                // First page setup\n                this.addWatermark();\n                if (collegeInfo) {\n                    this.addCollegeHeader(collegeInfo);\n                }\n                this.addTitle(questionPaper.title);\n                this.addExamDetails(questionPaper);\n                this.addSubjectHeader(subjectName);\n            } else {\n                // New page for subsequent subjects\n                this.addNewPage(collegeInfo, subjectName);\n            }\n            // Add questions for this section\n            if (section.questions && section.questions.length > 0) {\n                console.log(\"Adding questions for \".concat(subjectName, \":\"), section.questions.length);\n                for (const questionItem of section.questions){\n                    const question = questionItem.question;\n                    console.log(\"Processing question \".concat(overallQuestionNumber, \":\"), question);\n                    try {\n                        if (questionPaper.withSolutions || questionPaper.withHints) {\n                            await this.addQuestionWithSolutions(question, overallQuestionNumber, questionPaper.withAnswers, questionPaper.withSolutions || false, questionPaper.withHints || false, collegeInfo, subjectName);\n                        } else {\n                            await this.addQuestion(question, overallQuestionNumber, questionPaper.withAnswers, collegeInfo, subjectName);\n                        }\n                        overallQuestionNumber++;\n                    } catch (error) {\n                        console.error(\"Error processing question \".concat(overallQuestionNumber, \":\"), error);\n                        overallQuestionNumber++;\n                    // Continue with next question\n                    }\n                }\n            }\n        }\n    }\n    constructor(){\n        this.doc = new jspdf__WEBPACK_IMPORTED_MODULE_0__[\"default\"]('p', 'mm', 'a4');\n        this.pageWidth = this.doc.internal.pageSize.getWidth();\n        this.pageHeight = this.doc.internal.pageSize.getHeight();\n        this.margin = 20;\n        this.currentY = this.margin;\n        // Single column layout - full width\n        this.contentWidth = this.pageWidth - this.margin * 2;\n        // Set default font to Times\n        try {\n            this.doc.setFont('times', 'normal');\n        } catch (error) {\n            console.warn('Times font not available, using default');\n            this.doc.setFont('helvetica', 'normal');\n        }\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PDFGenerator);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/pdfGenerator.ts\n"));

/***/ })

});