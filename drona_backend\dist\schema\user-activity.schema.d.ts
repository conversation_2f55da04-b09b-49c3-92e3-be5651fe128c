import { Document, Schema as MongooseSchema } from 'mongoose';
import { User } from './user.schema';
import { College } from './college.schema';
export type UserActivityDocument = UserActivity & Document;
export declare class UserActivity {
    userId: User;
    collegeId: College;
    activityType: string;
    activityDetails: any;
    timestamp: Date;
    ipAddress: string;
}
export declare const UserActivitySchema: MongooseSchema<UserActivity, import("mongoose").Model<UserActivity, any, any, any, Document<unknown, any, UserActivity> & UserActivity & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, UserActivity, Document<unknown, {}, import("mongoose").FlatRecord<UserActivity>> & import("mongoose").FlatRecord<UserActivity> & {
    _id: import("mongoose").Types.ObjectId;
} & {
    __v: number;
}>;
