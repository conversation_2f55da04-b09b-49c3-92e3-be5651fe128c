"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionPapersModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const question_papers_service_1 = require("./question-papers.service");
const question_papers_controller_1 = require("./question-papers.controller");
const question_paper_schema_1 = require("../schema/question-paper.schema");
const question_schema_1 = require("../schema/question.schema");
const subject_schema_1 = require("../schema/subject.schema");
const college_schema_1 = require("../schema/college.schema");
const question_usage_module_1 = require("../question-usage/question-usage.module");
let QuestionPapersModule = class QuestionPapersModule {
};
exports.QuestionPapersModule = QuestionPapersModule;
exports.QuestionPapersModule = QuestionPapersModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: question_paper_schema_1.QuestionPaper.name, schema: question_paper_schema_1.QuestionPaperSchema },
                { name: question_schema_1.Question.name, schema: question_schema_1.QuestionSchema },
                { name: subject_schema_1.Subject.name, schema: subject_schema_1.SubjectSchema },
                { name: college_schema_1.College.name, schema: college_schema_1.CollegeSchema },
            ]),
            question_usage_module_1.QuestionUsageModule,
        ],
        controllers: [question_papers_controller_1.QuestionPapersController],
        providers: [question_papers_service_1.QuestionPapersService],
        exports: [question_papers_service_1.QuestionPapersService],
    })
], QuestionPapersModule);
//# sourceMappingURL=question-papers.module.js.map