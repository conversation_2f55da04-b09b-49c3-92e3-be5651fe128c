import { QuestionsService } from './questions.service';
import { CreateQuestionDto } from './dto/create-question.dto';
import { UpdateQuestionDto } from './dto/update-question.dto';
import { FilterQuestionsDto } from './dto/filter-questions.dto';
import { Question } from '../schema/question.schema';
import { BulkReviewDto } from './dto/bulk-review.dto';
import { BulkUploadPdfDto } from './dto/bulk-upload-pdf.dto';
import { ReviewQuestionDto } from './dto/review-question.dto';
export declare class QuestionsController {
    private readonly questionsService;
    constructor(questionsService: QuestionsService);
    create(createQuestionDto: CreateQuestionDto, images: Express.Multer.File[], req: any): Promise<Question>;
    findAll(filters: FilterQuestionsDto, page?: string, limit?: string): Promise<any>;
    findDuplicates(subjectId?: string, limit?: string): Promise<any>;
    removeQuestion(id: string): Promise<void>;
    findPendingReviews(subjectId?: string, page?: string, limit?: string): Promise<any>;
    findOne(id: string): Promise<Question>;
    update(id: string, updateQuestionDto: UpdateQuestionDto, images: Express.Multer.File[]): Promise<Question>;
    remove(id: string): Promise<Question>;
    reviewQuestion(id: string, reviewDto: ReviewQuestionDto, req: any): Promise<import("mongoose").Document<unknown, {}, Question> & Question & {
        _id: import("mongoose").Types.ObjectId;
    } & {
        __v: number;
    }>;
    bulkReviewQuestions(bulkReviewDto: BulkReviewDto, req: any): Promise<{
        message: string;
        reviewedCount: number;
        status: "approved" | "rejected";
        details: {
            matchedCount: number;
            modifiedCount: number;
            upsertedCount: number;
            deletedCount: number;
        };
    }>;
    bulkUploadPdf(file: Express.Multer.File, uploadDto: BulkUploadPdfDto, req: any): Promise<{
        message: string;
        questionsAdded: number;
        questionsFailed: number;
        questions: any[];
        errors: string[];
    }>;
    getDebugCounts(): Promise<any>;
}
