"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("../schema/user.schema");
let UsersService = class UsersService {
    constructor(userModel) {
        this.userModel = userModel;
    }
    async findOne(id) {
        console.log('id', id);
        const user = await this.userModel
            .findById(id)
            .select('-password -firebaseUid -__v')
            .populate('collegeId')
            .exec();
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        return user;
    }
    async findAll() {
        return this.userModel
            .find()
            .select('-password -firebaseUid -__v')
            .populate('collegeId')
            .exec();
    }
    async findByCollege(collegeId) {
        return this.userModel
            .find({ collegeId })
            .select('-password -firebaseUid -__v')
            .populate('collegeId')
            .exec();
    }
    async assignRole(id, assignRoleDto) {
        const user = await this.userModel.findById(id);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        user.role = assignRoleDto.role;
        const savedUser = await user.save();
        await savedUser.populate('collegeId');
        const userObject = savedUser.toObject();
        delete userObject.password;
        delete userObject.firebaseUid;
        delete userObject.__v;
        return userObject;
    }
    async create(userData) {
        const user = new this.userModel(userData);
        const savedUser = await user.save();
        await savedUser.populate('collegeId');
        const userObject = savedUser.toObject();
        delete userObject.password;
        delete userObject.firebaseUid;
        delete userObject.__v;
        return userObject;
    }
    async updateCollegeId(userId, collegeId) {
        const user = await this.userModel.findById(userId);
        if (!user) {
            throw new common_1.NotFoundException('User not found');
        }
        await this.userModel.updateOne({ _id: userId }, { $set: { collegeId: collegeId } });
        const updatedUser = await this.userModel
            .findById(userId)
            .select('-password -firebaseUid -__v')
            .populate('collegeId')
            .exec();
        if (!updatedUser) {
            throw new common_1.NotFoundException('User not found after update');
        }
        return updatedUser;
    }
};
exports.UsersService = UsersService;
exports.UsersService = UsersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], UsersService);
//# sourceMappingURL=users.service.js.map