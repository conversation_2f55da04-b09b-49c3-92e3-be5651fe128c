"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReviewQuestionDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class ReviewQuestionDto {
}
exports.ReviewQuestionDto = ReviewQuestionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Review status (approved or rejected)',
        example: 'approved',
        enum: ['approved', 'rejected'],
    }),
    (0, class_validator_1.IsEnum)(['approved', 'rejected'], {
        message: 'Status must be either approved or rejected',
    }),
    __metadata("design:type", String)
], ReviewQuestionDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Optional notes for the review',
        example: 'Question meets quality standards',
        required: false,
        maxLength: 500,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ReviewQuestionDto.prototype, "notes", void 0);
//# sourceMappingURL=review-question.dto.js.map