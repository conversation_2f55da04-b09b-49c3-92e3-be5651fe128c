"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var FirebaseAuthService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.FirebaseAuthService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const admin = require("firebase-admin");
const path = require("path");
let FirebaseAuthService = FirebaseAuthService_1 = class FirebaseAuthService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(FirebaseAuthService_1.name);
        this.initialized = false;
    }
    async onModuleInit() {
        await this.initializeFirebaseAdmin();
    }
    async initializeFirebaseAdmin() {
        try {
            if (!admin.apps.length) {
                const serviceAccountPath = this.configService.get('FIREBASE_SERVICE_ACCOUNT_PATH');
                if (serviceAccountPath) {
                    const serviceAccountFilePath = serviceAccountPath.startsWith('/')
                        ? serviceAccountPath
                        : path.join(process.cwd(), serviceAccountPath);
                    this.logger.log(`Initializing Firebase Admin SDK with service account file: ${serviceAccountFilePath}`);
                    admin.initializeApp({
                        credential: admin.credential.cert(serviceAccountFilePath),
                    });
                }
                else {
                    const projectId = this.configService.get('FIREBASE_PROJECT_ID');
                    const clientEmail = this.configService.get('FIREBASE_CLIENT_EMAIL');
                    const privateKey = this.configService.get('FIREBASE_PRIVATE_KEY');
                    if (!projectId || !clientEmail || !privateKey) {
                        this.logger.error('Missing Firebase credentials in environment variables');
                        throw new Error('Firebase credentials not properly configured');
                    }
                    admin.initializeApp({
                        credential: admin.credential.cert({
                            projectId,
                            clientEmail,
                            privateKey: privateKey.replace(/\\n/g, '\n'),
                        }),
                    });
                }
                this.initialized = true;
                this.logger.log('Firebase Admin SDK initialized successfully');
            }
            else {
                this.initialized = true;
            }
        }
        catch (error) {
            this.logger.error(`Failed to initialize Firebase Admin SDK: ${error.message}`);
            this.initialized = false;
        }
    }
    async verifyToken(token) {
        if (!this.initialized) {
            await this.initializeFirebaseAdmin();
            if (!this.initialized) {
                throw new common_1.UnauthorizedException('Firebase authentication is not available');
            }
        }
        try {
            const decodedToken = await admin.auth().verifyIdToken(token, true);
            return decodedToken;
        }
        catch (error) {
            this.logger.error(`Failed to verify Firebase token: ${error.message}`);
            throw new common_1.UnauthorizedException('Invalid or expired Firebase token');
        }
    }
    async getUserByUid(uid) {
        if (!this.initialized) {
            await this.initializeFirebaseAdmin();
            if (!this.initialized) {
                throw new common_1.UnauthorizedException('Firebase authentication is not available');
            }
        }
        try {
            const stringUid = String(uid);
            return await admin.auth().getUser(stringUid);
        }
        catch (error) {
            this.logger.error(`Failed to get user by UID: ${error.message}`);
            throw new common_1.UnauthorizedException('User not found in Firebase');
        }
    }
};
exports.FirebaseAuthService = FirebaseAuthService;
exports.FirebaseAuthService = FirebaseAuthService = FirebaseAuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], FirebaseAuthService);
//# sourceMappingURL=firebase-auth.service.js.map