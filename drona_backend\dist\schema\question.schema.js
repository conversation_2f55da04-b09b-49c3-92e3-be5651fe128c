"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionSchema = exports.Question = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const swagger_1 = require("@nestjs/swagger");
let Question = class Question {
};
exports.Question = Question;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Question content/text',
        example: 'What is the capital of France?',
        required: true,
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Question.prototype, "content", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Answer options for the question',
        example: ['Paris', 'London', 'Berlin', 'Madrid'],
        type: [String],
        required: true,
    }),
    (0, mongoose_1.Prop)({ type: [String], required: true }),
    __metadata("design:type", Array)
], Question.prototype, "options", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Correct answer to the question',
        example: 'Paris',
        required: true,
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Question.prototype, "answer", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'URLs to images associated with this question',
        example: [
            'https://s3.amazonaws.com/bucket-name/questions/images/image1.jpg',
        ],
        type: [String],
    }),
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], Question.prototype, "imageUrls", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Subject ID this question belongs to',
        example: '60d21b4667d0d8992e610c85',
        type: String,
        required: true,
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, required: true, ref: 'Subject' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Question.prototype, "subjectId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Topic ID this question belongs to',
        example: '60d21b4667d0d8992e610c86',
        type: String,
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'Topic' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Question.prototype, "topicId", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Question difficulty level',
        example: 'medium',
        enum: ['easy', 'medium', 'hard'],
        required: true,
    }),
    (0, mongoose_1.Prop)({ required: true, enum: ['easy', 'medium', 'hard'] }),
    __metadata("design:type", String)
], Question.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Question type (e.g., multiple-choice, true/false)',
        example: 'multiple-choice',
        required: true,
    }),
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], Question.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'User ID who created this question',
        example: '60d21b4667d0d8992e610c87',
        type: String,
        required: true,
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, required: true, ref: 'User' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Question.prototype, "createdBy", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Question status',
        example: 'active',
        enum: ['active', 'inactive'],
        default: 'inactive',
    }),
    (0, mongoose_1.Prop)({ default: 'inactive', enum: ['active', 'inactive'] }),
    __metadata("design:type", String)
], Question.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Question review status',
        example: 'pending',
        enum: ['pending', 'approved', 'rejected'],
        default: 'pending',
    }),
    (0, mongoose_1.Prop)({ default: 'pending', enum: ['pending', 'approved', 'rejected'] }),
    __metadata("design:type", String)
], Question.prototype, "reviewStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'User ID who reviewed this question',
        example: '60d21b4667d0d8992e610c89',
        type: String,
    }),
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], Question.prototype, "reviewedBy", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Date when the question was reviewed',
        example: '2023-07-21T15:30:00.000Z',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], Question.prototype, "reviewDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Notes from the reviewer',
        example: 'Approved with minor edits to option B',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Question.prototype, "reviewNotes", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Source of the question (e.g., "pdf-upload", "manual-entry")',
        example: 'pdf-upload',
    }),
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], Question.prototype, "source", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Detailed solution for the question',
        example: {
            steps: ['Step 1: Analyze the problem', 'Step 2: Apply the formula'],
            methodology: 'Direct calculation method',
            key_concepts: ['concept1', 'concept2'],
            final_explanation: 'The answer is derived from...'
        },
    }),
    (0, mongoose_1.Prop)({ type: Object }),
    __metadata("design:type", Object)
], Question.prototype, "solution", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Helpful hints for solving the question',
        example: ['Hint 1: Consider the formula', 'Hint 2: Check units'],
        type: [String],
    }),
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], Question.prototype, "hints", void 0);
exports.Question = Question = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], Question);
exports.QuestionSchema = mongoose_1.SchemaFactory.createForClass(Question);
//# sourceMappingURL=question.schema.js.map