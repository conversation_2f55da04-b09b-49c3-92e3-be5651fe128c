{"name": "frac", "version": "1.1.2", "author": "SheetJS", "description": "Rational approximation with bounded denominator", "keywords": ["math", "fraction", "rational", "approximation"], "main": "./frac", "types": "types", "dependencies": {}, "devDependencies": {"voc": "~1.1.0", "mocha": "~2.5.3", "blanket": "~1.2.3", "codepage": "~1.10.0", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/frac.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "frac.js"}}, "homepage": "http://sheetjs.com/opensource", "bugs": {"url": "https://github.com/SheetJS/frac/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}