"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SubjectsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubjectsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const subject_schema_1 = require("../schema/subject.schema");
const topic_schema_1 = require("../schema/topic.schema");
let SubjectsService = SubjectsService_1 = class SubjectsService {
    constructor(subjectModel, topicModel) {
        this.subjectModel = subjectModel;
        this.topicModel = topicModel;
        this.logger = new common_1.Logger(SubjectsService_1.name);
    }
    async create(createSubjectDto) {
        try {
            const existingSubject = await this.subjectModel.findOne({
                name: { $regex: new RegExp(`^${createSubjectDto.name}$`, 'i') },
            });
            if (existingSubject) {
                throw new common_1.ConflictException(`Subject with name '${createSubjectDto.name}' already exists`);
            }
            const createdSubject = new this.subjectModel(createSubjectDto);
            const savedSubject = await createdSubject.save();
            this.logger.log(`Subject created: ${savedSubject.name} (ID: ${savedSubject._id})`);
            return savedSubject;
        }
        catch (error) {
            this.logger.error(`Failed to create subject: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findAll() {
        try {
            return await this.subjectModel.find().sort({ name: 1 }).exec();
        }
        catch (error) {
            this.logger.error(`Failed to fetch subjects: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findAllWithTopics() {
        try {
            const subjects = await this.subjectModel
                .aggregate([
                {
                    $lookup: {
                        from: 'topics',
                        localField: '_id',
                        foreignField: 'subjectId',
                        as: 'topics',
                    },
                },
                {
                    $project: {
                        _id: 1,
                        name: 1,
                        description: 1,
                        topics: {
                            $map: {
                                input: {
                                    $sortArray: {
                                        input: '$topics',
                                        sortBy: { name: 1 },
                                    },
                                },
                                as: 'topic',
                                in: {
                                    _id: '$$topic._id',
                                    name: '$$topic.name',
                                    description: '$$topic.description',
                                },
                            },
                        },
                    },
                },
                { $sort: { name: 1 } },
            ])
                .exec();
            return subjects;
        }
        catch (error) {
            this.logger.error(`Failed to fetch subjects with topics: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findOne(id) {
        try {
            const subject = await this.subjectModel.findById(id).exec();
            if (!subject) {
                throw new common_1.NotFoundException(`Subject with ID ${id} not found`);
            }
            return subject;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch subject: ${error.message}`, error.stack);
            throw error;
        }
    }
    async update(id, updateSubjectDto) {
        try {
            const existingSubject = await this.findOne(id);
            if (updateSubjectDto.name &&
                updateSubjectDto.name !== existingSubject.name) {
                const conflictingSubject = await this.subjectModel.findOne({
                    _id: { $ne: id },
                    name: { $regex: new RegExp(`^${updateSubjectDto.name}$`, 'i') },
                });
                if (conflictingSubject) {
                    throw new common_1.ConflictException(`Subject with name '${updateSubjectDto.name}' already exists`);
                }
            }
            const updatedSubject = await this.subjectModel
                .findByIdAndUpdate(id, updateSubjectDto, {
                new: true,
                runValidators: true,
            })
                .exec();
            if (!updatedSubject) {
                throw new common_1.NotFoundException(`Subject with ID ${id} not found`);
            }
            this.logger.log(`Subject updated: ${updatedSubject.name} (ID: ${id})`);
            return updatedSubject;
        }
        catch (error) {
            this.logger.error(`Failed to update subject: ${error.message}`, error.stack);
            throw error;
        }
    }
    async remove(id) {
        try {
            await this.findOne(id);
            const topicsCount = await this.topicModel.countDocuments({
                subjectId: id,
            });
            if (topicsCount > 0) {
                throw new common_1.ConflictException(`Cannot delete subject. It has ${topicsCount} associated topics. Please delete the topics first.`);
            }
            await this.subjectModel.findByIdAndDelete(id).exec();
            this.logger.log(`Subject deleted: ID ${id}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete subject: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.SubjectsService = SubjectsService;
exports.SubjectsService = SubjectsService = SubjectsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(subject_schema_1.Subject.name)),
    __param(1, (0, mongoose_1.InjectModel)(topic_schema_1.Topic.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], SubjectsService);
//# sourceMappingURL=subjects.service.js.map