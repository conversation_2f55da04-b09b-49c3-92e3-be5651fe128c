"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserActivitySchema = exports.UserActivity = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("./user.schema");
const college_schema_1 = require("./college.schema");
let UserActivity = class UserActivity {
};
exports.UserActivity = UserActivity;
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", user_schema_1.User)
], UserActivity.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'College' }),
    __metadata("design:type", college_schema_1.College)
], UserActivity.prototype, "collegeId", void 0);
__decorate([
    (0, mongoose_1.Prop)({
        required: true,
        enum: ['login', 'question_creation', 'paper_generation'],
    }),
    __metadata("design:type", String)
], UserActivity.prototype, "activityType", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.Mixed }),
    __metadata("design:type", Object)
], UserActivity.prototype, "activityDetails", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, default: Date.now }),
    __metadata("design:type", Date)
], UserActivity.prototype, "timestamp", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], UserActivity.prototype, "ipAddress", void 0);
exports.UserActivity = UserActivity = __decorate([
    (0, mongoose_1.Schema)()
], UserActivity);
exports.UserActivitySchema = mongoose_1.SchemaFactory.createForClass(UserActivity);
//# sourceMappingURL=user-activity.schema.js.map