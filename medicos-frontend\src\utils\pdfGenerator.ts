import jsPDF from 'jspdf';
import katex from 'katex';
import 'katex/dist/katex.min.css';
import { extractImagesFromText, isBase64Image, ensureDataUrl } from '@/utils/imageUtils';

interface QuestionPaper {
  _id: string;
  title: string;
  subjectId?: {
    name: string;
  };
  duration: number;
  totalMarks: number;
  instructions?: string;
  questions: Question[];
  withAnswers: boolean;
  withSolutions?: boolean;
  withHints?: boolean;
  isMultiSubject?: boolean;
  sections?: Section[];
}

interface Section {
  name: string;
  description?: string;
  order: number;
  sectionMarks: number;
  subjectId?: string;
  subjectName?: string;
  questions: Array<{
    question: Question;
    order: number;
  }>;
}

interface Question {
  _id: string;
  content: string;
  options?: string[];
  answer?: string;
  marks: number;
  difficulty: string;
  type: string;
  imageUrls?: string[];
  solution?: {
    steps: string[];
    methodology: string;
    key_concepts: string[];
    final_explanation: string;
  };
  hints?: string[];
}

interface CollegeInfo {
  name: string;
  logoUrl?: string;
  address?: string;
}

interface ProcessedText {
  cleanText: string;
  images: Array<{
    id: string;
    src: string;
    alt: string;
  }>;
}

export class PDFGenerator {
  private doc: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  private margin: number;
  private currentY: number;
  private contentWidth: number;

  constructor() {
    this.doc = new jsPDF('p', 'mm', 'a4');
    this.pageWidth = this.doc.internal.pageSize.getWidth();
    this.pageHeight = this.doc.internal.pageSize.getHeight();
    this.margin = 20;
    this.currentY = this.margin;

    // Single column layout - full width
    this.contentWidth = this.pageWidth - this.margin * 2;

    // Set default font to Times
    try {
      this.doc.setFont('times', 'normal');
    } catch (error) {
      console.warn('Times font not available, using default');
      this.doc.setFont('helvetica', 'normal');
    }
  }

  private addWatermark(): void {
    try {
      // Save current graphics state
      this.doc.saveGraphicsState();

      // Set low opacity for watermark
      this.doc.setGState(this.doc.GState({ opacity: 0.08 }));

      // Calculate center position
      const centerX = this.pageWidth / 2;
      const centerY = this.pageHeight / 2;

      // Add watermark text with rotation
      this.doc.setFontSize(60);
      this.doc.setFont('times', 'bold');
      this.doc.setTextColor(150, 150, 150);

      // Rotate and add watermark text
      this.doc.text('MEDICOS', centerX, centerY, {
        angle: 45,
        align: 'center'
      });

      // Restore graphics state
      this.doc.restoreGraphicsState();

      // Reset text color to black
      this.doc.setTextColor(0, 0, 0);
    } catch (error) {
      console.warn('Could not add watermark:', error);
    }
  }

  private addCollegeHeader(collegeInfo: CollegeInfo): void {
    if (!collegeInfo) return;

    // Add college logo if available (Medicos logo in top right)
    try {
      this.doc.setFontSize(8);
      this.doc.setFont('times', 'normal');
      this.doc.setTextColor(100, 100, 100);
      this.doc.text('MEDICOS', this.pageWidth - this.margin - 15, this.currentY + 3, { align: 'center' });
      this.doc.setTextColor(0, 0, 0); // Reset to black
    } catch (error) {
      console.warn('Could not add logo:', error);
    }

    this.doc.setFontSize(14);
    this.doc.setFont('times', 'bold');

    // College name
    if (collegeInfo.name) {
      this.doc.text(collegeInfo.name, this.pageWidth / 2, this.currentY, { align: 'center' });
      this.currentY += 6;
    }

    // College address
    if (collegeInfo.address) {
      this.doc.setFontSize(10);
      this.doc.setFont('times', 'normal');
      this.doc.text(collegeInfo.address, this.pageWidth / 2, this.currentY, { align: 'center' });
      this.currentY += 6;
    }

    // Minimal space after college info
    this.currentY += 3;
  }

  private async addImageToPDF(imageSrc: string, x: number, y: number, maxWidth: number = 40, maxHeight: number = 30): Promise<number> {
    try {
      // Ensure the image has proper data URL format
      const dataUrl = ensureDataUrl(imageSrc);

      // Add image to PDF with smaller size for better layout
      this.doc.addImage(dataUrl, 'JPEG', x, y, maxWidth, maxHeight);

      // Return the height used
      return maxHeight + 2; // Add small margin
    } catch (error) {
      console.warn('Failed to add image to PDF:', error);

      // Add placeholder text for failed images
      this.doc.setFontSize(8);
      this.doc.setTextColor(150, 150, 150);
      this.doc.text('[Image]', x, y + 5);
      this.doc.setTextColor(0, 0, 0);

      return 8; // Return small height for placeholder
    }
  }

  private processTextWithImages(text: string): ProcessedText {
    try {
      // Use the same image extraction logic as the admin question bank
      const { cleanText, images } = extractImagesFromText(text);

      // Process the clean text for LaTeX rendering
      const processedText = this.renderLatex(cleanText);

      return {
        cleanText: processedText,
        images: images
      };
    } catch (error) {
      console.warn('Text processing error:', error);
      return {
        cleanText: text,
        images: []
      };
    }
  }

  private renderLatex(text: string): string {
    try {
      // Handle common LaTeX patterns - enhanced version
      let processedText = text;

      // Replace common LaTeX symbols with Unicode equivalents
      const latexReplacements: { [key: string]: string } = {
        // Remove LaTeX formatting
        '\\mathrm{': '',
        '\\text{': '',
        '\\rm{': '',

        // Greek letters
        '\\phi': 'φ',
        '\\Phi': 'Φ',
        '\\alpha': 'α',
        '\\beta': 'β',
        '\\gamma': 'γ',
        '\\Gamma': 'Γ',
        '\\delta': 'δ',
        '\\Delta': 'Δ',
        '\\epsilon': 'ε',
        '\\varepsilon': 'ε',
        '\\zeta': 'ζ',
        '\\eta': 'η',
        '\\theta': 'θ',
        '\\Theta': 'Θ',
        '\\iota': 'ι',
        '\\kappa': 'κ',
        '\\lambda': 'λ',
        '\\Lambda': 'Λ',
        '\\mu': 'μ',
        '\\nu': 'ν',
        '\\xi': 'ξ',
        '\\Xi': 'Ξ',
        '\\pi': 'π',
        '\\Pi': 'Π',
        '\\rho': 'ρ',
        '\\sigma': 'σ',
        '\\Sigma': 'Σ',
        '\\tau': 'τ',
        '\\upsilon': 'υ',
        '\\Upsilon': 'Υ',
        '\\chi': 'χ',
        '\\psi': 'ψ',
        '\\Psi': 'Ψ',
        '\\omega': 'ω',
        '\\Omega': 'Ω',

        // Arrows
        '\\rightarrow': '→',
        '\\leftarrow': '←',
        '\\leftrightarrow': '↔',
        '\\Rightarrow': '⇒',
        '\\Leftarrow': '⇐',
        '\\Leftrightarrow': '⇔',
        '\\uparrow': '↑',
        '\\downarrow': '↓',

        // Mathematical operators
        '\\pm': '±',
        '\\mp': '∓',
        '\\times': '×',
        '\\div': '÷',
        '\\cdot': '·',
        '\\ast': '*',
        '\\star': '⋆',
        '\\circ': '∘',
        '\\bullet': '•',

        // Relations
        '\\leq': '≤',
        '\\geq': '≥',
        '\\neq': '≠',
        '\\equiv': '≡',
        '\\approx': '≈',
        '\\cong': '≅',
        '\\sim': '∼',
        '\\propto': '∝',
        '\\parallel': '∥',
        '\\perp': '⊥',

        // Set theory
        '\\in': '∈',
        '\\notin': '∉',
        '\\subset': '⊂',
        '\\supset': '⊃',
        '\\subseteq': '⊆',
        '\\supseteq': '⊇',
        '\\cup': '∪',
        '\\cap': '∩',
        '\\emptyset': '∅',

        // Calculus
        '\\infty': '∞',
        '\\sum': 'Σ',
        '\\prod': 'Π',
        '\\int': '∫',
        '\\oint': '∮',
        '\\partial': '∂',
        '\\nabla': '∇',

        // Logic
        '\\land': '∧',
        '\\lor': '∨',
        '\\neg': '¬',
        '\\forall': '∀',
        '\\exists': '∃',

        // Remove dollar signs and braces
        '$': '',
        '{': '',
        '}': '',
      };

      // Apply replacements
      for (const [latex, replacement] of Object.entries(latexReplacements)) {
        processedText = processedText.replace(new RegExp(latex.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacement);
      }

      // Handle subscripts (convert to Unicode subscripts where possible)
      const subscriptMap: { [key: string]: string } = {
        '0': '₀', '1': '₁', '2': '₂', '3': '₃', '4': '₄',
        '5': '₅', '6': '₆', '7': '₇', '8': '₈', '9': '₉',
        '+': '₊', '-': '₋', '=': '₌', '(': '₍', ')': '₎',
        'a': 'ₐ', 'e': 'ₑ', 'h': 'ₕ', 'i': 'ᵢ', 'j': 'ⱼ',
        'k': 'ₖ', 'l': 'ₗ', 'm': 'ₘ', 'n': 'ₙ', 'o': 'ₒ',
        'p': 'ₚ', 'r': 'ᵣ', 's': 'ₛ', 't': 'ₜ', 'u': 'ᵤ',
        'v': 'ᵥ', 'x': 'ₓ'
      };

      // Handle superscripts (convert to Unicode superscripts where possible)
      const superscriptMap: { [key: string]: string } = {
        '0': '⁰', '1': '¹', '2': '²', '3': '³', '4': '⁴',
        '5': '⁵', '6': '⁶', '7': '⁷', '8': '⁸', '9': '⁹',
        '+': '⁺', '-': '⁻', '=': '⁼', '(': '⁽', ')': '⁾',
        'a': 'ᵃ', 'b': 'ᵇ', 'c': 'ᶜ', 'd': 'ᵈ', 'e': 'ᵉ',
        'f': 'ᶠ', 'g': 'ᵍ', 'h': 'ʰ', 'i': 'ⁱ', 'j': 'ʲ',
        'k': 'ᵏ', 'l': 'ˡ', 'm': 'ᵐ', 'n': 'ⁿ', 'o': 'ᵒ',
        'p': 'ᵖ', 'r': 'ʳ', 's': 'ˢ', 't': 'ᵗ', 'u': 'ᵘ',
        'v': 'ᵛ', 'w': 'ʷ', 'x': 'ˣ', 'y': 'ʸ', 'z': 'ᶻ'
      };

      // Process subscripts
      processedText = processedText.replace(/_{([^}]+)}/g, (match, content) => {
        return content.split('').map((char: string) => subscriptMap[char] || char).join('');
      });

      // Process superscripts
      processedText = processedText.replace(/\^{([^}]+)}/g, (match, content) => {
        return content.split('').map((char: string) => superscriptMap[char] || char).join('');
      });

      // Handle fractions (basic conversion)
      processedText = processedText.replace(/\\frac{([^}]+)}{([^}]+)}/g, '($1)/($2)');

      // Handle square roots
      processedText = processedText.replace(/\\sqrt{([^}]+)}/g, '√($1)');

      // Clean up any remaining braces
      processedText = processedText.replace(/[{}]/g, '');

      return processedText;
    } catch (error) {
      console.warn('LaTeX rendering error:', error);
      return text; // Return original text if rendering fails
    }
  }

  private addTitle(title: string): void {
    this.doc.setFontSize(16);
    this.doc.setFont('times', 'bold');

    // Add underline effect
    const titleWidth = this.doc.getTextWidth(title);
    const titleX = (this.pageWidth - titleWidth) / 2;

    this.doc.text(title, this.pageWidth / 2, this.currentY, { align: 'center' });

    // Draw underline
    this.doc.line(titleX, this.currentY + 1, titleX + titleWidth, this.currentY + 1);

    this.currentY += 8;
  }

  private addExamDetails(questionPaper: QuestionPaper): void {
    this.doc.setFontSize(11);
    this.doc.setFont('times', 'normal');

    // Subject (for single subject papers)
    if (questionPaper.subjectId?.name && !questionPaper.isMultiSubject) {
      this.doc.text(`Subject: ${questionPaper.subjectId.name}`, this.pageWidth / 2, this.currentY, { align: 'center' });
      this.currentY += 6;
    }

    // Duration and marks - centered layout
    const detailsText = `Duration: ${questionPaper.duration} minutes | Total Marks: ${questionPaper.totalMarks}`;
    this.doc.text(detailsText, this.pageWidth / 2, this.currentY, { align: 'center' });
    this.currentY += 8;

    // Instructions
    if (questionPaper.instructions) {
      this.doc.setFont('times', 'bold');
      this.doc.text('Instructions:', this.margin, this.currentY);
      this.currentY += 5;

      this.doc.setFont('times', 'normal');
      this.doc.setFontSize(9);
      const instructionLines = this.doc.splitTextToSize(questionPaper.instructions, this.contentWidth);
      this.doc.text(instructionLines, this.margin, this.currentY);
      this.currentY += instructionLines.length * 3 + 6;
    }

    // Add horizontal line
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    this.currentY += 6;
  }

  private checkPageBreak(requiredHeight: number): boolean {
    // More aggressive page break - only break when absolutely necessary
    return this.currentY + requiredHeight > this.pageHeight - 10;
  }

  private addNewPage(collegeInfo?: CollegeInfo, subjectName?: string): void {
    this.doc.addPage();
    this.addWatermark();

    // Always add college header on new pages
    this.currentY = this.margin;
    if (collegeInfo) {
      this.addCollegeHeader(collegeInfo);
    }

    // Add subject header if this is a multi-subject paper
    if (subjectName) {
      this.addSubjectHeader(subjectName);
    }
  }

  private addSubjectHeader(subjectName: string): void {
    this.doc.setFontSize(14);
    this.doc.setFont('times', 'bold');
    this.doc.text(subjectName, this.pageWidth / 2, this.currentY, { align: 'center' });
    this.currentY += 6;

    // Add horizontal line under subject name
    this.doc.line(this.margin, this.currentY, this.pageWidth - this.margin, this.currentY);
    this.currentY += 8;
  }

  private async addQuestion(question: Question, questionNumber: number, withAnswers: boolean, collegeInfo?: CollegeInfo, subjectName?: string): Promise<void> {
    // Estimate required height for this question
    const estimatedHeight = this.estimateQuestionHeight(question, withAnswers);

    // More intelligent page break - try to fit the question first
    if (this.checkPageBreak(estimatedHeight)) {
      // Check if we can fit at least the question text and first option
      const minHeight = 15; // Minimum space needed for question start
      if (this.currentY + minHeight > this.pageHeight - 8) {
        this.addNewPage(collegeInfo, subjectName);
      }
    }

    // Process question content with images
    const questionContent = this.processTextWithImages(question.content);

    // Question number and text
    this.doc.setFontSize(11);
    this.doc.setFont('times', 'bold');
    this.doc.text(`${questionNumber}.`, this.margin, this.currentY);

    // Question content
    this.doc.setFont('times', 'normal');
    const questionText = questionContent.cleanText;
    const questionLines = this.doc.splitTextToSize(questionText, this.contentWidth - 12);
    this.doc.text(questionLines, this.margin + 12, this.currentY);
    this.currentY += Math.max(questionLines.length * 4, 5) + 2;

    // Add question images if any
    if (questionContent.images.length > 0) {
      for (const image of questionContent.images) {
        const imageHeight = await this.addImageToPDF(image.src, this.margin + 12, this.currentY, 50, 35);
        this.currentY += imageHeight;
      }
    }

    // Options with compact formatting
    if (question.options && question.options.length > 0) {
      this.doc.setFontSize(10);
      this.doc.setFont('times', 'normal');

      for (let optIndex = 0; optIndex < question.options.length; optIndex++) {
        const option = question.options[optIndex];
        const optionLabel = String.fromCharCode(97 + optIndex); // a, b, c, d

        // Check if we have space for this option, if not, continue on new page
        if (this.currentY > this.pageHeight - 20) {
          this.addNewPage(collegeInfo, subjectName);
        }

        // Process option content with images
        const optionContent = this.processTextWithImages(option);

        // Option label and text on same line
        this.doc.text(`${optionLabel})`, this.margin + 15, this.currentY);

        // Option text
        const optionLines = this.doc.splitTextToSize(optionContent.cleanText, this.contentWidth - 25);
        this.doc.text(optionLines, this.margin + 25, this.currentY);
        this.currentY += Math.max(optionLines.length * 4, 4) + 1;

        // Add option images if any
        if (optionContent.images.length > 0) {
          for (const image of optionContent.images) {
            const imageHeight = await this.addImageToPDF(image.src, this.margin + 25, this.currentY, 40, 30);
            this.currentY += imageHeight;
          }
        }
      }
    }

    // Answer (if included) - Bold black text
    if (withAnswers && question.answer) {
      this.currentY += 2; // Minimal space before answer
      this.doc.setFont('times', 'bold');
      this.doc.setTextColor(0, 0, 0);

      const answerContent = this.processTextWithImages(question.answer);

      // Answer label and text
      this.doc.text('Answer:', this.margin + 15, this.currentY);

      // Answer text
      const answerLines = this.doc.splitTextToSize(answerContent.cleanText, this.contentWidth - 40);
      this.doc.text(answerLines, this.margin + 40, this.currentY);
      this.currentY += Math.max(answerLines.length * 4, 4) + 1;

      // Add answer images if any
      if (answerContent.images.length > 0) {
        for (const image of answerContent.images) {
          const imageHeight = await this.addImageToPDF(image.src, this.margin + 40, this.currentY, 40, 30);
          this.currentY += imageHeight;
        }
      }

      // Reset to normal font
      this.doc.setFont('times', 'normal');
      this.doc.setTextColor(0, 0, 0);
    }

    // Minimal spacing after question
    this.currentY += 4;
  }

  private async addQuestionWithSolutions(question: Question, questionNumber: number, withAnswers: boolean, withSolutions: boolean, withHints: boolean, collegeInfo?: CollegeInfo, subjectName?: string): Promise<void> {
    // Estimate required height for this question with solutions
    const estimatedHeight = this.estimateQuestionHeightWithSolutions(question, withAnswers, withSolutions, withHints);

    // Check for page break
    if (this.checkPageBreak(estimatedHeight)) {
      const minHeight = 20; // Minimum space needed for question start with solutions
      if (this.currentY + minHeight > this.pageHeight - 8) {
        this.addNewPage(collegeInfo, subjectName);
      }
    }

    // First add the basic question
    await this.addQuestion(question, questionNumber, withAnswers, collegeInfo, subjectName);

    // Add solutions if requested and available
    if (withSolutions && question.solution) {
      this.currentY += 2;
      this.doc.setFont('times', 'bold');
      this.doc.setTextColor(0, 0, 0);
      this.doc.text('Solution:', this.margin + 15, this.currentY);
      this.currentY += 5;

      // Add methodology if available
      if (question.solution.methodology) {
        this.doc.setFont('times', 'italic');
        this.doc.text('Methodology:', this.margin + 20, this.currentY);
        this.doc.setFont('times', 'normal');
        const methodologyLines = this.doc.splitTextToSize(question.solution.methodology, this.contentWidth - 50);
        this.doc.text(methodologyLines, this.margin + 20, this.currentY + 4);
        this.currentY += Math.max(methodologyLines.length * 4, 4) + 3;
      }

      // Add solution steps
      if (question.solution.steps && question.solution.steps.length > 0) {
        this.doc.setFont('times', 'italic');
        this.doc.text('Steps:', this.margin + 20, this.currentY);
        this.currentY += 4;

        question.solution.steps.forEach((step, index) => {
          this.doc.setFont('times', 'normal');
          const stepText = `${index + 1}. ${step}`;
          const stepLines = this.doc.splitTextToSize(stepText, this.contentWidth - 55);
          this.doc.text(stepLines, this.margin + 25, this.currentY);
          this.currentY += Math.max(stepLines.length * 4, 4) + 2;
        });
      }

      // Add key concepts
      if (question.solution.key_concepts && question.solution.key_concepts.length > 0) {
        this.doc.setFont('times', 'italic');
        this.doc.text('Key Concepts:', this.margin + 20, this.currentY);
        this.doc.setFont('times', 'normal');
        const conceptsText = question.solution.key_concepts.join(', ');
        const conceptsLines = this.doc.splitTextToSize(conceptsText, this.contentWidth - 50);
        this.doc.text(conceptsLines, this.margin + 20, this.currentY + 4);
        this.currentY += Math.max(conceptsLines.length * 4, 4) + 3;
      }

      // Add final explanation
      if (question.solution.final_explanation) {
        this.doc.setFont('times', 'italic');
        this.doc.text('Explanation:', this.margin + 20, this.currentY);
        this.doc.setFont('times', 'normal');
        const explanationLines = this.doc.splitTextToSize(question.solution.final_explanation, this.contentWidth - 50);
        this.doc.text(explanationLines, this.margin + 20, this.currentY + 4);
        this.currentY += Math.max(explanationLines.length * 4, 4) + 3;
      }
    }

    // Add hints if requested and available
    if (withHints && question.hints && question.hints.length > 0) {
      this.currentY += 2;
      this.doc.setFont('times', 'bold');
      this.doc.setTextColor(0, 0, 0);
      this.doc.text('Hints:', this.margin + 15, this.currentY);
      this.currentY += 5;

      question.hints.forEach((hint, index) => {
        this.doc.setFont('times', 'normal');
        const hintText = `• ${hint}`;
        const hintLines = this.doc.splitTextToSize(hintText, this.contentWidth - 50);
        this.doc.text(hintLines, this.margin + 20, this.currentY);
        this.currentY += Math.max(hintLines.length * 4, 4) + 2;
      });
    }

    // Reset font and add spacing
    this.doc.setFont('times', 'normal');
    this.doc.setTextColor(0, 0, 0);
    this.currentY += 6;
  }

  private estimateQuestionHeight(question: Question, withAnswers: boolean): number {
    let height = 10; // Base question height (further reduced)

    // Add height for options (more conservative)
    if (question.options && question.options.length > 0) {
      height += question.options.length * 4; // Reduced from 5 to 4
    }

    // Add height for answer if included
    if (withAnswers && question.answer) {
      height += 6; // Reduced from 10 to 6
    }

    // Add minimal height for potential images (only if content suggests images)
    const hasImages = question.content.includes('base64') ||
                     (question.options && question.options.some(opt => opt.includes('base64'))) ||
                     (question.answer && question.answer.includes('base64'));

    if (hasImages) {
      height += 20; // Only add image height if images are likely present
    } else {
      height += 5; // Minimal buffer for text-only questions
    }

    return height;
  }

  private estimateQuestionHeightWithSolutions(question: Question, withAnswers: boolean, withSolutions: boolean, withHints: boolean): number {
    let height = this.estimateQuestionHeight(question, withAnswers);

    // Add height for solutions if included
    if (withSolutions && question.solution) {
      height += 15; // Base solution height

      if (question.solution.methodology) {
        height += 8;
      }

      if (question.solution.steps && question.solution.steps.length > 0) {
        height += question.solution.steps.length * 6;
      }

      if (question.solution.key_concepts && question.solution.key_concepts.length > 0) {
        height += 8;
      }

      if (question.solution.final_explanation) {
        height += 12;
      }
    }

    // Add height for hints if included
    if (withHints && question.hints && question.hints.length > 0) {
      height += 10 + (question.hints.length * 6);
    }

    return height;
  }

  public async generatePDF(questionPaper: QuestionPaper, collegeInfo?: CollegeInfo): Promise<Blob> {
    try {
      console.log("PDF Generator - Starting generation with:", {
        questionPaper: questionPaper,
        collegeInfo: collegeInfo
      });

      // Check if this is a multi-subject paper
      const isMultiSubject = questionPaper.isMultiSubject && questionPaper.sections && questionPaper.sections.length > 1;

      if (isMultiSubject) {
        // Handle multi-subject papers - separate page for each subject
        await this.generateMultiSubjectPDF(questionPaper, collegeInfo);
      } else {
        // Handle single subject papers
        await this.generateSingleSubjectPDF(questionPaper, collegeInfo);
      }

      // Generate and return blob
      const pdfBlob = this.doc.output('blob');
      return pdfBlob;

    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error('Failed to generate PDF: ' + error.message);
    }
  }

  private async generateSingleSubjectPDF(questionPaper: QuestionPaper, collegeInfo?: CollegeInfo): Promise<void> {
    // Add watermark to first page
    this.addWatermark();

    // Always add college header on first page
    if (collegeInfo) {
      this.addCollegeHeader(collegeInfo);
    }

    // Add title
    this.addTitle(questionPaper.title);

    // Add exam details
    this.addExamDetails(questionPaper);

    // Add questions
    console.log("Adding questions to PDF:", questionPaper.questions.length);
    for (let index = 0; index < questionPaper.questions.length; index++) {
      const question = questionPaper.questions[index];
      console.log(`Processing question ${index + 1}:`, question);
      try {
        if (questionPaper.withSolutions || questionPaper.withHints) {
          await this.addQuestionWithSolutions(
            question,
            index + 1,
            questionPaper.withAnswers,
            questionPaper.withSolutions || false,
            questionPaper.withHints || false,
            collegeInfo
          );
        } else {
          await this.addQuestion(question, index + 1, questionPaper.withAnswers, collegeInfo);
        }
      } catch (error) {
        console.error(`Error processing question ${index + 1}:`, error);
        // Continue with next question
      }
    }
  }

  private async generateMultiSubjectPDF(questionPaper: QuestionPaper, collegeInfo?: CollegeInfo): Promise<void> {
    if (!questionPaper.sections) return;

    let overallQuestionNumber = 1;

    for (let sectionIndex = 0; sectionIndex < questionPaper.sections.length; sectionIndex++) {
      const section = questionPaper.sections[sectionIndex];
      const subjectName = section.subjectName || section.name || `Subject ${sectionIndex + 1}`;

      // Add new page for each subject (except the first one)
      if (sectionIndex === 0) {
        // First page setup
        this.addWatermark();
        if (collegeInfo) {
          this.addCollegeHeader(collegeInfo);
        }
        this.addTitle(questionPaper.title);
        this.addExamDetails(questionPaper);
        this.addSubjectHeader(subjectName);
      } else {
        // New page for subsequent subjects
        this.addNewPage(collegeInfo, subjectName);
      }

      // Add questions for this section
      if (section.questions && section.questions.length > 0) {
        console.log(`Adding questions for ${subjectName}:`, section.questions.length);

        for (const questionItem of section.questions) {
          const question = questionItem.question;
          console.log(`Processing question ${overallQuestionNumber}:`, question);
          try {
            if (questionPaper.withSolutions || questionPaper.withHints) {
              await this.addQuestionWithSolutions(
                question,
                overallQuestionNumber,
                questionPaper.withAnswers,
                questionPaper.withSolutions || false,
                questionPaper.withHints || false,
                collegeInfo,
                subjectName
              );
            } else {
              await this.addQuestion(question, overallQuestionNumber, questionPaper.withAnswers, collegeInfo, subjectName);
            }
            overallQuestionNumber++;
          } catch (error) {
            console.error(`Error processing question ${overallQuestionNumber}:`, error);
            overallQuestionNumber++;
            // Continue with next question
          }
        }
      }
    }
  }
}

export default PDFGenerator;
