"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var QuestionPapersController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.QuestionPapersController = void 0;
const common_1 = require("@nestjs/common");
const path = require("path");
const question_papers_service_1 = require("./question-papers.service");
const create_question_paper_dto_1 = require("./dto/create-question-paper.dto");
const update_question_paper_dto_1 = require("./dto/update-question-paper.dto");
const set_question_limit_dto_1 = require("./dto/set-question-limit.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const role_enum_1 = require("../auth/enums/role.enum");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../common/services");
let QuestionPapersController = QuestionPapersController_1 = class QuestionPapersController {
    constructor(questionPapersService, trackingService) {
        this.questionPapersService = questionPapersService;
        this.trackingService = trackingService;
        this.logger = new common_1.Logger(QuestionPapersController_1.name);
    }
    async create(req, createQuestionPaperDto) {
        const result = await this.questionPapersService.createUnified(createQuestionPaperDto, req.user);
        const questionPaper = result.questionPaper;
        if (questionPaper.isMultiSubject && createQuestionPaperDto.subjects) {
            for (let i = 0; i < createQuestionPaperDto.subjects.length; i++) {
                const subjectConfig = createQuestionPaperDto.subjects[i];
                const subject = await this.questionPapersService.resolveSubject(subjectConfig.subject);
                if (subject) {
                    await this.trackingService.trackPaperGeneration({
                        userId: req.user._id,
                        paperId: questionPaper._id.toString(),
                        collegeId: req.user.collegeId,
                        subjectId: subject._id.toString(),
                        ipAddress: req.ip,
                    });
                }
            }
        }
        else {
            await this.trackingService.trackPaperGeneration({
                userId: req.user._id,
                paperId: questionPaper._id.toString(),
                collegeId: req.user.collegeId,
                subjectId: questionPaper.subjectId.toString(),
                ipAddress: req.ip,
            });
        }
        return result;
    }
    findAll(req) {
        return this.questionPapersService.findAll(req.user);
    }
    findOne(req, id) {
        return this.questionPapersService.findOne(id, req.user);
    }
    async download(id, format = 'pdf', req, res) {
        try {
            if (format !== 'pdf' && format !== 'docx') {
                return res.status(400).json({
                    statusCode: 400,
                    message: `Invalid format: ${format}. Supported formats are 'pdf' and 'docx'.`,
                });
            }
            const filePath = await this.questionPapersService.download(id, format, req.user);
            await this.trackingService.trackDownload({
                userId: req.user._id,
                paperId: id,
                collegeId: req.user.collegeId,
                downloadFormat: format,
                ipAddress: req.ip,
            });
            return res.download(filePath, path.basename(filePath), (err) => {
                if (err) {
                    this.logger.error(`Error sending file: ${err.message}`, err.stack);
                    if (!res.headersSent) {
                        return res.status(500).json({
                            statusCode: 500,
                            message: 'Error sending file',
                        });
                    }
                }
            });
        }
        catch (error) {
            this.logger.error(`Error in download: ${error.message}`, error.stack);
            if (!res.headersSent) {
                const status = error.status || 500;
                const message = error.message || 'Internal server error';
                return res.status(status).json({
                    statusCode: status,
                    message,
                });
            }
        }
    }
    setQuestionLimit(setQuestionLimitDto) {
        return this.questionPapersService.setQuestionLimit(setQuestionLimitDto);
    }
    update(req, id, updateQuestionPaperDto) {
        return this.questionPapersService.update(id, updateQuestionPaperDto, req.user);
    }
};
exports.QuestionPapersController = QuestionPapersController;
__decorate([
    (0, common_1.Post)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER, role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiTags)('Question Paper Generation'),
    (0, swagger_1.ApiOperation)({
        summary: 'Create question paper (Unified Single/Multi-Subject Generation)',
        description: `
**Unified Question Paper Generation API**

Create single-subject or multi-subject question papers using one endpoint with different field combinations.

**📋 REQUEST PATTERNS:**

**Single Subject Papers:**
- Required: \`title\`, \`subject\`, \`customise\`
- Optional: \`examType\`, \`topicId\`, \`instructions\`, \`description\`

**Multi-Subject Papers:**
- Required: \`title\`, \`subjects\` (min 2), \`duration\`, \`includeAnswers\`
- Optional: \`examType\`, \`instructions\`, \`description\`

**⚠️ VALIDATION RULES:**
- Cannot use both \`subject\` and \`subjects\` fields together
- Multi-subject: minimum 2 subjects, no duplicates allowed
- Difficulty percentages must sum to 100 for each subject
- Question count cannot exceed available unused questions per college

**📊 RESPONSE STRUCTURE:**
- \`questionPaper\`: Complete paper with populated questions and sections
- \`college\`: College information (name, logo, address) when available
- Fields marked as OPTIONAL may not be present depending on paper type

**🔒 ACCESS & LIMITS:**
- Teachers: Create papers for their college only
- Super Admins: Create papers for any college
- Generation limits enforced per college/subject
- Question reuse prevention applied per college
    `,
    }),
    (0, swagger_1.ApiBody)({
        type: create_question_paper_dto_1.CreateQuestionPaperDto,
        description: 'Unified request body for creating single or multi-subject question papers. Use either subject+customise for single-subject OR subjects array for multi-subject papers.',
        schema: {
            type: 'object',
            required: ['title'],
            properties: {
                title: {
                    type: 'string',
                    description: 'Title of the question paper',
                    example: 'NEET Mock Test 2024'
                },
                description: {
                    type: 'string',
                    description: 'Optional description of the question paper',
                    example: 'Comprehensive test covering multiple subjects'
                },
                examType: {
                    type: 'string',
                    enum: ['NEET', 'CET', 'JEE', 'AIIMS', 'JIPMER', 'CUSTOM'],
                    description: 'Optional exam type for categorization',
                    example: 'NEET'
                },
                instructions: {
                    type: 'string',
                    description: 'Optional instructions for the question paper',
                    example: 'Read all questions carefully before answering'
                },
                subject: {
                    type: 'string',
                    description: 'Subject shortcode or ObjectId (REQUIRED for single-subject papers, MUST NOT be used with subjects array)',
                    example: 'physics'
                },
                topicId: {
                    type: 'string',
                    description: 'Optional topic ID for single-subject papers only',
                    example: '60d21b4667d0d8992e610c87'
                },
                totalMarks: {
                    type: 'number',
                    description: 'Total marks for single-subject papers only',
                    example: 100
                },
                duration: {
                    type: 'number',
                    description: 'Duration in minutes (REQUIRED for multi-subject, optional for single-subject)',
                    example: 180
                },
                customise: {
                    type: 'object',
                    description: 'Customization config (REQUIRED for single-subject papers)',
                    properties: {
                        customDifficulty: {
                            type: 'object',
                            properties: {
                                easyPercentage: { type: 'number', example: 30 },
                                mediumPercentage: { type: 'number', example: 50 },
                                hardPercentage: { type: 'number', example: 20 }
                            }
                        },
                        numberOfQuestions: { type: 'number', example: 50 },
                        totalMarks: { type: 'number', example: 100 },
                        duration: { type: 'number', example: 180 },
                        includeAnswers: { type: 'boolean', example: true }
                    }
                },
                subjects: {
                    type: 'array',
                    description: 'Array of subject configurations (REQUIRED for multi-subject papers, minimum 2 subjects, MUST NOT be used with subject field)',
                    minItems: 2,
                    items: {
                        type: 'object',
                        required: ['subject', 'numberOfQuestions', 'totalMarks', 'customDifficulty'],
                        properties: {
                            subject: {
                                type: 'string',
                                enum: ['physics', 'chemistry', 'biology', 'mathematics'],
                                example: 'physics'
                            },
                            numberOfQuestions: {
                                type: 'number',
                                minimum: 1,
                                maximum: 200,
                                example: 45
                            },
                            totalMarks: {
                                type: 'number',
                                minimum: 1,
                                example: 180
                            },
                            topicId: {
                                type: 'string',
                                description: 'Optional topic ID for this subject',
                                example: '60d21b4667d0d8992e610c87'
                            },
                            customDifficulty: {
                                type: 'object',
                                required: ['easyPercentage', 'mediumPercentage', 'hardPercentage'],
                                properties: {
                                    easyPercentage: { type: 'number', minimum: 0, maximum: 100, example: 30 },
                                    mediumPercentage: { type: 'number', minimum: 0, maximum: 100, example: 50 },
                                    hardPercentage: { type: 'number', minimum: 0, maximum: 100, example: 20 }
                                }
                            }
                        }
                    }
                },
                includeAnswers: {
                    type: 'boolean',
                    description: 'Include answers in the paper (REQUIRED for multi-subject papers)',
                    example: true
                }
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Question paper created successfully - unified response for both single and multi-subject papers',
        schema: {
            type: 'object',
            required: ['questionPaper'],
            properties: {
                questionPaper: {
                    type: 'object',
                    required: ['_id', 'title', 'totalMarks', 'duration', 'withAnswers', 'difficultyMode', 'isMultiSubject', 'sections', 'questions', 'generatedBy', 'collegeId', 'status', 'createdAt', 'updatedAt'],
                    properties: {
                        _id: {
                            type: 'string',
                            description: 'Unique identifier of the question paper',
                            example: '60d21b4667d0d8992e610c85'
                        },
                        title: {
                            type: 'string',
                            description: 'Title of the question paper',
                            example: 'NEET Mock Test 2024'
                        },
                        description: {
                            type: 'string',
                            description: 'OPTIONAL - Description of the question paper',
                            example: 'Multi-subject question paper with 3 subjects: physics, chemistry, biology',
                        },
                        subjectId: {
                            type: 'string',
                            description: 'OPTIONAL - Only present for single subject papers',
                            example: '60d21b4667d0d8992e610c86'
                        },
                        topicId: {
                            type: 'string',
                            description: 'OPTIONAL - Only present for single subject papers with topic filtering',
                            example: '60d21b4667d0d8992e610c87'
                        },
                        totalMarks: {
                            type: 'number',
                            description: 'Total marks for the entire paper (sum of all subjects for multi-subject)',
                            example: 720
                        },
                        duration: {
                            type: 'number',
                            description: 'Duration in minutes for the entire paper',
                            example: 180
                        },
                        withAnswers: {
                            type: 'boolean',
                            description: 'Whether answers are included in the paper',
                            example: true
                        },
                        instructions: {
                            type: 'string',
                            description: 'OPTIONAL - Instructions for the question paper',
                            example: 'Read all questions carefully before answering',
                        },
                        examType: {
                            type: 'string',
                            enum: ['NEET', 'CET', 'JEE', 'AIIMS', 'JIPMER', 'CUSTOM'],
                            description: 'OPTIONAL - Exam type for categorization',
                            example: 'NEET'
                        },
                        difficultyMode: {
                            type: 'string',
                            enum: ['auto', 'custom'],
                            description: 'Difficulty mode (always custom in current implementation)',
                            example: 'custom'
                        },
                        isMultiSubject: {
                            type: 'boolean',
                            description: 'Indicates if this is a multi-subject paper',
                            example: true
                        },
                        subjectCount: {
                            type: 'number',
                            description: 'OPTIONAL - Number of subjects (only present for multi-subject papers)',
                            example: 3
                        },
                        subjectBreakdown: {
                            type: 'array',
                            description: 'OPTIONAL - Subject summary (only present for multi-subject papers)',
                            items: {
                                type: 'object',
                                properties: {
                                    subject: { type: 'string', example: 'physics' },
                                    questionCount: { type: 'number', example: 45 },
                                    marks: { type: 'number', example: 180 },
                                },
                            },
                        },
                        questions: {
                            type: 'array',
                            description: 'All questions in the paper with full populated details',
                            items: {
                                type: 'object',
                                required: ['_id', 'content', 'difficulty', 'type', 'marks'],
                                properties: {
                                    _id: {
                                        type: 'string',
                                        description: 'Question ID',
                                        example: '60d21b4667d0d8992e610c88'
                                    },
                                    content: {
                                        type: 'string',
                                        description: 'Question text content',
                                        example: 'What is the derivative of f(x) = x²?'
                                    },
                                    options: {
                                        type: 'array',
                                        items: { type: 'string' },
                                        description: 'OPTIONAL - Multiple choice options',
                                        example: ['2x', 'x', '2', '1']
                                    },
                                    answer: {
                                        type: 'string',
                                        description: 'OPTIONAL - Correct answer (included based on withAnswers flag)',
                                        example: '2x'
                                    },
                                    difficulty: {
                                        type: 'string',
                                        enum: ['easy', 'medium', 'hard'],
                                        description: 'Question difficulty level',
                                        example: 'medium'
                                    },
                                    type: {
                                        type: 'string',
                                        description: 'Question type',
                                        example: 'multiple-choice'
                                    },
                                    marks: {
                                        type: 'number',
                                        description: 'Marks for this question',
                                        example: 4
                                    },
                                    explanation: {
                                        type: 'string',
                                        description: 'OPTIONAL - Explanation for the answer',
                                        example: 'The derivative of x² is 2x using the power rule'
                                    },
                                    imageUrls: {
                                        type: 'array',
                                        items: { type: 'string' },
                                        description: 'OPTIONAL - Array of image URLs for the question',
                                        example: []
                                    },
                                    subjectId: {
                                        type: 'object',
                                        description: 'OPTIONAL - Populated subject information',
                                        properties: {
                                            _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                                            name: { type: 'string', example: 'Physics' },
                                            description: { type: 'string', example: 'Physics subject' }
                                        }
                                    },
                                    topicId: {
                                        type: 'object',
                                        description: 'OPTIONAL - Populated topic information',
                                        properties: {
                                            _id: { type: 'string', example: '60d21b4667d0d8992e610c87' },
                                            name: { type: 'string', example: 'Mechanics' },
                                            description: { type: 'string', example: 'Classical mechanics topics' }
                                        }
                                    }
                                },
                            },
                        },
                        sections: {
                            type: 'array',
                            description: 'Organized sections with questions (each section represents a subject for multi-subject papers)',
                            items: {
                                type: 'object',
                                required: ['name', 'order', 'sectionMarks', 'questions'],
                                properties: {
                                    name: {
                                        type: 'string',
                                        description: 'Section name',
                                        example: 'Section A - Physics'
                                    },
                                    description: {
                                        type: 'string',
                                        description: 'OPTIONAL - Section description',
                                        example: 'Physics Questions (45 questions, 180 marks)'
                                    },
                                    order: {
                                        type: 'number',
                                        description: 'Section order/sequence',
                                        example: 1
                                    },
                                    sectionMarks: {
                                        type: 'number',
                                        description: 'Total marks for this section',
                                        example: 180
                                    },
                                    subjectId: {
                                        type: 'string',
                                        description: 'OPTIONAL - Subject ID (only for multi-subject papers)',
                                        example: '60d21b4667d0d8992e610c86'
                                    },
                                    subjectName: {
                                        type: 'string',
                                        description: 'OPTIONAL - Subject name (only for multi-subject papers)',
                                        example: 'Physics'
                                    },
                                    questions: {
                                        type: 'array',
                                        description: 'Questions in this section with full populated question objects',
                                        items: {
                                            type: 'object',
                                            required: ['questionId', 'order', 'question'],
                                            properties: {
                                                questionId: {
                                                    type: 'string',
                                                    description: 'Reference to question ID',
                                                    example: '60d21b4667d0d8992e610c88'
                                                },
                                                order: {
                                                    type: 'number',
                                                    description: 'Question order within section',
                                                    example: 1
                                                },
                                                question: {
                                                    type: 'object',
                                                    description: 'Full populated question object for easy access',
                                                    required: ['_id', 'content', 'difficulty', 'type', 'marks'],
                                                    properties: {
                                                        _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                                                        content: { type: 'string', example: 'What is the derivative of f(x) = x²?' },
                                                        options: {
                                                            type: 'array',
                                                            items: { type: 'string' },
                                                            description: 'OPTIONAL - Multiple choice options',
                                                            example: ['2x', 'x', '2', '1']
                                                        },
                                                        answer: {
                                                            type: 'string',
                                                            description: 'OPTIONAL - Correct answer (based on withAnswers flag)',
                                                            example: '2x'
                                                        },
                                                        difficulty: {
                                                            type: 'string',
                                                            enum: ['easy', 'medium', 'hard'],
                                                            example: 'medium'
                                                        },
                                                        type: { type: 'string', example: 'multiple-choice' },
                                                        marks: { type: 'number', example: 4 },
                                                        explanation: {
                                                            type: 'string',
                                                            description: 'OPTIONAL - Answer explanation',
                                                            example: 'The derivative of x² is 2x using the power rule'
                                                        },
                                                        imageUrls: {
                                                            type: 'array',
                                                            items: { type: 'string' },
                                                            description: 'OPTIONAL - Question image URLs',
                                                            example: []
                                                        }
                                                    },
                                                },
                                            },
                                        },
                                    },
                                },
                            },
                        },
                        generatedBy: {
                            type: 'string',
                            description: 'ID of user who generated the paper',
                            example: '60d21b4667d0d8992e610c90'
                        },
                        collegeId: {
                            type: 'string',
                            description: 'ID of the college',
                            example: '60d21b4667d0d8992e610c91'
                        },
                        status: {
                            type: 'string',
                            description: 'Paper status',
                            example: 'active'
                        },
                        createdAt: {
                            type: 'string',
                            format: 'date-time',
                            description: 'Creation timestamp',
                            example: '2024-01-15T10:30:00.000Z'
                        },
                        updatedAt: {
                            type: 'string',
                            format: 'date-time',
                            description: 'Last update timestamp',
                            example: '2024-01-15T10:30:00.000Z'
                        },
                    },
                },
                college: {
                    type: 'object',
                    description: 'OPTIONAL - College information (included when available)',
                    properties: {
                        name: {
                            type: 'string',
                            description: 'College name',
                            example: 'Harvard University'
                        },
                        logoUrl: {
                            type: 'string',
                            description: 'OPTIONAL - College logo URL',
                            example: 'https://example.com/logo.png'
                        },
                        address: {
                            type: 'string',
                            description: 'OPTIONAL - College address',
                            example: '123 Main St, Cambridge, MA 02138'
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 400,
        description: 'Bad request - Invalid input or limit exceeded',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 400 },
                message: {
                    type: 'string',
                    example: 'Only custom question paper generation is allowed. Please provide customise configuration.'
                },
            },
            examples: {
                'Missing customise for single subject': {
                    value: {
                        statusCode: 400,
                        message: 'Only custom question paper generation is allowed. Please provide customise configuration.'
                    }
                },
                'Missing duration for multi-subject': {
                    value: {
                        statusCode: 400,
                        message: 'Duration is required for multi-subject papers'
                    }
                },
                'Invalid difficulty percentages': {
                    value: {
                        statusCode: 400,
                        message: 'Difficulty percentages for subject physics must sum to 100'
                    }
                },
                'Insufficient questions': {
                    value: {
                        statusCode: 400,
                        message: 'Only 25 unused questions available for physics. Requested: 50'
                    }
                },
                'Minimum subjects required': {
                    value: {
                        statusCode: 400,
                        message: 'Multi-subject papers must contain at least 2 subjects'
                    }
                },
                'Duplicate subjects': {
                    value: {
                        statusCode: 400,
                        message: 'Duplicate subjects are not allowed in multi-subject papers'
                    }
                },
                'Invalid subject configuration': {
                    value: {
                        statusCode: 400,
                        message: 'Number of questions must be greater than 0 for subject: physics'
                    }
                },
                'Conflicting fields': {
                    value: {
                        statusCode: 400,
                        message: 'Cannot provide both subject and subjects fields. Use subject for single-subject or subjects for multi-subject papers.'
                    }
                }
            }
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 401,
        description: 'Unauthorized - Invalid token',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 401 },
                message: { type: 'string', example: 'Unauthorized' },
            }
        }
    }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
        schema: {
            type: 'object',
            properties: {
                statusCode: { type: 'number', example: 403 },
                message: { type: 'string', example: 'Only teachers and super admins can create question papers.' },
            }
        }
    }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, create_question_paper_dto_1.CreateQuestionPaperDto]),
    __metadata("design:returntype", Promise)
], QuestionPapersController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER, role_enum_1.Role.COLLEGE_ADMIN, role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiTags)('Question Paper Management'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all question papers based on user role',
        description: 'Returns question papers based on user role: Teachers see only their own papers, College Admins see all papers from their college with teacher details, Super Admins see all papers with college-wise grouping.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns all question papers',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    title: { type: 'string', example: 'Mathematics Final Exam 2024' },
                    subjectId: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                            name: { type: 'string', example: 'Mathematics' },
                        },
                    },
                    totalMarks: { type: 'number', example: 100 },
                    duration: { type: 'number', example: 180 },
                    generatedBy: { type: 'string', example: '60d21b4667d0d8992e610c90' },
                    collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
                    status: { type: 'string', example: 'active' },
                    createdAt: { type: 'string', format: 'date-time' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], QuestionPapersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER, role_enum_1.Role.COLLEGE_ADMIN, role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiTags)('Question Paper Management'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get a specific question paper by ID',
        description: 'Returns a specific question paper by ID with role-based access: Teachers can only access their own papers, College Admins can access papers from their college, Super Admins can access any paper.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Question paper ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns the question paper',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                title: { type: 'string', example: 'Mathematics Final Exam 2024' },
                description: {
                    type: 'string',
                    example: 'Final examination for Mathematics course',
                },
                subjectId: {
                    type: 'object',
                    properties: {
                        _id: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                        name: { type: 'string', example: 'Mathematics' },
                    },
                },
                topicId: {
                    type: 'object',
                    properties: {
                        _id: { type: 'string', example: '60d21b4667d0d8992e610c87' },
                        name: { type: 'string', example: 'Calculus' },
                    },
                },
                totalMarks: { type: 'number', example: 100 },
                duration: { type: 'number', example: 180 },
                instructions: {
                    type: 'string',
                    example: 'Answer all questions. Each question carries equal marks.',
                },
                questions: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                            content: {
                                type: 'string',
                                example: 'What is the derivative of f(x) = x²?',
                            },
                            options: {
                                type: 'array',
                                items: { type: 'string' },
                                example: ['2x', 'x', '2', '1'],
                            },
                            answer: { type: 'string', example: '2x' },
                            difficulty: { type: 'string', example: 'medium' },
                        },
                    },
                },
                generatedBy: { type: 'string', example: '60d21b4667d0d8992e610c90' },
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
                status: { type: 'string', example: 'active' },
                sections: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            name: { type: 'string', example: 'Section A' },
                            description: {
                                type: 'string',
                                example: 'Multiple Choice Questions',
                            },
                            order: { type: 'number', example: 1 },
                            sectionMarks: { type: 'number', example: 50 },
                            questions: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        question: {
                                            type: 'object',
                                            properties: {
                                                _id: { type: 'string', example: '60d21b4667d0d8992e610c88' },
                                                content: { type: 'string', example: 'What is the derivative of f(x) = x²?' },
                                                options: { type: 'array', items: { type: 'string' }, example: ['2x', 'x', '2', '1'] },
                                                answer: { type: 'string', example: '2x' },
                                                difficulty: { type: 'string', example: 'medium' },
                                                type: { type: 'string', example: 'multiple-choice' },
                                                marks: { type: 'number', example: 4 },
                                            },
                                        },
                                        order: { type: 'number', example: 1 },
                                    },
                                },
                            },
                        },
                    },
                },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question paper not found' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], QuestionPapersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)(':id/download'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER, role_enum_1.Role.COLLEGE_ADMIN, role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiTags)('Question Paper Download'),
    (0, swagger_1.ApiOperation)({
        summary: 'Download a question paper in PDF or DOCX format',
        description: 'Download question papers with role-based access and download limits enforcement. Teachers can download their own papers, College Admins can download papers from their college, Super Admins can download any paper.',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Question paper ID' }),
    (0, swagger_1.ApiQuery)({
        name: 'format',
        enum: ['pdf', 'docx'],
        required: false,
        description: 'File format',
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns the file for download' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid format' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question paper not found' }),
    (0, swagger_1.ApiResponse)({ status: 500, description: 'Internal server error' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('format')),
    __param(2, (0, common_1.Req)()),
    __param(3, (0, common_1.Res)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Object, Object]),
    __metadata("design:returntype", Promise)
], QuestionPapersController.prototype, "download", null);
__decorate([
    (0, common_1.Post)('limits'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.SUPER_ADMIN),
    (0, swagger_1.ApiTags)('Question Paper Administration'),
    (0, swagger_1.ApiOperation)({
        summary: 'Set question paper generation and download limits for a college',
        description: 'Sets the maximum number of question papers that can be generated and downloaded per college. Only super admins can set these limits.',
    }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Limit set successfully',
        schema: {
            type: 'object',
            properties: {
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c91' },
                subjectId: { type: 'string', example: '60d21b4667d0d8992e610c86' },
                maxQuestions: { type: 'number', example: 50 },
                message: { type: 'string', example: 'Question limit set successfully' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Not found - College or subject not found',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [set_question_limit_dto_1.SetQuestionLimitDto]),
    __metadata("design:returntype", void 0)
], QuestionPapersController.prototype, "setQuestionLimit", null);
__decorate([
    (0, common_1.Patch)(':id'),
    (0, roles_decorator_1.Roles)(role_enum_1.Role.TEACHER),
    (0, swagger_1.ApiTags)('Question Paper Management'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update a question paper',
        description: 'Updates an existing question paper. Teachers can only update their own question papers.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Question paper ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Question paper updated successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                title: {
                    type: 'string',
                    example: 'Mathematics Final Exam 2024 (Updated)',
                },
                description: {
                    type: 'string',
                    example: 'Updated final examination for Mathematics course',
                },
                instructions: {
                    type: 'string',
                    example: 'Updated instructions. Answer all questions.',
                },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions or not the owner',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Question paper not found' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Param)('id')),
    __param(2, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String, update_question_paper_dto_1.UpdateQuestionPaperDto]),
    __metadata("design:returntype", void 0)
], QuestionPapersController.prototype, "update", null);
exports.QuestionPapersController = QuestionPapersController = QuestionPapersController_1 = __decorate([
    (0, swagger_1.ApiTags)('Question Papers'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('question-papers'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [question_papers_service_1.QuestionPapersService,
        services_1.TrackingService])
], QuestionPapersController);
//# sourceMappingURL=question-papers.controller.js.map