"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollegesService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const college_schema_1 = require("../schema/college.schema");
let CollegesService = class CollegesService {
    constructor(collegeModel) {
        this.collegeModel = collegeModel;
    }
    async create(createCollegeDto) {
        const collegeData = {
            ...createCollegeDto,
            contactPhone: createCollegeDto.contactPhone || createCollegeDto.contact,
            contactEmail: createCollegeDto.contactEmail || createCollegeDto.email,
            logoUrl: createCollegeDto.logoUrl || createCollegeDto.logoURL,
        };
        if (collegeData.contact)
            delete collegeData.contact;
        if (collegeData.email)
            delete collegeData.email;
        if (collegeData.logoURL)
            delete collegeData.logoURL;
        const adminsArray = createCollegeDto.admins ? [...createCollegeDto.admins] : [];
        if (collegeData.contactEmail && !adminsArray.includes(collegeData.contactEmail)) {
            adminsArray.push(collegeData.contactEmail);
        }
        collegeData.admins = adminsArray;
        const createdCollege = new this.collegeModel(collegeData);
        return createdCollege.save();
    }
    async findAll() {
        return this.collegeModel.find().exec();
    }
    async findOne(id) {
        const college = await this.collegeModel.findById(id).exec();
        if (!college) {
            throw new common_1.NotFoundException(`College with ID ${id} not found`);
        }
        return college;
    }
    async update(id, updateCollegeDto) {
        const collegeData = {
            ...updateCollegeDto,
            contactPhone: updateCollegeDto.contactPhone || updateCollegeDto.contact,
            contactEmail: updateCollegeDto.contactEmail || updateCollegeDto.email,
            logoUrl: updateCollegeDto.logoUrl || updateCollegeDto.logoURL,
        };
        if (collegeData.contact)
            delete collegeData.contact;
        if (collegeData.email)
            delete collegeData.email;
        if (collegeData.logoURL)
            delete collegeData.logoURL;
        const updatedCollege = await this.collegeModel
            .findByIdAndUpdate(id, collegeData, { new: true })
            .exec();
        if (!updatedCollege) {
            throw new common_1.NotFoundException(`College with ID ${id} not found`);
        }
        return updatedCollege;
    }
    async remove(id) {
        const deletedCollege = await this.collegeModel.findByIdAndDelete(id).exec();
        if (!deletedCollege) {
            throw new common_1.NotFoundException(`College with ID ${id} not found`);
        }
        return deletedCollege;
    }
    async findAllPublic() {
        const colleges = await this.collegeModel
            .find({}, { name: 1, logoUrl: 1 })
            .exec();
        return colleges.map((college) => ({
            _id: college._id.toString(),
            name: college.name,
            logoUrl: college.logoUrl,
        }));
    }
};
exports.CollegesService = CollegesService;
exports.CollegesService = CollegesService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(college_schema_1.College.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], CollegesService);
//# sourceMappingURL=colleges.service.js.map