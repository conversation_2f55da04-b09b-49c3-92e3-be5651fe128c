"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeachersController = void 0;
const common_1 = require("@nestjs/common");
const teachers_service_1 = require("./teachers.service");
const create_teacher_dto_1 = require("./dto/create-teacher.dto");
const update_teacher_profile_dto_1 = require("./dto/update-teacher-profile.dto");
const update_teacher_admin_dto_1 = require("./dto/update-teacher-admin.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/decorators/roles.decorator");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const services_1 = require("../common/services");
let TeachersController = class TeachersController {
    constructor(teachersService, trackingService) {
        this.teachersService = teachersService;
        this.trackingService = trackingService;
    }
    addTeacherToCollege(collegeId, createTeacherDto) {
        return this.teachersService.addTeacherToCollege(collegeId, createTeacherDto);
    }
    findAllTeachersInCollege(collegeId) {
        return this.teachersService.findAllTeachersInCollege(collegeId);
    }
    findOne(id) {
        return this.teachersService.findOne(id);
    }
    updateOwnProfile(user, updateTeacherProfileDto) {
        const userId = user._id || user.userId;
        return this.teachersService.update(userId, updateTeacherProfileDto);
    }
    update(id, updateTeacherAdminDto) {
        return this.teachersService.update(id, updateTeacherAdminDto);
    }
    remove(id) {
        return this.teachersService.remove(id);
    }
    async getTeacherAnalytics(id, startDate, endDate, subjectId) {
        const filters = {};
        if (startDate) {
            filters.startDate = new Date(startDate);
        }
        if (endDate) {
            filters.endDate = new Date(endDate);
        }
        if (subjectId) {
            filters.subjectId = subjectId;
        }
        const [downloads, paperGeneration] = await Promise.all([
            this.trackingService.getTeacherDownloadStats(id, filters),
            this.trackingService.getTeacherPaperGenerationStats(id, filters),
        ]);
        return {
            teacherId: id,
            downloads,
            paperGeneration,
        };
    }
};
exports.TeachersController = TeachersController;
__decorate([
    (0, common_1.Post)('colleges/:collegeId/teachers'),
    (0, roles_decorator_1.Roles)('collegeAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Add a teacher to a college',
        description: 'Adds a new teacher to a specific college (college admin only). Only name and email are required - phone, department, designation, and profile image are optional.',
    }),
    (0, swagger_1.ApiParam)({
        name: 'collegeId',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiBody)({ type: create_teacher_dto_1.CreateTeacherDto }),
    (0, swagger_1.ApiCreatedResponse)({
        description: 'Teacher added successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                email: { type: 'string', example: '<EMAIL>' },
                displayName: { type: 'string', example: 'John Doe' },
                firstName: { type: 'string', example: 'John' },
                lastName: { type: 'string', example: 'Doe' },
                phone: { type: 'string', example: '+****************', nullable: true },
                role: { type: 'string', example: 'teacher' },
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                status: { type: 'string', example: 'active' },
                department: {
                    type: 'string',
                    example: 'Computer Science',
                    nullable: true,
                },
                designation: {
                    type: 'string',
                    example: 'Associate Professor',
                    nullable: true,
                },
                profileImageUrl: {
                    type: 'string',
                    example: 'https://example.com/images/teacher-profile.jpg',
                    nullable: true,
                },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('collegeId')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, create_teacher_dto_1.CreateTeacherDto]),
    __metadata("design:returntype", void 0)
], TeachersController.prototype, "addTeacherToCollege", null);
__decorate([
    (0, common_1.Get)('colleges/:collegeId/teachers'),
    (0, roles_decorator_1.Roles)('collegeAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all teachers in a college',
        description: 'Returns all teachers associated with a specific college (college admin only)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'collegeId',
        description: 'College ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns all teachers in the college',
        schema: {
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    email: { type: 'string', example: '<EMAIL>' },
                    displayName: { type: 'string', example: 'John Doe' },
                    firstName: { type: 'string', example: 'John' },
                    lastName: { type: 'string', example: 'Doe' },
                    phone: { type: 'string', example: '+****************' },
                    role: { type: 'string', example: 'teacher' },
                    collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                    status: { type: 'string', example: 'active' },
                    department: { type: 'string', example: 'Computer Science' },
                    designation: { type: 'string', example: 'Associate Professor' },
                    profileImageUrl: {
                        type: 'string',
                        example: 'https://example.com/images/teacher-profile.jpg',
                    },
                    createdAt: { type: 'string', format: 'date-time' },
                    updatedAt: { type: 'string', format: 'date-time' },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'College not found' }),
    __param(0, (0, common_1.Param)('collegeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TeachersController.prototype, "findAllTeachersInCollege", null);
__decorate([
    (0, common_1.Get)('teachers/:id'),
    (0, roles_decorator_1.Roles)('collegeAdmin', 'teacher'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get a teacher by ID',
        description: 'Returns a teacher by ID (college admin or teacher can access)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Teacher ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Returns the teacher',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                email: { type: 'string', example: '<EMAIL>' },
                displayName: { type: 'string', example: 'John Doe' },
                firstName: { type: 'string', example: 'John' },
                lastName: { type: 'string', example: 'Doe' },
                phone: { type: 'string', example: '+****************' },
                role: { type: 'string', example: 'teacher' },
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                status: { type: 'string', example: 'active' },
                department: { type: 'string', example: 'Computer Science' },
                designation: { type: 'string', example: 'Associate Professor' },
                profileImageUrl: {
                    type: 'string',
                    example: 'https://example.com/images/teacher-profile.jpg',
                },
                createdAt: { type: 'string', format: 'date-time' },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Teacher not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TeachersController.prototype, "findOne", null);
__decorate([
    (0, common_1.Put)('teachers/me'),
    (0, roles_decorator_1.Roles)('teacher'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update own profile',
        description: 'Allows teachers to update their own profile information (excludes status, designation, department, and timestamps)',
    }),
    (0, swagger_1.ApiBody)({ type: update_teacher_profile_dto_1.UpdateTeacherProfileDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Profile updated successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                email: { type: 'string', example: '<EMAIL>' },
                displayName: { type: 'string', example: 'John Doe' },
                firstName: { type: 'string', example: 'John' },
                lastName: { type: 'string', example: 'Doe' },
                phone: { type: 'string', example: '+****************' },
                role: { type: 'string', example: 'teacher' },
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                status: { type: 'string', example: 'active' },
                department: { type: 'string', example: 'Computer Science' },
                designation: { type: 'string', example: 'Associate Professor' },
                profileImageUrl: {
                    type: 'string',
                    example: 'https://example.com/images/teacher-profile.jpg',
                },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Teacher not found' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, update_teacher_profile_dto_1.UpdateTeacherProfileDto]),
    __metadata("design:returntype", void 0)
], TeachersController.prototype, "updateOwnProfile", null);
__decorate([
    (0, common_1.Put)('teachers/:id'),
    (0, roles_decorator_1.Roles)('collegeAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Update a teacher',
        description: "Updates a teacher's administrative information (college admin only) - phone, department, designation, status, role",
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Teacher ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiBody)({ type: update_teacher_admin_dto_1.UpdateTeacherAdminDto }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Teacher updated successfully',
        schema: {
            type: 'object',
            properties: {
                _id: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                email: { type: 'string', example: '<EMAIL>' },
                displayName: { type: 'string', example: 'John Doe' },
                firstName: { type: 'string', example: 'John' },
                lastName: { type: 'string', example: 'Doe' },
                phone: { type: 'string', example: '+****************' },
                role: { type: 'string', example: 'teacher' },
                collegeId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                status: { type: 'string', example: 'active' },
                department: { type: 'string', example: 'Computer Science' },
                designation: { type: 'string', example: 'Associate Professor' },
                profileImageUrl: {
                    type: 'string',
                    example: 'https://example.com/images/teacher-profile.jpg',
                },
                updatedAt: { type: 'string', format: 'date-time' },
            },
        },
    }),
    (0, swagger_1.ApiBadRequestResponse)({ description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Teacher not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_teacher_admin_dto_1.UpdateTeacherAdminDto]),
    __metadata("design:returntype", void 0)
], TeachersController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)('teachers/:id'),
    (0, roles_decorator_1.Roles)('collegeAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Remove a teacher',
        description: 'Removes a teacher from a college (college admin only)',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Teacher ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Teacher removed successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Teacher removed successfully' },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Teacher not found' }),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], TeachersController.prototype, "remove", null);
__decorate([
    (0, common_1.Get)('teachers/:id/analytics'),
    (0, roles_decorator_1.Roles)('collegeAdmin', 'teacher'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get teacher analytics',
        description: 'Returns analytics for a specific teacher including download and paper generation statistics',
    }),
    (0, swagger_1.ApiParam)({
        name: 'id',
        description: 'Teacher ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'startDate',
        description: 'Start date for filtering (ISO string)',
        required: false,
        example: '2024-01-01T00:00:00.000Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'endDate',
        description: 'End date for filtering (ISO string)',
        required: false,
        example: '2024-12-31T23:59:59.999Z',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'subjectId',
        description: 'Subject ID for filtering',
        required: false,
        example: '60d21b4667d0d8992e610c86',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Teacher analytics retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                teacherId: { type: 'string', example: '60d21b4667d0d8992e610c85' },
                downloads: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            subjectId: {
                                type: 'string',
                                example: '60d21b4667d0d8992e610c86',
                            },
                            subjectName: { type: 'string', example: 'Mathematics' },
                            totalDownloads: { type: 'number', example: 25 },
                            formatBreakdown: {
                                type: 'array',
                                items: {
                                    type: 'object',
                                    properties: {
                                        format: { type: 'string', example: 'pdf' },
                                        count: { type: 'number', example: 15 },
                                    },
                                },
                            },
                            lastDownload: { type: 'string', format: 'date-time' },
                        },
                    },
                },
                paperGeneration: {
                    type: 'array',
                    items: {
                        type: 'object',
                        properties: {
                            subjectId: {
                                type: 'string',
                                example: '60d21b4667d0d8992e610c86',
                            },
                            subjectName: { type: 'string', example: 'Mathematics' },
                            totalPapers: { type: 'number', example: 12 },
                            lastGenerated: { type: 'string', format: 'date-time' },
                        },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({ description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiForbiddenResponse)({ description: 'Forbidden - Insufficient permissions' }),
    (0, swagger_1.ApiNotFoundResponse)({ description: 'Teacher not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('startDate')),
    __param(2, (0, common_1.Query)('endDate')),
    __param(3, (0, common_1.Query)('subjectId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String]),
    __metadata("design:returntype", Promise)
], TeachersController.prototype, "getTeacherAnalytics", null);
exports.TeachersController = TeachersController = __decorate([
    (0, swagger_1.ApiTags)('Teachers'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [teachers_service_1.TeachersService,
        services_1.TrackingService])
], TeachersController);
//# sourceMappingURL=teachers.controller.js.map