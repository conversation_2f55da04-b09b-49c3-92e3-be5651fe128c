"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FilterQuestionsDto = exports.Difficulty = exports.QuestionType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var QuestionType;
(function (QuestionType) {
    QuestionType["MCQ"] = "mcq";
    QuestionType["MULTIPLE_CHOICE"] = "multiple-choice";
    QuestionType["TRUE_FALSE"] = "true-false";
    QuestionType["DESCRIPTIVE"] = "descriptive";
})(QuestionType || (exports.QuestionType = QuestionType = {}));
var Difficulty;
(function (Difficulty) {
    Difficulty["EASY"] = "easy";
    Difficulty["MEDIUM"] = "medium";
    Difficulty["HARD"] = "hard";
})(Difficulty || (exports.Difficulty = Difficulty = {}));
class FilterQuestionsDto {
}
exports.FilterQuestionsDto = FilterQuestionsDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by subject ID',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, class_validator_1.ValidateIf)((o) => o.topicId),
    (0, class_validator_1.IsMongoId)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FilterQuestionsDto.prototype, "subjectId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by topic ID',
        example: '60d21b4667d0d8992e610c86',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsMongoId)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FilterQuestionsDto.prototype, "topicId", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by difficulty level',
        example: 'medium',
        enum: Difficulty,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(Difficulty),
    __metadata("design:type", String)
], FilterQuestionsDto.prototype, "difficulty", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by question type',
        example: 'multiple-choice',
        enum: QuestionType,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(QuestionType),
    __metadata("design:type", String)
], FilterQuestionsDto.prototype, "type", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Search in question content',
        example: 'calculus',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], FilterQuestionsDto.prototype, "search", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Filter by review status',
        example: 'pending',
        enum: ['pending', 'approved', 'rejected'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['pending', 'approved', 'rejected']),
    __metadata("design:type", String)
], FilterQuestionsDto.prototype, "reviewStatus", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Page number for pagination',
        example: 1,
        minimum: 1,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], FilterQuestionsDto.prototype, "page", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Number of items per page',
        example: 20,
        minimum: 1,
        maximum: 100,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", Number)
], FilterQuestionsDto.prototype, "limit", void 0);
//# sourceMappingURL=filter-questions.dto.js.map