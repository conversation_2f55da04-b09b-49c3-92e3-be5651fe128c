"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeachersService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const user_schema_1 = require("../schema/user.schema");
const college_schema_1 = require("../schema/college.schema");
let TeachersService = class TeachersService {
    constructor(userModel, collegeModel) {
        this.userModel = userModel;
        this.collegeModel = collegeModel;
    }
    async addTeacherToCollege(collegeId, createTeacherDto) {
        const college = await this.collegeModel.findById(collegeId).exec();
        if (!college) {
            throw new common_1.NotFoundException(`College with ID ${collegeId} not found`);
        }
        const existingTeacher = await this.userModel
            .findOne({ email: createTeacherDto.email })
            .exec();
        if (existingTeacher) {
            throw new common_1.BadRequestException(`User with email ${createTeacherDto.email} already exists`);
        }
        const { name, ...otherFields } = createTeacherDto;
        const nameParts = name.trim().split(' ');
        const firstName = nameParts[0] || '';
        const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
        const teacher = new this.userModel({
            ...otherFields,
            displayName: name,
            firstName: firstName,
            lastName: lastName,
            role: 'teacher',
            collegeId: collegeId,
        });
        const savedTeacher = await teacher.save();
        if (!college.teachers.includes(createTeacherDto.email)) {
            college.teachers.push(createTeacherDto.email);
            await college.save();
        }
        await savedTeacher.populate('collegeId');
        return savedTeacher;
    }
    async findAllTeachersInCollege(collegeId) {
        const college = await this.collegeModel.findById(collegeId).exec();
        if (!college) {
            throw new common_1.NotFoundException(`College with ID ${collegeId} not found`);
        }
        return this.userModel
            .find({ collegeId, role: 'teacher' })
            .populate('collegeId')
            .exec();
    }
    async findOne(id) {
        const teacher = await this.userModel
            .findOne({ _id: id, role: 'teacher' })
            .populate('collegeId')
            .exec();
        if (!teacher) {
            throw new common_1.NotFoundException(`Teacher with ID ${id} not found`);
        }
        return teacher;
    }
    async update(id, updateTeacherDto) {
        const teacher = await this.userModel
            .findOne({ _id: id, role: 'teacher' })
            .exec();
        if (!teacher) {
            throw new common_1.NotFoundException(`Teacher with ID ${id} not found`);
        }
        if (updateTeacherDto.email && updateTeacherDto.email !== teacher.email) {
            const existingTeacher = await this.userModel
                .findOne({ email: updateTeacherDto.email })
                .exec();
            if (existingTeacher) {
                throw new common_1.BadRequestException(`User with email ${updateTeacherDto.email} already exists`);
            }
            if (teacher.collegeId) {
                const college = await this.collegeModel
                    .findById(teacher.collegeId)
                    .exec();
                if (college) {
                    college.teachers = college.teachers.filter((email) => email !== teacher.email);
                    if (!college.teachers.includes(updateTeacherDto.email)) {
                        college.teachers.push(updateTeacherDto.email);
                    }
                    await college.save();
                }
            }
        }
        let updateData = { ...updateTeacherDto };
        if (updateTeacherDto.name) {
            const { name, ...otherFields } = updateTeacherDto;
            const nameParts = name.trim().split(' ');
            const firstName = nameParts[0] || '';
            const lastName = nameParts.length > 1 ? nameParts.slice(1).join(' ') : '';
            updateData = {
                ...otherFields,
                displayName: name,
                firstName: firstName,
                lastName: lastName,
            };
        }
        const updatedTeacher = await this.userModel
            .findByIdAndUpdate(id, updateData, { new: true })
            .populate('collegeId')
            .exec();
        if (!updatedTeacher) {
            throw new common_1.NotFoundException(`Teacher with ID ${id} not found`);
        }
        return updatedTeacher;
    }
    async remove(id) {
        const teacher = await this.userModel
            .findOne({ _id: id, role: 'teacher' })
            .populate('collegeId')
            .exec();
        if (!teacher) {
            throw new common_1.NotFoundException(`Teacher with ID ${id} not found`);
        }
        if (teacher.collegeId) {
            const college = await this.collegeModel
                .findById(teacher.collegeId)
                .exec();
            if (college && college.teachers.includes(teacher.email)) {
                college.teachers = college.teachers.filter((email) => email !== teacher.email);
                await college.save();
            }
        }
        const deletedTeacher = await this.userModel
            .findByIdAndDelete(id)
            .populate('collegeId')
            .exec();
        if (!deletedTeacher) {
            throw new common_1.NotFoundException(`Teacher with ID ${id} not found`);
        }
        return deletedTeacher;
    }
};
exports.TeachersService = TeachersService;
exports.TeachersService = TeachersService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(user_schema_1.User.name)),
    __param(1, (0, mongoose_1.InjectModel)(college_schema_1.College.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], TeachersService);
//# sourceMappingURL=teachers.service.js.map