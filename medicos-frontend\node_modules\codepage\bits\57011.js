if(typeof cptable === 'undefined') cptable = {};
cptable[57011] = (function(){ var d = [], e = {}, D = [], j;
D[0] = "\u0000\u0001\u0002\u0003\u0004\u0005\u0006\u0007\b\t\n\u000b\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u0015\u0016\u0017\u0018\u0019\u001a\u001b\u001c\u001d\u001e\u001f !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~��ਂ�ਅਆਇਈਉਊ�ਏਏਐਐਐਓਔਔਕਖਗਘਙਚਛਜਝਞਟਠਡਢਣਤਥਦਧਨਨਪਫਬਭਮਯਯਰਰਲਲ਼ਲ਼ਵਸ਼ਸ਼ਸਹ�ਾਿੀੁੂ�ੇੇੈੈੋੋੌੌ਼੍.������੦੧੨੩੪੫੬੭੮੯�����".split("");
for(j = 0; j != D[0].length; ++j) if(D[0][j].charCodeAt(0) !== 0xFFFD) { e[D[0][j]] = 0 + j; d[0 + j] = D[0][j];}
D[180] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ਖ਼����������������������".split("");
for(j = 0; j != D[180].length; ++j) if(D[180][j].charCodeAt(0) !== 0xFFFD) { e[D[180][j]] = 46080 + j; d[46080 + j] = D[180][j];}
D[181] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ਗ਼����������������������".split("");
for(j = 0; j != D[181].length; ++j) if(D[181][j].charCodeAt(0) !== 0xFFFD) { e[D[181][j]] = 46336 + j; d[46336 + j] = D[181][j];}
D[186] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ਜ਼����������������������".split("");
for(j = 0; j != D[186].length; ++j) if(D[186][j].charCodeAt(0) !== 0xFFFD) { e[D[186][j]] = 47616 + j; d[47616 + j] = D[186][j];}
D[192] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ੜ����������������������".split("");
for(j = 0; j != D[192].length; ++j) if(D[192][j].charCodeAt(0) !== 0xFFFD) { e[D[192][j]] = 49152 + j; d[49152 + j] = D[192][j];}
D[201] = "�����������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������ਫ਼����������������������".split("");
for(j = 0; j != D[201].length; ++j) if(D[201][j].charCodeAt(0) !== 0xFFFD) { e[D[201][j]] = 51456 + j; d[51456 + j] = D[201][j];}
D[239] = "����������������������������������������������������������������੯੯੯੯੯੯੯੯੯੯੯੯������������������������������������������������������������������������������������������������������������������������������������������������������������������������������������".split("");
for(j = 0; j != D[239].length; ++j) if(D[239][j].charCodeAt(0) !== 0xFFFD) { e[D[239][j]] = 61184 + j; d[61184 + j] = D[239][j];}
return {"enc": e, "dec": d }; })();
