"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var TopicsService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TopicsService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const topic_schema_1 = require("../schema/topic.schema");
const subject_schema_1 = require("../schema/subject.schema");
let TopicsService = TopicsService_1 = class TopicsService {
    constructor(topicModel, subjectModel) {
        this.topicModel = topicModel;
        this.subjectModel = subjectModel;
        this.logger = new common_1.Logger(TopicsService_1.name);
    }
    async create(createTopicDto) {
        try {
            const subject = await this.subjectModel.findById(createTopicDto.subjectId);
            if (!subject) {
                throw new common_1.NotFoundException(`Subject with ID ${createTopicDto.subjectId} not found`);
            }
            const existingTopic = await this.topicModel.findOne({
                subjectId: createTopicDto.subjectId,
                name: { $regex: new RegExp(`^${createTopicDto.name}$`, 'i') },
            });
            if (existingTopic) {
                throw new common_1.ConflictException(`Topic with name '${createTopicDto.name}' already exists in subject '${subject.name}'`);
            }
            const createdTopic = new this.topicModel(createTopicDto);
            const savedTopic = await createdTopic.save();
            this.logger.log(`Topic created: ${savedTopic.name} in subject ${subject.name} (ID: ${savedTopic._id})`);
            return savedTopic;
        }
        catch (error) {
            this.logger.error(`Failed to create topic: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findAll() {
        try {
            return await this.topicModel
                .find()
                .populate('subjectId', 'name description')
                .sort({ name: 1 })
                .exec();
        }
        catch (error) {
            this.logger.error(`Failed to fetch topics: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findBySubject(subjectId) {
        try {
            const subject = await this.subjectModel.findById(subjectId);
            if (!subject) {
                throw new common_1.NotFoundException(`Subject with ID ${subjectId} not found`);
            }
            return await this.topicModel.find({ subjectId }).sort({ name: 1 }).exec();
        }
        catch (error) {
            this.logger.error(`Failed to fetch topics for subject: ${error.message}`, error.stack);
            throw error;
        }
    }
    async findOne(id) {
        try {
            const topic = await this.topicModel
                .findById(id)
                .populate('subjectId', 'name description')
                .exec();
            if (!topic) {
                throw new common_1.NotFoundException(`Topic with ID ${id} not found`);
            }
            return topic;
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                throw error;
            }
            this.logger.error(`Failed to fetch topic: ${error.message}`, error.stack);
            throw error;
        }
    }
    async update(id, updateTopicDto) {
        try {
            const existingTopic = await this.findOne(id);
            if (updateTopicDto.subjectId &&
                updateTopicDto.subjectId !== existingTopic.subjectId.toString()) {
                const subject = await this.subjectModel.findById(updateTopicDto.subjectId);
                if (!subject) {
                    throw new common_1.NotFoundException(`Subject with ID ${updateTopicDto.subjectId} not found`);
                }
            }
            if (updateTopicDto.name && updateTopicDto.name !== existingTopic.name) {
                const subjectId = updateTopicDto.subjectId || existingTopic.subjectId;
                const conflictingTopic = await this.topicModel.findOne({
                    _id: { $ne: id },
                    subjectId: subjectId,
                    name: { $regex: new RegExp(`^${updateTopicDto.name}$`, 'i') },
                });
                if (conflictingTopic) {
                    throw new common_1.ConflictException(`Topic with name '${updateTopicDto.name}' already exists in this subject`);
                }
            }
            const updatedTopic = await this.topicModel
                .findByIdAndUpdate(id, updateTopicDto, {
                new: true,
                runValidators: true,
            })
                .populate('subjectId', 'name description')
                .exec();
            if (!updatedTopic) {
                throw new common_1.NotFoundException(`Topic with ID ${id} not found`);
            }
            this.logger.log(`Topic updated: ${updatedTopic.name} (ID: ${id})`);
            return updatedTopic;
        }
        catch (error) {
            this.logger.error(`Failed to update topic: ${error.message}`, error.stack);
            throw error;
        }
    }
    async remove(id) {
        try {
            await this.findOne(id);
            await this.topicModel.findByIdAndDelete(id).exec();
            this.logger.log(`Topic deleted: ID ${id}`);
        }
        catch (error) {
            this.logger.error(`Failed to delete topic: ${error.message}`, error.stack);
            throw error;
        }
    }
};
exports.TopicsService = TopicsService;
exports.TopicsService = TopicsService = TopicsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(topic_schema_1.Topic.name)),
    __param(1, (0, mongoose_1.InjectModel)(subject_schema_1.Subject.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], TopicsService);
//# sourceMappingURL=topics.service.js.map