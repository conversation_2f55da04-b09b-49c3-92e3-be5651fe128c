"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DownloadSchema = exports.Download = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const question_paper_schema_1 = require("./question-paper.schema");
const user_schema_1 = require("./user.schema");
const college_schema_1 = require("./college.schema");
let Download = class Download {
};
exports.Download = Download;
__decorate([
    (0, mongoose_1.Prop)({
        type: mongoose_2.Schema.Types.ObjectId,
        ref: 'QuestionPaper',
        required: true,
    }),
    __metadata("design:type", question_paper_schema_1.QuestionPaper)
], Download.prototype, "paperId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", user_schema_1.User)
], Download.prototype, "userId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.ObjectId, ref: 'College' }),
    __metadata("design:type", college_schema_1.College)
], Download.prototype, "collegeId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Date)
], Download.prototype, "downloadDate", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, enum: ['pdf', 'docx'] }),
    __metadata("design:type", String)
], Download.prototype, "downloadFormat", void 0);
exports.Download = Download = __decorate([
    (0, mongoose_1.Schema)()
], Download);
exports.DownloadSchema = mongoose_1.SchemaFactory.createForClass(Download);
//# sourceMappingURL=download.schema.js.map