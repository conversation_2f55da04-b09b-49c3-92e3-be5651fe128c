"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TeacherGeneratePaperDto = exports.CustomDifficultyConfig = exports.DifficultyMode = exports.SubjectShortCode = exports.ExamType = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
var ExamType;
(function (ExamType) {
    ExamType["NEET"] = "NEET";
    ExamType["CET"] = "CET";
    ExamType["JEE"] = "JEE";
    ExamType["AIIMS"] = "AIIMS";
    ExamType["JIPMER"] = "JIPMER";
    ExamType["CUSTOM"] = "CUSTOM";
})(ExamType || (exports.ExamType = ExamType = {}));
var SubjectShortCode;
(function (SubjectShortCode) {
    SubjectShortCode["PHYSICS"] = "physics";
    SubjectShortCode["CHEMISTRY"] = "chemistry";
    SubjectShortCode["BIOLOGY"] = "biology";
    SubjectShortCode["MATHEMATICS"] = "mathematics";
    SubjectShortCode["MATH"] = "math";
    SubjectShortCode["PHY"] = "phy";
    SubjectShortCode["CHEM"] = "chem";
    SubjectShortCode["BIO"] = "bio";
})(SubjectShortCode || (exports.SubjectShortCode = SubjectShortCode = {}));
var DifficultyMode;
(function (DifficultyMode) {
    DifficultyMode["AUTO"] = "auto";
    DifficultyMode["CUSTOM"] = "custom";
})(DifficultyMode || (exports.DifficultyMode = DifficultyMode = {}));
class CustomDifficultyConfig {
}
exports.CustomDifficultyConfig = CustomDifficultyConfig;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of easy questions',
        minimum: 0,
        maximum: 100,
        example: 30,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CustomDifficultyConfig.prototype, "easyPercentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of medium questions',
        minimum: 0,
        maximum: 100,
        example: 50,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CustomDifficultyConfig.prototype, "mediumPercentage", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Percentage of hard questions',
        minimum: 0,
        maximum: 100,
        example: 20,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(0),
    (0, class_validator_1.Max)(100),
    __metadata("design:type", Number)
], CustomDifficultyConfig.prototype, "hardPercentage", void 0);
class TeacherGeneratePaperDto {
}
exports.TeacherGeneratePaperDto = TeacherGeneratePaperDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Question paper title',
        example: 'NEET Physics Mock Test 2024',
    }),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TeacherGeneratePaperDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Exam type',
        enum: ExamType,
        example: ExamType.NEET,
    }),
    (0, class_validator_1.IsEnum)(ExamType),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], TeacherGeneratePaperDto.prototype, "examType", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Subject selection',
        enum: SubjectShortCode,
        example: SubjectShortCode.PHYSICS,
    }),
    (0, class_validator_1.IsEnum)(SubjectShortCode),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], TeacherGeneratePaperDto.prototype, "subject", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Difficulty level mode',
        enum: DifficultyMode,
        example: DifficultyMode.AUTO,
    }),
    (0, class_validator_1.IsEnum)(DifficultyMode),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], TeacherGeneratePaperDto.prototype, "difficultyMode", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Custom difficulty configuration (required if difficultyMode is custom)',
        type: CustomDifficultyConfig,
    }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", CustomDifficultyConfig)
], TeacherGeneratePaperDto.prototype, "customDifficulty", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Number of questions',
        minimum: 1,
        maximum: 200,
        example: 50,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    (0, class_validator_1.Max)(200),
    __metadata("design:type", Number)
], TeacherGeneratePaperDto.prototype, "numberOfQuestions", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Total marks for the paper',
        minimum: 1,
        example: 100,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], TeacherGeneratePaperDto.prototype, "totalMarks", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Duration in minutes',
        minimum: 1,
        example: 180,
    }),
    (0, class_validator_1.IsNumber)(),
    (0, class_validator_1.Min)(1),
    __metadata("design:type", Number)
], TeacherGeneratePaperDto.prototype, "duration", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Include answers in the generated paper',
        example: true,
    }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], TeacherGeneratePaperDto.prototype, "includeAnswers", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Additional instructions for the paper',
        example: 'Read all questions carefully before answering',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TeacherGeneratePaperDto.prototype, "instructions", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Topic ID for specific topic filtering (optional)',
        example: '60d21b4667d0d8992e610c86',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], TeacherGeneratePaperDto.prototype, "topicId", void 0);
//# sourceMappingURL=teacher-generate-paper.dto.js.map