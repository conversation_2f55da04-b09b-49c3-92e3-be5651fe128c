"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AnalyticsCacheService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsCacheService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
let AnalyticsCacheService = AnalyticsCacheService_1 = class AnalyticsCacheService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(AnalyticsCacheService_1.name);
        this.cache = new Map();
        const configTtl = this.configService.get('CACHE_TTL');
        this.ttl = configTtl ? configTtl * 1000 : 3600000;
        this.logger.log(`Analytics cache initialized with TTL: ${this.ttl}ms`);
    }
    get(key) {
        const cached = this.cache.get(key);
        if (!cached) {
            return null;
        }
        const now = Date.now();
        if (now - cached.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }
        return cached.data;
    }
    set(key, data) {
        this.cache.set(key, {
            data,
            timestamp: Date.now(),
        });
    }
    delete(key) {
        this.cache.delete(key);
    }
    clear() {
        this.cache.clear();
        this.logger.log('Analytics cache cleared');
    }
    async getOrCompute(key, fn) {
        const cached = this.get(key);
        if (cached !== null) {
            return cached;
        }
        const computed = await fn();
        this.set(key, computed);
        return computed;
    }
};
exports.AnalyticsCacheService = AnalyticsCacheService;
exports.AnalyticsCacheService = AnalyticsCacheService = AnalyticsCacheService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], AnalyticsCacheService);
//# sourceMappingURL=analytics-cache.service.js.map