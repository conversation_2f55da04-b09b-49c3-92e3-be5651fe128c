"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsersController = void 0;
const common_1 = require("@nestjs/common");
const users_service_1 = require("./users.service");
const user_schema_1 = require("../schema/user.schema");
const assign_role_dto_1 = require("./dto/assign-role.dto");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const roles_guard_1 = require("../auth/guards/roles.guard");
const roles_decorator_1 = require("../auth/guards/roles.decorator");
const current_user_decorator_1 = require("../auth/decorators/current-user.decorator");
const swagger_1 = require("@nestjs/swagger");
let UsersController = class UsersController {
    constructor(usersService) {
        this.usersService = usersService;
    }
    async getCurrentUser(user) {
        const userId = user._id || user.userId;
        const userDoc = await this.usersService.findOne(userId);
        if (user.collegeId && !userDoc.collegeId) {
            await this.usersService.updateCollegeId(userId, user.collegeId);
            const updatedUser = await this.usersService.findOne(userId);
            const userResponse = this.transformUserResponse(updatedUser);
            return userResponse;
        }
        const userResponse = this.transformUserResponse(userDoc);
        return userResponse;
    }
    transformUserResponse(userDoc) {
        const userObj = userDoc.toObject();
        if (userObj.collegeId) {
            userObj.college = userObj.collegeId;
            delete userObj.collegeId;
        }
        return userObj;
    }
    async findAll() {
        return this.usersService.findAll();
    }
    async assignRole(id, assignRoleDto) {
        return this.usersService.assignRole(id, assignRoleDto);
    }
    async findByCollege(collegeId) {
        return this.usersService.findByCollege(collegeId);
    }
};
exports.UsersController = UsersController;
__decorate([
    (0, common_1.Get)('me'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get current user',
        description: 'Returns the currently authenticated user',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns the current user',
        type: user_schema_1.User,
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    __param(0, (0, current_user_decorator_1.CurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "getCurrentUser", null);
__decorate([
    (0, common_1.Get)(),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get all users',
        description: 'Returns all users (super admin only)',
    }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Returns all users', type: [user_schema_1.User] }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findAll", null);
__decorate([
    (0, common_1.Post)(':id/assign-role'),
    (0, roles_decorator_1.Roles)('superAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Assign role to user',
        description: 'Assigns a role to a user (super admin only)',
    }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'User ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Role assigned successfully',
        type: user_schema_1.User,
    }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Bad request - Invalid input data' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'User not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, assign_role_dto_1.AssignRoleDto]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "assignRole", null);
__decorate([
    (0, common_1.Get)('colleges/:collegeId/users'),
    (0, roles_decorator_1.Roles)('superAdmin', 'collegeAdmin'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get users by college',
        description: 'Returns all users associated with a college',
    }),
    (0, swagger_1.ApiParam)({ name: 'collegeId', description: 'College ID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Returns users associated with the college',
        type: [user_schema_1.User],
    }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Unauthorized - Invalid token' }),
    (0, swagger_1.ApiResponse)({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
    }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'College not found' }),
    __param(0, (0, common_1.Param)('collegeId')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UsersController.prototype, "findByCollege", null);
exports.UsersController = UsersController = __decorate([
    (0, swagger_1.ApiTags)('Users'),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Controller)('users'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, roles_guard_1.RolesGuard),
    __metadata("design:paramtypes", [users_service_1.UsersService])
], UsersController);
//# sourceMappingURL=users.controller.js.map