"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommonModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const services_1 = require("./services");
const download_schema_1 = require("../schema/download.schema");
const user_activity_schema_1 = require("../schema/user-activity.schema");
const question_paper_schema_1 = require("../schema/question-paper.schema");
let CommonModule = class CommonModule {
};
exports.CommonModule = CommonModule;
exports.CommonModule = CommonModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: download_schema_1.Download.name, schema: download_schema_1.DownloadSchema },
                { name: user_activity_schema_1.UserActivity.name, schema: user_activity_schema_1.UserActivitySchema },
                { name: question_paper_schema_1.QuestionPaper.name, schema: question_paper_schema_1.QuestionPaperSchema },
            ]),
        ],
        providers: [services_1.AnalyticsCacheService, services_1.TrackingService],
        exports: [services_1.AnalyticsCacheService, services_1.TrackingService],
    })
], CommonModule);
//# sourceMappingURL=common.module.js.map