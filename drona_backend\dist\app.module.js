"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const app_controller_1 = require("./app.controller");
const app_service_1 = require("./app.service");
const auth_module_1 = require("./auth/auth.module");
const users_module_1 = require("./users/users.module");
const colleges_module_1 = require("./colleges/colleges.module");
const teachers_module_1 = require("./teachers/teachers.module");
const questions_module_1 = require("./questions/questions.module");
const question_papers_module_1 = require("./question-papers/question-papers.module");
const subjects_module_1 = require("./subjects/subjects.module");
const topics_module_1 = require("./topics/topics.module");
const analytics_module_1 = require("./analytics/college/analytics.module");
const analytics_module_2 = require("./analytics/super-admin/analytics.module");
const middleware_1 = require("./common/middleware");
const common_module_1 = require("./common/common.module");
let AppModule = class AppModule {
    configure(consumer) {
        consumer
            .apply(middleware_1.RateLimiterMiddleware)
            .forRoutes({ path: '*', method: common_1.RequestMethod.ALL });
    }
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
            }),
            mongoose_1.MongooseModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => {
                    const uri = configService.get('MONGODB_URI');
                    return {
                        uri,
                        dbName: 'Medicos',
                        serverSelectionTimeoutMS: 5000,
                        socketTimeoutMS: 45000,
                    };
                },
                inject: [config_1.ConfigService],
            }),
            common_module_1.CommonModule,
            auth_module_1.AuthModule,
            users_module_1.UsersModule,
            colleges_module_1.CollegesModule,
            teachers_module_1.TeachersModule,
            questions_module_1.QuestionsModule,
            question_papers_module_1.QuestionPapersModule,
            subjects_module_1.SubjectsModule,
            topics_module_1.TopicsModule,
            analytics_module_1.AnalyticsModule,
            analytics_module_2.AnalyticsModule,
        ],
        controllers: [app_controller_1.AppController],
        providers: [app_service_1.AppService],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map