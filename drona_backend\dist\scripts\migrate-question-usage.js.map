{"version": 3, "file": "migrate-question-usage.js", "sourceRoot": "", "sources": ["../../src/scripts/migrate-question-usage.ts"], "names": [], "mappings": ";;AAuHS,oDAAoB;AAvH7B,uCAA2C;AAC3C,8CAA0C;AAC1C,qFAAgF;AAEhF,+CAAiD;AACjD,2EAAgE;AAChE,2CAAwC;AAOxC,KAAK,UAAU,oBAAoB;IACjC,MAAM,MAAM,GAAG,IAAI,eAAM,CAAC,wBAAwB,CAAC,CAAC;IAEpD,IAAI,CAAC;QAEH,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,wBAAwB,CAAC,sBAAS,CAAC,CAAC;QAGlE,MAAM,oBAAoB,GAAG,GAAG,CAAC,GAAG,CAAC,6CAAoB,CAAC,CAAC;QAC3D,MAAM,kBAAkB,GAAG,GAAG,CAAC,GAAG,CAChC,IAAA,wBAAa,EAAC,qCAAa,CAAC,IAAI,CAAC,CAClC,CAAC;QAEF,MAAM,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAGnD,MAAM,cAAc,GAAG,MAAM,kBAAkB;aAC5C,IAAI,CAAC;YACJ,IAAI,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE;YACtB,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE;YAChD,SAAS,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;SAC7B,CAAC;aACD,QAAQ,CAAC,WAAW,CAAC;aACrB,IAAI,EAAE,CAAC;QAEV,MAAM,CAAC,GAAG,CAAC,SAAS,cAAc,CAAC,MAAM,6BAA6B,CAAC,CAAC;QAExE,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,YAAY,GAAG,CAAC,CAAC;QAGrB,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,IACE,CAAC,KAAK,CAAC,SAAS;oBAChB,CAAC,KAAK,CAAC,SAAS;oBAChB,KAAK,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAC5B,CAAC;oBACD,MAAM,CAAC,IAAI,CACT,kBAAkB,KAAK,CAAC,GAAG,kCAAkC,CAC9D,CAAC;oBACF,SAAS;gBACX,CAAC;gBAGD,MAAM,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAa,EAAE,EAAE,CAAC,CAAC;oBACxD,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE;oBACrC,UAAU,EAAE,QAAQ,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACnC,eAAe,EAAE,KAAK,CAAC,GAAG,CAAC,QAAQ,EAAE;oBACrC,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,QAAQ,EAAE;oBACpC,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,EAAE,QAAQ,EAAE;oBACxF,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,QAAQ,CAAC,OAAO,EAAE,QAAQ,EAAE;oBAClE,QAAQ,EAAE;wBACR,QAAQ,EAAE,KAAK,CAAC,QAAQ;wBACxB,UAAU,EAAE,QAAQ,CAAC,UAAU;wBAC/B,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,CAAC;wBAC1B,QAAQ,EAAE,IAAI;qBACf;iBACF,CAAC,CAAC,CAAC;gBAGJ,MAAM,MAAM,GACV,MAAM,oBAAoB,CAAC,2BAA2B,CAAC,SAAS,CAAC,CAAC;gBAEpE,cAAc,EAAE,CAAC;gBACjB,aAAa,IAAI,MAAM,CAAC,QAAQ,CAAC;gBACjC,YAAY,IAAI,MAAM,CAAC,OAAO,CAAC;gBAE/B,IAAI,cAAc,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC;oBAC9B,MAAM,CAAC,GAAG,CACR,aAAa,cAAc,IAAI,cAAc,CAAC,MAAM,YAAY,CACjE,CAAC;gBACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,MAAM,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;QAChD,MAAM,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;QACvB,MAAM,CAAC,GAAG,CAAC,gCAAgC,cAAc,EAAE,CAAC,CAAC;QAC7D,MAAM,CAAC,GAAG,CAAC,qCAAqC,aAAa,EAAE,CAAC,CAAC;QACjE,MAAM,CAAC,GAAG,CAAC,gCAAgC,YAAY,EAAE,CAAC,CAAC;QAG3D,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,CAAC,KAAK,CAAC,qBAAqB,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC;QAChE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAGD,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,oBAAoB,EAAE;SACnB,IAAI,CAAC,GAAG,EAAE;QACT,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC;SACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;QACf,OAAO,CAAC,KAAK,CAAC,mBAAmB,EAAE,KAAK,CAAC,CAAC;QAC1C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC,CAAC,CAAC;AACP,CAAC"}